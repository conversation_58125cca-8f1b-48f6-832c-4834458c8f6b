/**
 * Auth Form Component
 * Reusable form component for authentication (sign in/sign up)
 * Consolidates the repetitive auth form patterns
 */

import React from 'react';
import { FormField } from '@/components/ui/form-field';
import { FormSection, FormActions } from '@/components/ui/form-section';
import { But<PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Mail } from 'lucide-react';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface AuthFormData {
  email: string;
  password: string;
  fullName?: string;
  confirmPassword?: string;
}

interface AuthFormProps extends BaseComponentProps {
  /** Form mode */
  mode: 'signin' | 'signup';
  /** Form data */
  data: AuthFormData;
  /** Form data change handler */
  onChange: (data: AuthFormData) => void;
  /** Form submission handler */
  onSubmit: () => void;
  /** Google sign in handler */
  onGoogleSignIn?: () => void;
  /** Mode toggle handler */
  onModeToggle: () => void;
  /** Whether the form is in loading state */
  isLoading?: boolean;
  /** Form validation errors */
  errors?: Partial<Record<keyof AuthFormData, string>>;
  /** Whether to show Google sign in option */
  showGoogleSignIn?: boolean;
}

/**
 * AuthForm component with consistent styling and validation
 */
export const AuthForm: React.FC<AuthFormProps> = ({
  mode,
  data,
  onChange,
  onSubmit,
  onGoogleSignIn,
  onModeToggle,
  isLoading = false,
  errors = {},
  showGoogleSignIn = true,
  className,
  testId,
  ...props
}) => {
  const isSignUp = mode === 'signup';

  const handleFieldChange = (field: keyof AuthFormData) => (value: string | number) => {
    onChange({
      ...data,
      [field]: value.toString(),
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <div className={cn('space-y-4', className)} data-testid={testId} {...props}>
      {/* Google Sign In */}
      {showGoogleSignIn && onGoogleSignIn && (
        <>
          <Button
            variant="outline"
            onClick={onGoogleSignIn}
            disabled={isLoading}
            className="w-full border-amber-200 hover:bg-amber-50"
            type="button"
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continue with Google
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
            </div>
          </div>
        </>
      )}

      {/* Email/Password Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <FormSection layout="vertical" gap="md">
          {/* Full Name (Sign Up Only) */}
          {isSignUp && (
            <FormField
              label="Full Name"
              type="text"
              placeholder="Enter your full name"
              value={data.fullName || ''}
              onChange={handleFieldChange('fullName')}
              required={isSignUp}
              disabled={isLoading}
              error={errors.fullName}
              testId={testId ? `${testId}-fullname` : undefined}
            />
          )}

          {/* Email */}
          <FormField
            label="Email"
            type="email"
            placeholder="Enter your email"
            value={data.email}
            onChange={handleFieldChange('email')}
            required
            disabled={isLoading}
            error={errors.email}
            icon={<Mail className="h-4 w-4" />}
            testId={testId ? `${testId}-email` : undefined}
          />

          {/* Password */}
          <FormField
            label="Password"
            type="password"
            placeholder="Enter your password"
            value={data.password}
            onChange={handleFieldChange('password')}
            required
            disabled={isLoading}
            error={errors.password}
            showPasswordToggle
            testId={testId ? `${testId}-password` : undefined}
          />

          {/* Confirm Password (Sign Up Only) */}
          {isSignUp && (
            <FormField
              label="Confirm Password"
              type="password"
              placeholder="Confirm your password"
              value={data.confirmPassword || ''}
              onChange={handleFieldChange('confirmPassword')}
              required={isSignUp}
              disabled={isLoading}
              error={errors.confirmPassword}
              showPasswordToggle
              testId={testId ? `${testId}-confirm-password` : undefined}
            />
          )}
        </FormSection>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-amber-500 hover:bg-amber-600 text-white"
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
              {isSignUp ? 'Creating account...' : 'Signing in...'}
            </div>
          ) : (
            isSignUp ? 'Create account' : 'Sign in'
          )}
        </Button>
      </form>

      {/* Mode Toggle */}
      <div className="text-center">
        <Button
          variant="link"
          onClick={onModeToggle}
          className="text-amber-600 hover:text-amber-700"
          type="button"
        >
          {isSignUp 
            ? 'Already have an account? Sign in' 
            : "Don't have an account? Sign up"
          }
        </Button>
      </div>
    </div>
  );
};

/**
 * Hook for managing auth form state and validation
 */
export const useAuthForm = (initialMode: 'signin' | 'signup' = 'signin') => {
  const [mode, setMode] = React.useState<'signin' | 'signup'>(initialMode);
  const [data, setData] = React.useState<AuthFormData>({
    email: '',
    password: '',
    fullName: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = React.useState<Partial<Record<keyof AuthFormData, string>>>({});

  const validate = (): boolean => {
    const newErrors: Partial<Record<keyof AuthFormData, string>> = {};

    if (!data.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(data.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!data.password) {
      newErrors.password = 'Password is required';
    } else if (data.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (mode === 'signup') {
      if (!data.fullName?.trim()) {
        newErrors.fullName = 'Full name is required';
      }

      if (!data.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (data.password !== data.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const toggleMode = () => {
    setMode(mode === 'signin' ? 'signup' : 'signin');
    setErrors({});
  };

  const reset = () => {
    setData({
      email: '',
      password: '',
      fullName: '',
      confirmPassword: '',
    });
    setErrors({});
  };

  return {
    mode,
    setMode,
    data,
    setData,
    errors,
    setErrors,
    validate,
    toggleMode,
    reset,
  };
};
