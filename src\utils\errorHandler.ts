/**
 * Unified Error Handling System
 * Centralized error handling with user-friendly messages and retry mechanisms
 */

import { toast } from 'sonner';
import { ApiError, AuthError } from '@/types';

/**
 * Error severity levels
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Error context for better debugging and user experience
 */
export interface ErrorContext {
  /** Component or service where error occurred */
  source: string;
  /** User action that triggered the error */
  action: string;
  /** Additional context data */
  metadata?: Record<string, any>;
  /** User ID for error tracking */
  userId?: string;
}

/**
 * Enhanced error interface with context
 */
export interface EnhancedError {
  /** Original error */
  error: ApiError | AuthError | Error;
  /** Error context */
  context: ErrorContext;
  /** Error severity */
  severity: ErrorSeverity;
  /** Timestamp when error occurred */
  timestamp: string;
  /** Unique error ID for tracking */
  errorId: string;
}

/**
 * Error handling configuration
 */
interface ErrorHandlingConfig {
  /** Whether to show toast notifications */
  showToast: boolean;
  /** Whether to log errors to console */
  logToConsole: boolean;
  /** Whether to report errors to external service */
  reportToService: boolean;
  /** Custom error message override */
  customMessage?: string;
  /** Retry configuration */
  retry?: {
    enabled: boolean;
    maxAttempts: number;
    onRetry: () => void | Promise<void>;
  };
}

/**
 * Default error handling configuration
 */
const defaultConfig: ErrorHandlingConfig = {
  showToast: true,
  logToConsole: true,
  reportToService: false,
};

/**
 * Determines error severity based on error type and context
 */
const determineErrorSeverity = (
  error: ApiError | AuthError | Error,
  context: ErrorContext
): ErrorSeverity => {
  // Critical errors that prevent core functionality
  if (context.action.includes('auth') || context.action.includes('login')) {
    return 'critical';
  }

  // High severity for data loss or corruption
  if (context.action.includes('delete') || context.action.includes('save')) {
    return 'high';
  }

  // Medium severity for feature failures
  if (context.action.includes('load') || context.action.includes('fetch')) {
    return 'medium';
  }

  // Low severity for non-critical features
  return 'low';
};

/**
 * Generates user-friendly error messages based on error type and context
 */
const generateUserFriendlyMessage = (
  error: ApiError | AuthError | Error,
  context: ErrorContext
): string => {
  // Use existing error message if it's user-friendly
  if ('message' in error && error.message && !error.message.includes('Error:')) {
    return error.message;
  }

  // Generate contextual messages based on action
  switch (context.action) {
    case 'sign_in':
      return 'Unable to sign in. Please check your credentials and try again.';
    case 'sign_up':
      return 'Unable to create account. Please try again or contact support.';
    case 'load_entries':
      return 'Unable to load your journal entries. Please refresh the page.';
    case 'save_entry':
      return 'Unable to save your journal entry. Please try again.';
    case 'delete_entry':
      return 'Unable to delete the entry. Please try again.';
    case 'ai_reflection':
      return 'Unable to generate AI reflection. The entry was saved successfully.';
    case 'load_profile':
      return 'Unable to load your profile. Please refresh the page.';
    case 'update_profile':
      return 'Unable to update your profile. Please try again.';
    default:
      return 'Something went wrong. Please try again.';
  }
};

/**
 * Logs error details for debugging
 */
const logError = (enhancedError: EnhancedError): void => {
  const { error, context, severity, timestamp, errorId } = enhancedError;

  console.group(`🚨 Error [${severity.toUpperCase()}] - ${errorId}`);
  console.log('Timestamp:', timestamp);
  console.log('Source:', context.source);
  console.log('Action:', context.action);
  console.log('User ID:', context.userId || 'Anonymous');
  console.log('Error:', error);
  if (context.metadata) {
    console.log('Metadata:', context.metadata);
  }
  console.groupEnd();
};

/**
 * Reports error to external monitoring service (placeholder)
 */
const reportError = async (enhancedError: EnhancedError): Promise<void> => {
  // TODO: Implement error reporting to service like Sentry, LogRocket, etc.
  // For now, just log that we would report it
  console.log('📊 Would report error to monitoring service:', enhancedError.errorId);
};

/**
 * Main error handling function
 */
export const handleError = async (
  error: ApiError | AuthError | Error,
  context: ErrorContext,
  config: Partial<ErrorHandlingConfig> = {}
): Promise<EnhancedError> => {
  const finalConfig = { ...defaultConfig, ...config };

  // Create enhanced error object
  const enhancedError: EnhancedError = {
    error,
    context,
    severity: determineErrorSeverity(error, context),
    timestamp: new Date().toISOString(),
    errorId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  };

  // Log error if enabled
  if (finalConfig.logToConsole) {
    logError(enhancedError);
  }

  // Show toast notification if enabled
  if (finalConfig.showToast) {
    const message = finalConfig.customMessage || generateUserFriendlyMessage(error, context);

    // Show different toast types based on severity
    switch (enhancedError.severity) {
      case 'critical':
        toast.error(message, {
          duration: 10000, // Longer duration for critical errors
          action: finalConfig.retry
            ? {
                label: 'Retry',
                onClick: finalConfig.retry.onRetry,
              }
            : undefined,
        });
        break;
      case 'high':
        toast.error(message, {
          duration: 7000,
          action: finalConfig.retry
            ? {
                label: 'Retry',
                onClick: finalConfig.retry.onRetry,
              }
            : undefined,
        });
        break;
      case 'medium':
        toast.warning(message, {
          duration: 5000,
        });
        break;
      case 'low':
        toast.info(message, {
          duration: 3000,
        });
        break;
    }
  }

  // Report error to external service if enabled
  if (finalConfig.reportToService) {
    try {
      await reportError(enhancedError);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  return enhancedError;
};

/**
 * Convenience function for handling API errors
 */
export const handleApiError = (
  error: ApiError,
  source: string,
  action: string,
  config?: Partial<ErrorHandlingConfig>
): Promise<EnhancedError> => {
  return handleError(error, { source, action }, config);
};

/**
 * Convenience function for handling authentication errors
 */
export const handleAuthError = (
  error: AuthError,
  source: string,
  action: string,
  config?: Partial<ErrorHandlingConfig>
): Promise<EnhancedError> => {
  return handleError(error, { source, action }, config);
};

/**
 * Convenience function for handling generic errors
 */
export const handleGenericError = (
  error: Error,
  source: string,
  action: string,
  config?: Partial<ErrorHandlingConfig>
): Promise<EnhancedError> => {
  return handleError(error, { source, action }, config);
};

/**
 * Creates a retry-enabled error handler
 */
export const createRetryableErrorHandler = (
  retryFn: () => void | Promise<void>,
  maxAttempts: number = 3
) => {
  return (
    error: ApiError | AuthError | Error,
    context: ErrorContext,
    config?: Partial<ErrorHandlingConfig>
  ) => {
    return handleError(error, context, {
      ...config,
      retry: {
        enabled: true,
        maxAttempts,
        onRetry: retryFn,
      },
    });
  };
};

/**
 * Error boundary helper for React components
 */
export const createErrorBoundaryHandler = (componentName: string) => {
  return (error: Error, errorInfo: any) => {
    handleError(
      error,
      {
        source: componentName,
        action: 'render',
        metadata: { errorInfo },
      },
      {
        showToast: true,
        logToConsole: true,
        reportToService: true,
        customMessage: 'Something went wrong with this component. Please refresh the page.',
      }
    );
  };
};
