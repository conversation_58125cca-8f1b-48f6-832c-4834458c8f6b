-- AmberGlow Conversation System Migration
-- Run this in your Supabase SQL Editor to add conversation functionality

-- First, check if the AI reflection columns already exist (they might from previous migrations)
-- If they don't exist, add them
DO $$
BEGIN
    -- Add AI reflection columns if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'journal_entries' AND column_name = 'ai_reflection') THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_reflection TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_reflection IS 'AI-generated reflection based on the journal entry content and mood (legacy)';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'journal_entries' AND column_name = 'ai_summary') THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_summary TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_summary IS 'AI-generated 1-2 sentence friendly summary';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'journal_entries' AND column_name = 'ai_emotion') THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_emotion TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_emotion IS 'AI-detected main emotion from the journal entry';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'journal_entries' AND column_name = 'ai_encouragement') THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_encouragement TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_encouragement IS 'AI-generated supportive friend response';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'journal_entries' AND column_name = 'ai_reflection_question') THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_reflection_question TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_reflection_question IS 'AI-generated gentle reflection question';
    END IF;
END $$;

-- Create reflection conversations table for conversational AI interactions
CREATE TABLE IF NOT EXISTS public.reflection_conversations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  journal_entry_id UUID NOT NULL REFERENCES public.journal_entries(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create reflection conversation messages table
CREATE TABLE IF NOT EXISTS public.reflection_conversation_messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id UUID NOT NULL REFERENCES public.reflection_conversations(id) ON DELETE CASCADE,
  sender_type TEXT NOT NULL CHECK (sender_type IN ('user', 'ai')),
  message_content TEXT NOT NULL,
  message_type TEXT NOT NULL DEFAULT 'text' CHECK (message_type IN ('text', 'reflection_question', 'follow_up')),
  ai_metadata JSONB, -- Store AI response metadata like emotion, confidence, etc.
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add indexes for performance (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_reflection_conversations_journal_entry ON public.reflection_conversations(journal_entry_id);
CREATE INDEX IF NOT EXISTS idx_reflection_conversations_user ON public.reflection_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_reflection_conversation_messages_conversation ON public.reflection_conversation_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_reflection_conversation_messages_created_at ON public.reflection_conversation_messages(created_at);

-- Add comments to describe the tables and columns
COMMENT ON TABLE public.reflection_conversations IS 'Conversational threads between users and Amber AI for journal entry reflections';
COMMENT ON COLUMN public.reflection_conversations.journal_entry_id IS 'Reference to the journal entry this conversation is about';
COMMENT ON COLUMN public.reflection_conversations.user_id IS 'User who owns this conversation';

COMMENT ON TABLE public.reflection_conversation_messages IS 'Individual messages within reflection conversations';
COMMENT ON COLUMN public.reflection_conversation_messages.conversation_id IS 'Reference to the parent conversation';
COMMENT ON COLUMN public.reflection_conversation_messages.sender_type IS 'Whether message is from user or AI (Amber)';
COMMENT ON COLUMN public.reflection_conversation_messages.message_content IS 'The actual message text content';
COMMENT ON COLUMN public.reflection_conversation_messages.message_type IS 'Type of message: text, reflection_question, or follow_up';
COMMENT ON COLUMN public.reflection_conversation_messages.ai_metadata IS 'JSON metadata for AI messages (emotion, confidence, etc.)';

-- Enable Row Level Security (RLS)
ALTER TABLE public.reflection_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reflection_conversation_messages ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own reflection conversations" ON public.reflection_conversations;
DROP POLICY IF EXISTS "Users can create their own reflection conversations" ON public.reflection_conversations;
DROP POLICY IF EXISTS "Users can update their own reflection conversations" ON public.reflection_conversations;
DROP POLICY IF EXISTS "Users can delete their own reflection conversations" ON public.reflection_conversations;

DROP POLICY IF EXISTS "Users can view messages in their own conversations" ON public.reflection_conversation_messages;
DROP POLICY IF EXISTS "Users can create messages in their own conversations" ON public.reflection_conversation_messages;
DROP POLICY IF EXISTS "Users can update messages in their own conversations" ON public.reflection_conversation_messages;
DROP POLICY IF EXISTS "Users can delete messages in their own conversations" ON public.reflection_conversation_messages;

-- Create RLS policies for reflection_conversations
CREATE POLICY "Users can view their own reflection conversations" ON public.reflection_conversations
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own reflection conversations" ON public.reflection_conversations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reflection conversations" ON public.reflection_conversations
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reflection conversations" ON public.reflection_conversations
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for reflection_conversation_messages
CREATE POLICY "Users can view messages in their own conversations" ON public.reflection_conversation_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.reflection_conversations 
      WHERE id = conversation_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create messages in their own conversations" ON public.reflection_conversation_messages
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.reflection_conversations 
      WHERE id = conversation_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update messages in their own conversations" ON public.reflection_conversation_messages
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.reflection_conversations 
      WHERE id = conversation_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete messages in their own conversations" ON public.reflection_conversation_messages
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.reflection_conversations 
      WHERE id = conversation_id AND user_id = auth.uid()
    )
  );

-- Verify the tables were created successfully
SELECT 'Migration completed successfully!' as status;
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('reflection_conversations', 'reflection_conversation_messages')
ORDER BY table_name, ordinal_position;
