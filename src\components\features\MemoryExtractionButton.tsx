/**
 * Memory Extraction Button Component
 * Button component for extracting memories from journal entries
 */

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Brain, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useMemoryContext } from '@/hooks/useMemoryContext';
import { UserMemory } from '@/types';

/**
 * Props for MemoryExtractionButton component
 */
interface MemoryExtractionButtonProps {
  /** Journal entry data to extract memories from */
  journalEntry: {
    title: string;
    content: string;
    emotion: string;
    moodScore: number;
  };
  /** Button variant */
  variant?: 'default' | 'outline' | 'ghost';
  /** Button size */
  size?: 'sm' | 'default' | 'lg';
  /** Whether to show extracted memories inline */
  showExtractedMemories?: boolean;
  /** Custom className */
  className?: string;
  /** Callback when memories are extracted */
  onMemoriesExtracted?: (memories: UserMemory[]) => void;
}

/**
 * Get category color for memory badges
 */
const getCategoryColor = (category: UserMemory['category']) => {
  const colors = {
    fact: 'bg-blue-100 text-blue-800 border-blue-200',
    emotion: 'bg-red-100 text-red-800 border-red-200',
    event: 'bg-green-100 text-green-800 border-green-200',
    goal: 'bg-purple-100 text-purple-800 border-purple-200',
    preference: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    identity: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  };
  return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
};

/**
 * Memory extraction button component
 */
export const MemoryExtractionButton: React.FC<MemoryExtractionButtonProps> = ({
  journalEntry,
  variant = 'outline',
  size = 'sm',
  showExtractedMemories = true,
  className,
  onMemoriesExtracted,
}) => {
  const { extractFromJournalEntry, isExtracting } = useMemoryContext();
  const [extractedMemories, setExtractedMemories] = useState<UserMemory[]>([]);
  const [extractionStatus, setExtractionStatus] = useState<'idle' | 'success' | 'error'>('idle');

  /**
   * Handle memory extraction
   */
  const handleExtractMemories = async () => {
    try {
      setExtractionStatus('idle');
      setExtractedMemories([]);

      const response = await extractFromJournalEntry(journalEntry);

      if (response.success) {
        setExtractedMemories(response.memories);
        setExtractionStatus('success');
        
        if (response.memories.length > 0) {
          toast.success(`💾 Extracted ${response.memories.length} memories from your journal entry`);
        } else {
          toast.info('💭 No new memories found in this entry');
        }

        // Call callback if provided
        if (onMemoriesExtracted) {
          onMemoriesExtracted(response.memories);
        }
      } else {
        setExtractionStatus('error');
        toast.error(`❌ Memory extraction failed: ${response.error}`);
      }
    } catch (error) {
      setExtractionStatus('error');
      console.error('Memory extraction error:', error);
      toast.error('❌ Failed to extract memories. Please try again.');
    }
  };

  /**
   * Get button icon based on status
   */
  const getButtonIcon = () => {
    if (isExtracting) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    if (extractionStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    if (extractionStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    return <Brain className="h-4 w-4" />;
  };

  /**
   * Get button text based on status
   */
  const getButtonText = () => {
    if (isExtracting) {
      return 'Extracting...';
    }
    if (extractionStatus === 'success') {
      return `💾 Remembered (${extractedMemories.length})`;
    }
    if (extractionStatus === 'error') {
      return 'Try Again';
    }
    return '💾 Remember This';
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Extraction Button */}
      <Button
        variant={variant}
        size={size}
        onClick={handleExtractMemories}
        disabled={isExtracting}
        className="gap-2"
      >
        {getButtonIcon()}
        {getButtonText()}
      </Button>

      {/* Extracted Memories Display */}
      {showExtractedMemories && extractedMemories.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">
            🧠 Memories Extracted:
          </h4>
          <div className="flex flex-wrap gap-2">
            {extractedMemories.map((memory, index) => (
              <Badge
                key={index}
                variant="outline"
                className={`${getCategoryColor(memory.category)} text-xs`}
              >
                <span className="font-medium">{memory.key}:</span>
                <span className="ml-1">{memory.value}</span>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Extraction Status Messages */}
      {extractionStatus === 'success' && extractedMemories.length === 0 && (
        <div className="text-sm text-gray-500 italic">
          💭 No new memories found in this entry
        </div>
      )}
    </div>
  );
};
