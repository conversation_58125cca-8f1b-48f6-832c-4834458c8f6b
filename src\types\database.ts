/**
 * Database Types
 * Bridge between Supabase generated types and our application types
 */

import { Database } from '@/integrations/supabase/types';
import { JournalEntry, EmotionType, MoodScore } from './journal';
import { UserProfile } from './auth';
import { ConversationMessage, ReflectionConversation, AIMessageMetadata } from './conversation';

/**
 * Supabase table types
 */
export type SupabaseJournalEntry = Database['public']['Tables']['journal_entries']['Row'];
export type SupabaseJournalEntryInsert = Database['public']['Tables']['journal_entries']['Insert'];
export type SupabaseJournalEntryUpdate = Database['public']['Tables']['journal_entries']['Update'];

export type SupabaseProfile = Database['public']['Tables']['profiles']['Row'];
export type SupabaseProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type SupabaseProfileUpdate = Database['public']['Tables']['profiles']['Update'];

export type SupabaseReflectionConversation = Database['public']['Tables']['reflection_conversations']['Row'];
export type SupabaseReflectionConversationInsert = Database['public']['Tables']['reflection_conversations']['Insert'];
export type SupabaseReflectionConversationUpdate = Database['public']['Tables']['reflection_conversations']['Update'];

export type SupabaseConversationMessage = Database['public']['Tables']['reflection_conversation_messages']['Row'];
export type SupabaseConversationMessageInsert = Database['public']['Tables']['reflection_conversation_messages']['Insert'];
export type SupabaseConversationMessageUpdate = Database['public']['Tables']['reflection_conversation_messages']['Update'];

/**
 * Type converters between Supabase and application types
 */

/**
 * Convert Supabase journal entry to application journal entry
 */
export function supabaseToJournalEntry(supabaseEntry: SupabaseJournalEntry): JournalEntry {
  return {
    id: supabaseEntry.id,
    user_id: supabaseEntry.user_id,
    title: supabaseEntry.title,
    content: supabaseEntry.content || '',
    emotion: (supabaseEntry.emotion as EmotionType) || 'neutral',
    mood_score: (supabaseEntry.mood_score as MoodScore) || 5,
    ai_summary: supabaseEntry.ai_summary || undefined,
    ai_emotion: supabaseEntry.ai_emotion || undefined,
    ai_encouragement: supabaseEntry.ai_encouragement || undefined,
    ai_reflection_question: supabaseEntry.ai_reflection_question || undefined,
    ai_reflection: supabaseEntry.ai_reflection || undefined,
    created_at: supabaseEntry.created_at,
    updated_at: supabaseEntry.updated_at,
  };
}

/**
 * Convert application journal entry to Supabase insert format
 */
export function journalEntryToSupabaseInsert(
  entry: Omit<JournalEntry, 'id' | 'created_at' | 'updated_at'>
): SupabaseJournalEntryInsert {
  return {
    user_id: entry.user_id,
    title: entry.title,
    content: entry.content,
    emotion: entry.emotion,
    mood_score: entry.mood_score,
    ai_summary: entry.ai_summary || null,
    ai_emotion: entry.ai_emotion || null,
    ai_encouragement: entry.ai_encouragement || null,
    ai_reflection_question: entry.ai_reflection_question || null,
    ai_reflection: entry.ai_reflection || null,
  };
}

/**
 * Convert application journal entry to Supabase update format
 */
export function journalEntryToSupabaseUpdate(
  entry: Partial<Omit<JournalEntry, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
): SupabaseJournalEntryUpdate {
  return {
    title: entry.title,
    content: entry.content,
    emotion: entry.emotion,
    mood_score: entry.mood_score,
    ai_summary: entry.ai_summary || null,
    ai_emotion: entry.ai_emotion || null,
    ai_encouragement: entry.ai_encouragement || null,
    ai_reflection_question: entry.ai_reflection_question || null,
    ai_reflection: entry.ai_reflection || null,
  };
}

/**
 * Convert Supabase profile to application user profile
 */
export function supabaseToUserProfile(supabaseProfile: SupabaseProfile): UserProfile {
  return {
    id: supabaseProfile.id,
    email: supabaseProfile.email || '',
    full_name: supabaseProfile.full_name || undefined,
    avatar_url: supabaseProfile.avatar_url || undefined,
    created_at: supabaseProfile.created_at,
    updated_at: supabaseProfile.updated_at,
  };
}

/**
 * Convert application user profile to Supabase insert format
 */
export function userProfileToSupabaseInsert(
  profile: Omit<UserProfile, 'created_at' | 'updated_at'>
): SupabaseProfileInsert {
  return {
    id: profile.id,
    email: profile.email,
    full_name: profile.full_name || null,
    avatar_url: profile.avatar_url || null,
  };
}

/**
 * Convert application user profile to Supabase update format
 */
export function userProfileToSupabaseUpdate(
  profile: Partial<Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>>
): SupabaseProfileUpdate {
  return {
    email: profile.email,
    full_name: profile.full_name || null,
    avatar_url: profile.avatar_url || null,
  };
}

/**
 * Database query result types
 */
export interface DatabaseQueryResult<T> {
  data: T | null;
  error: Error | null;
  count?: number;
}

/**
 * Convert Supabase conversation to application conversation
 */
export function supabaseToReflectionConversation(
  supabaseConversation: SupabaseReflectionConversation
): ReflectionConversation {
  return {
    id: supabaseConversation.id,
    journal_entry_id: supabaseConversation.journal_entry_id,
    user_id: supabaseConversation.user_id,
    created_at: supabaseConversation.created_at,
    updated_at: supabaseConversation.updated_at,
  };
}

/**
 * Convert application conversation to Supabase insert format
 */
export function reflectionConversationToSupabaseInsert(
  conversation: Omit<ReflectionConversation, 'id' | 'created_at' | 'updated_at'>
): SupabaseReflectionConversationInsert {
  return {
    journal_entry_id: conversation.journal_entry_id,
    user_id: conversation.user_id,
  };
}

/**
 * Convert Supabase conversation message to application message
 */
export function supabaseToConversationMessage(
  supabaseMessage: SupabaseConversationMessage
): ConversationMessage {
  return {
    id: supabaseMessage.id,
    conversation_id: supabaseMessage.conversation_id,
    sender_type: supabaseMessage.sender_type as 'user' | 'ai',
    message_content: supabaseMessage.message_content,
    message_type: supabaseMessage.message_type as 'text' | 'reflection_question' | 'follow_up',
    ai_metadata: supabaseMessage.ai_metadata as AIMessageMetadata | null,
    created_at: supabaseMessage.created_at,
  };
}

/**
 * Convert application conversation message to Supabase insert format
 */
export function conversationMessageToSupabaseInsert(
  message: Omit<ConversationMessage, 'id' | 'created_at'>
): SupabaseConversationMessageInsert {
  return {
    conversation_id: message.conversation_id,
    sender_type: message.sender_type,
    message_content: message.message_content,
    message_type: message.message_type,
    ai_metadata: message.ai_metadata as any,
  };
}

/**
 * Database operation result types
 */
export interface DatabaseOperationResult {
  success: boolean;
  error?: Error;
  data?: any;
}
