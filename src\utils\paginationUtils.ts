/**
 * Pagination Utilities
 * Reusable utilities for pagination logic across the application
 * Consolidates common pagination patterns and calculations
 */

/**
 * Default pagination configuration
 */
export const DEFAULT_PAGINATION_CONFIG = {
  pageSize: 10,
  cacheTime: 10 * 60 * 1000, // 10 minutes
  staleTime: 5 * 60 * 1000, // 5 minutes
  maxPages: 100, // Prevent infinite loading
} as const;

/**
 * Pagination state interface
 */
export interface PaginationState {
  /** Current page number (0-based) */
  currentPage: number;
  /** Number of items per page */
  pageSize: number;
  /** Total number of items */
  totalItems: number;
  /** Total number of pages */
  totalPages: number;
  /** Whether there are more pages to load */
  hasNextPage: boolean;
  /** Whether there are previous pages */
  hasPreviousPage: boolean;
  /** Whether currently loading */
  isLoading: boolean;
  /** Whether there's an error */
  isError: boolean;
}

/**
 * Pagination actions interface
 */
export interface PaginationActions {
  /** Go to next page */
  nextPage: () => void;
  /** Go to previous page */
  previousPage: () => void;
  /** Go to specific page */
  goToPage: (page: number) => void;
  /** Reset to first page */
  reset: () => void;
  /** Load more items (for infinite scroll) */
  loadMore: () => void;
}

/**
 * Pagination options interface
 */
export interface PaginationOptions {
  /** Initial page size */
  pageSize?: number;
  /** Whether to enable infinite scroll */
  infiniteScroll?: boolean;
  /** Cache time in milliseconds */
  cacheTime?: number;
  /** Stale time in milliseconds */
  staleTime?: number;
  /** Maximum number of pages to load */
  maxPages?: number;
}

/**
 * Calculate pagination metadata
 */
export const calculatePaginationMeta = (
  currentPage: number,
  pageSize: number,
  totalItems: number
): Pick<PaginationState, 'totalPages' | 'hasNextPage' | 'hasPreviousPage'> => {
  const totalPages = Math.ceil(totalItems / pageSize);
  
  return {
    totalPages,
    hasNextPage: currentPage < totalPages - 1,
    hasPreviousPage: currentPage > 0,
  };
};

/**
 * Calculate offset for database queries
 */
export const calculateOffset = (page: number, pageSize: number): number => {
  return page * pageSize;
};

/**
 * Calculate range for Supabase queries
 */
export const calculateRange = (page: number, pageSize: number): [number, number] => {
  const start = calculateOffset(page, pageSize);
  const end = start + pageSize - 1;
  return [start, end];
};

/**
 * Validate page number
 */
export const validatePageNumber = (page: number, totalPages: number): number => {
  if (page < 0) return 0;
  if (page >= totalPages) return Math.max(0, totalPages - 1);
  return page;
};

/**
 * Generate page numbers for pagination UI
 */
export const generatePageNumbers = (
  currentPage: number,
  totalPages: number,
  maxVisible: number = 5
): number[] => {
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i);
  }

  const half = Math.floor(maxVisible / 2);
  let start = Math.max(0, currentPage - half);
  const end = Math.min(totalPages - 1, start + maxVisible - 1);

  // Adjust start if we're near the end
  if (end - start < maxVisible - 1) {
    start = Math.max(0, end - maxVisible + 1);
  }

  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
};

/**
 * Create pagination state reducer
 */
export type PaginationAction =
  | { type: 'SET_PAGE'; page: number }
  | { type: 'NEXT_PAGE' }
  | { type: 'PREVIOUS_PAGE' }
  | { type: 'RESET' }
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'SET_ERROR'; error: boolean }
  | { type: 'SET_TOTAL_ITEMS'; totalItems: number };

export const paginationReducer = (
  state: PaginationState,
  action: PaginationAction
): PaginationState => {
  switch (action.type) {
    case 'SET_PAGE': {
      const newPage = validatePageNumber(action.page, state.totalPages);
      const meta = calculatePaginationMeta(newPage, state.pageSize, state.totalItems);
      return {
        ...state,
        currentPage: newPage,
        ...meta,
      };
    }
    
    case 'NEXT_PAGE': {
      if (!state.hasNextPage) return state;
      const newPage = state.currentPage + 1;
      const meta = calculatePaginationMeta(newPage, state.pageSize, state.totalItems);
      return {
        ...state,
        currentPage: newPage,
        ...meta,
      };
    }
    
    case 'PREVIOUS_PAGE': {
      if (!state.hasPreviousPage) return state;
      const newPage = state.currentPage - 1;
      const meta = calculatePaginationMeta(newPage, state.pageSize, state.totalItems);
      return {
        ...state,
        currentPage: newPage,
        ...meta,
      };
    }
    
    case 'RESET': {
      const meta = calculatePaginationMeta(0, state.pageSize, state.totalItems);
      return {
        ...state,
        currentPage: 0,
        ...meta,
      };
    }
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.loading,
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        isError: action.error,
      };
    
    case 'SET_TOTAL_ITEMS': {
      const meta = calculatePaginationMeta(state.currentPage, state.pageSize, action.totalItems);
      return {
        ...state,
        totalItems: action.totalItems,
        ...meta,
      };
    }
    
    default:
      return state;
  }
};

/**
 * Create initial pagination state
 */
export const createInitialPaginationState = (
  options: PaginationOptions = {}
): PaginationState => {
  const pageSize = options.pageSize || DEFAULT_PAGINATION_CONFIG.pageSize;
  
  return {
    currentPage: 0,
    pageSize,
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
    isLoading: false,
    isError: false,
  };
};

/**
 * Format pagination info for display
 */
export const formatPaginationInfo = (state: PaginationState): string => {
  if (state.totalItems === 0) {
    return 'No items found';
  }

  const start = state.currentPage * state.pageSize + 1;
  const end = Math.min((state.currentPage + 1) * state.pageSize, state.totalItems);
  
  return `Showing ${start}-${end} of ${state.totalItems} items`;
};

/**
 * Check if pagination is needed
 */
export const isPaginationNeeded = (totalItems: number, pageSize: number): boolean => {
  return totalItems > pageSize;
};

/**
 * Debounce function for pagination actions
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
