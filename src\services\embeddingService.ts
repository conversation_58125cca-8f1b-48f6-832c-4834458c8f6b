/**
 * Embedding Service Client
 * TypeScript client for communicating with the semantic embedding service
 * Provides semantic embeddings for memory relevance calculations
 */

import { ApiResponse } from '@/types';

/**
 * Configuration for the embedding service
 */
interface EmbeddingServiceConfig {
  /** Base URL of the embedding service */
  baseUrl: string;
  /** Request timeout in milliseconds */
  timeout: number;
  /** Maximum text length for embedding generation */
  maxTextLength: number;
}

/**
 * Response from the embedding service
 */
interface EmbeddingResponse {
  /** 384-dimensional normalized embedding vector */
  embedding: number[];
}

/**
 * Error response from the embedding service
 */
interface EmbeddingError {
  /** Error message */
  error: string;
  /** Optional error details */
  details?: string;
}

/**
 * Default configuration for the embedding service
 */
const DEFAULT_CONFIG: EmbeddingServiceConfig = {
  baseUrl: '/api/embedding', // Use Vite proxy to avoid CORS issues
  timeout: 30000, // 30 seconds
  maxTextLength: 10000, // Match service limit
};

/**
 * Embedding service client class
 */
class EmbeddingServiceClient {
  private config: EmbeddingServiceConfig;
  private isHealthy: boolean = false;
  private lastHealthCheck: number = 0;
  private readonly HEALTH_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

  constructor(config: Partial<EmbeddingServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Check if the embedding service is healthy
   */
  async checkHealth(): Promise<boolean> {
    const now = Date.now();
    
    // Use cached health status if recent
    if (this.isHealthy && (now - this.lastHealthCheck) < this.HEALTH_CHECK_INTERVAL) {
      return true;
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout for health check

      const response = await fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        this.isHealthy = data.status === 'healthy';
        this.lastHealthCheck = now;
        return this.isHealthy;
      }

      this.isHealthy = false;
      return false;
    } catch (error) {
      console.warn('Embedding service health check failed:', error);
      this.isHealthy = false;
      return false;
    }
  }

  /**
   * Generate embedding for the given text
   */
  async generateEmbedding(text: string): Promise<ApiResponse<number[]>> {
    try {
      // Validate input
      if (!text || typeof text !== 'string') {
        return {
          success: false,
          error: {
            message: 'Text is required and must be a string',
            code: 'INVALID_INPUT'
          }
        };
      }

      // Trim and validate text length
      const trimmedText = text.trim();
      if (trimmedText.length === 0) {
        return {
          success: false,
          error: {
            message: 'Text cannot be empty',
            code: 'EMPTY_TEXT'
          }
        };
      }

      if (trimmedText.length > this.config.maxTextLength) {
        return {
          success: false,
          error: {
            message: `Text length exceeds maximum of ${this.config.maxTextLength} characters`,
            code: 'TEXT_TOO_LONG'
          }
        };
      }

      // Check service health first
      const isHealthy = await this.checkHealth();
      if (!isHealthy) {
        return {
          success: false,
          error: {
            message: 'Embedding service is not available',
            code: 'SERVICE_UNAVAILABLE'
          }
        };
      }

      // Make the embedding request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(`${this.config.baseUrl}/embed`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: trimmedText }),
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData: EmbeddingError = await response.json().catch(() => ({
          error: `HTTP ${response.status}: ${response.statusText}`
        }));

        return {
          success: false,
          error: {
            message: errorData.error || 'Failed to generate embedding',
            code: 'EMBEDDING_GENERATION_FAILED'
          }
        };
      }

      const data: EmbeddingResponse = await response.json();

      // Validate response
      if (!data.embedding || !Array.isArray(data.embedding)) {
        return {
          success: false,
          error: {
            message: 'Invalid embedding response format',
            code: 'INVALID_RESPONSE'
          }
        };
      }

      // Validate embedding dimensions (should be 384 for all-MiniLM-L6-v2)
      if (data.embedding.length !== 384) {
        return {
          success: false,
          error: {
            message: `Invalid embedding dimensions: expected 384, got ${data.embedding.length}`,
            code: 'INVALID_DIMENSIONS'
          }
        };
      }

      return {
        success: true,
        data: data.embedding
      };

    } catch (error) {
      console.error('Embedding generation error:', error);
      
      // Handle specific error types
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            error: {
              message: 'Embedding request timed out',
              code: 'REQUEST_TIMEOUT'
            }
          };
        }

        if (error.message.includes('fetch')) {
          return {
            success: false,
            error: {
              message: 'Failed to connect to embedding service',
              code: 'CONNECTION_ERROR'
            }
          };
        }
      }

      return {
        success: false,
        error: {
          message: 'Unexpected error during embedding generation',
          code: 'UNKNOWN_ERROR'
        }
      };
    }
  }

  /**
   * Calculate cosine similarity between two embeddings
   */
  static calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same dimensions');
    }

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      norm1 += embedding1[i] * embedding1[i];
      norm2 += embedding2[i] * embedding2[i];
    }

    // Handle edge cases
    if (norm1 === 0 || norm2 === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * Get service configuration
   */
  getConfig(): EmbeddingServiceConfig {
    return { ...this.config };
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<EmbeddingServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    // Reset health status to force recheck with new config
    this.isHealthy = false;
    this.lastHealthCheck = 0;
  }
}

/**
 * Default embedding service client instance
 */
export const embeddingService = new EmbeddingServiceClient();

/**
 * Generate embedding for text using the default client
 */
export const generateEmbedding = (text: string): Promise<ApiResponse<number[]>> => {
  return embeddingService.generateEmbedding(text);
};

/**
 * Calculate cosine similarity between two embeddings
 */
export const calculateCosineSimilarity = EmbeddingServiceClient.calculateCosineSimilarity;

/**
 * Check if the embedding service is available
 */
export const isEmbeddingServiceAvailable = (): Promise<boolean> => {
  return embeddingService.checkHealth();
};

export { EmbeddingServiceClient };
export type { EmbeddingServiceConfig, EmbeddingResponse, EmbeddingError };
