/**
 * Settings Page
 * User settings and account management page
 */

import React, { useState, useEffect } from 'react';
import PageLayout from '@/components/layout/PageLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';
import { MemoryManagement } from '@/components/features/MemoryManagement';
import { useAuth } from '@/contexts/AuthContext';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useToast } from '@/hooks/use-toast';
import {
  UserSettingsFormData,
  UserSettingsUpdatePayload,
  SettingsLoadingStates,
  SettingsErrorStates,
  SettingsFormErrors,
  BulkDeleteConfirmation,
} from '@/types';
import {
  getUserSettings,
  updateUserSettings,
  deleteAllJournalEntries,
  validateUserSettings,
} from '@/services/settings.service';
import {
  Settings as SettingsIcon,
  User,
  Mail,
  Trash2,
  Save,
  AlertTriangle,
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

/**
 * Settings page component
 */
const Settings: React.FC = () => {
  const { user } = useAuth();
  const { refreshProfile } = useUserProfile();
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState<UserSettingsFormData>({
    full_name: '',
    email: '',
  });

  // Loading states
  const [loading, setLoading] = useState<SettingsLoadingStates>({
    loadingProfile: true,
    updatingProfile: false,
    deletingEntries: false,
  });

  // Error states
  const [errors, setErrors] = useState<SettingsErrorStates>({});

  // Form validation errors
  const [formErrors, setFormErrors] = useState<SettingsFormErrors>({});

  // Bulk delete confirmation
  const [bulkDeleteConfirmation, setBulkDeleteConfirmation] = useState<BulkDeleteConfirmation>({
    confirmationText: '',
    isConfirmed: false,
  });

  /**
   * Load user settings on component mount
   */
  useEffect(() => {
    const loadUserSettings = async () => {
      if (!user?.id) return;

      setLoading(prev => ({ ...prev, loadingProfile: true }));
      setErrors(prev => ({ ...prev, profileError: null }));

      try {
        const response = await getUserSettings(user.id);

        if (response.success && response.data) {
          setFormData(response.data);
        } else {
          setErrors(prev => ({
            ...prev,
            profileError: response.error?.message || 'Failed to load user settings.',
          }));
          toast({
            title: 'Error',
            description: 'Failed to load your settings. Please try refreshing the page.',
            variant: 'destructive',
          });
        }
      } catch (error) {
        setErrors(prev => ({
          ...prev,
          profileError: 'An unexpected error occurred while loading settings.',
        }));
        toast({
          title: 'Error',
          description: 'An unexpected error occurred. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(prev => ({ ...prev, loadingProfile: false }));
      }
    };

    loadUserSettings();
  }, [user?.id, toast]);

  /**
   * Handle hash navigation to memories section
   */
  useEffect(() => {
    const hash = window.location.hash;
    if (hash === '#memories') {
      // Small delay to ensure the component is rendered
      setTimeout(() => {
        const memoriesSection = document.querySelector('[data-section="memories"]');
        if (memoriesSection) {
          memoriesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
    }
  }, []);

  /**
   * Handle form field changes
   */
  const handleFieldChange = (field: keyof UserSettingsFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field error when user starts typing
    if (formErrors[field as keyof SettingsFormErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  /**
   * Handle profile update
   */
  const handleUpdateProfile = async () => {
    if (!user?.id) return;

    const updatePayload: UserSettingsUpdatePayload = {
      full_name: formData.full_name,
    };

    // Validate form
    const validation = validateUserSettings(updatePayload);
    if (!validation.isValid) {
      setFormErrors(validation.errors as SettingsFormErrors);
      return;
    }

    setLoading(prev => ({ ...prev, updatingProfile: true }));
    setErrors(prev => ({ ...prev, updateError: null }));
    setFormErrors({});

    try {
      const response = await updateUserSettings(user.id, updatePayload);

      if (response.success && response.data) {
        setFormData(response.data.profile);
        // Refresh the profile data in the navigation
        await refreshProfile();
        toast({
          title: 'Success',
          description: 'Your profile has been updated successfully.',
        });
      } else {
        setErrors(prev => ({
          ...prev,
          updateError: response.error?.message || 'Failed to update profile.',
        }));
        toast({
          title: 'Error',
          description: response.error?.message || 'Failed to update your profile.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        updateError: 'An unexpected error occurred while updating profile.',
      }));
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, updatingProfile: false }));
    }
  };

  /**
   * Handle bulk delete confirmation text change
   */
  const handleConfirmationTextChange = (value: string) => {
    setBulkDeleteConfirmation(prev => ({
      ...prev,
      confirmationText: value,
      isConfirmed: value.toLowerCase() === 'delete all entries',
    }));
  };

  /**
   * Handle bulk delete of all journal entries
   */
  const handleBulkDelete = async () => {
    if (!user?.id || !bulkDeleteConfirmation.isConfirmed) return;

    setLoading(prev => ({ ...prev, deletingEntries: true }));
    setErrors(prev => ({ ...prev, deleteError: null }));

    try {
      const response = await deleteAllJournalEntries(user.id);

      if (response.success && response.data) {
        toast({
          title: 'Success',
          description: `Successfully deleted ${response.data.deletedCount} journal entries.`,
        });
        // Reset confirmation state
        setBulkDeleteConfirmation({
          confirmationText: '',
          isConfirmed: false,
        });
      } else {
        setErrors(prev => ({
          ...prev,
          deleteError: response.error?.message || 'Failed to delete journal entries.',
        }));
        toast({
          title: 'Error',
          description: response.error?.message || 'Failed to delete your journal entries.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        deleteError: 'An unexpected error occurred while deleting entries.',
      }));
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, deletingEntries: false }));
    }
  };

  // Show loading spinner while loading profile
  if (loading.loadingProfile) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" message="Loading settings..." />
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="max-w-2xl mx-auto space-y-8 fade-in">
          {/* Page Header */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2 mb-4">
              <SettingsIcon className="w-8 h-8 text-amber-600" />
              <h1 className="text-3xl font-bold text-gradient">Settings</h1>
            </div>
            <p className="text-amber-700/80">
              Manage your account settings and preferences
            </p>
          </div>

          {/* Profile Settings Card */}
          <Card className="glass-effect border-amber-200/30 slide-up stagger-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-amber-900">
                <User className="w-5 h-5" />
                Profile Information
              </CardTitle>
              <CardDescription className="text-amber-700/70">
                Update your personal information and display name
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Username Field */}
              <div className="space-y-2">
                <Label htmlFor="full_name" className="text-amber-900 font-medium">
                  Display Name
                </Label>
                <Input
                  id="full_name"
                  type="text"
                  value={formData.full_name}
                  onChange={(e) => handleFieldChange('full_name', e.target.value)}
                  placeholder="Enter your display name"
                  className={`border-amber-200 focus:border-amber-500 focus:ring-amber-500 ${
                    formErrors.full_name ? 'border-red-500' : ''
                  }`}
                  disabled={loading.updatingProfile}
                />
                {formErrors.full_name && (
                  <p className="text-sm text-red-600">{formErrors.full_name}</p>
                )}
              </div>

              {/* Email Field (Read-only) */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-amber-900 font-medium">
                  Email Address
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-amber-600" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    readOnly
                    className="pl-10 bg-amber-50/50 border-amber-200 text-amber-800 cursor-not-allowed"
                  />
                </div>
                <p className="text-xs text-amber-600">
                  Email address cannot be changed from this page
                </p>
              </div>

              {/* Update Button */}
              <div className="flex justify-end">
                <Button
                  onClick={handleUpdateProfile}
                  disabled={loading.updatingProfile || !formData.full_name.trim()}
                  className="bg-amber-500 hover:bg-amber-600 text-white"
                >
                  {loading.updatingProfile ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Update Profile
                    </>
                  )}
                </Button>
              </div>

              {errors.updateError && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{errors.updateError}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Separator className="bg-amber-200/50" />

          {/* Memory Management Section */}
          <div data-section="memories" className="slide-up stagger-2">
            <MemoryManagement />
          </div>

          <Separator className="bg-amber-200/50" />

          {/* Danger Zone Card */}
          <Card className="glass-effect border-red-200/30 slide-up stagger-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-900">
                <AlertTriangle className="w-5 h-5" />
                Danger Zone
              </CardTitle>
              <CardDescription className="text-red-700/70">
                Irreversible actions that will permanently delete your data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h3 className="font-semibold text-red-900 mb-2">
                    Delete All Journal Entries
                  </h3>
                  <p className="text-sm text-red-700 mb-4">
                    This will permanently delete all of your journal entries. This action cannot be undone.
                  </p>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        disabled={loading.deletingEntries}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete All Entries
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="glass-effect">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="text-red-900">
                          Are you absolutely sure?
                        </AlertDialogTitle>
                        <AlertDialogDescription className="text-red-700">
                          This action cannot be undone. This will permanently delete all of your journal entries.
                          <br />
                          <br />
                          To confirm, please type{' '}
                          <span className="font-semibold">"delete all entries"</span> below:
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="my-4">
                        <Input
                          value={bulkDeleteConfirmation.confirmationText}
                          onChange={(e) => handleConfirmationTextChange(e.target.value)}
                          placeholder="Type 'delete all entries' to confirm"
                          className="border-red-200 focus:border-red-500 focus:ring-red-500"
                        />
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel
                          onClick={() => setBulkDeleteConfirmation({ confirmationText: '', isConfirmed: false })}
                        >
                          Cancel
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleBulkDelete}
                          disabled={!bulkDeleteConfirmation.isConfirmed || loading.deletingEntries}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          {loading.deletingEntries ? (
                            <>
                              <LoadingSpinner size="sm" className="mr-2" />
                              Deleting...
                            </>
                          ) : (
                            'Delete All Entries'
                          )}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>

                {errors.deleteError && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">{errors.deleteError}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
      </div>
    </PageLayout>
  );
};

export default Settings;
