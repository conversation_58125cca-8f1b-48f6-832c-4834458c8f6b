/**
 * Local LLM Integration Tests
 * Tests for the migration from Google Gemini to Ollama local LLM
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { generateAIReflection } from '@/services/aiReflectionService';
import { generateAIConversationResponse } from '@/services/aiConversationService';
import { callLocalLLM, generateLocalLLMReflection } from '@/services/localLLMService';
import { AIReflectionInput, AIConversationInput } from '@/types';

// Mock fetch for testing
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('Local LLM Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Local LLM Service', () => {
    it('should call local LLM with correct parameters', async () => {
      const mockResponse = {
        choices: [
          {
            message: {
              content: JSON.stringify({
                summary: 'Test summary',
                emotion: 'happy',
                encouragement: 'Great job!',
                reflection_question: 'How did this make you feel?'
              })
            }
          }
        ]
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const messages = [
        { role: 'system' as const, content: 'You are a helpful assistant' },
        { role: 'user' as const, content: 'Hello' }
      ];

      const result = await callLocalLLM(messages);
      
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:11434/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'llama3',
            messages: messages,
            temperature: 0.7,
            max_tokens: 1000,
            stream: false,
          })
        })
      );

      expect(result).toContain('summary');
    });

    it('should handle local LLM service unavailable', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Connection refused'));

      const messages = [
        { role: 'user' as const, content: 'Hello' }
      ];

      await expect(callLocalLLM(messages)).rejects.toThrow();
    });
  });

  describe('AI Reflection Service', () => {
    it('should generate reflection using local LLM', async () => {
      const mockLLMResponse = {
        summary: 'You seem to be feeling grateful today',
        emotion: 'gratitude',
        encouragement: 'It\'s wonderful to see you appreciating the good things in life',
        reflection_question: 'What specific moments brought you the most joy today?'
      };

      // Mock the local LLM service availability check
      mockFetch
        .mockResolvedValueOnce({ ok: true }) // Service availability check
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: JSON.stringify(mockLLMResponse) } }]
          })
        });

      const input: AIReflectionInput = {
        title: 'A Great Day',
        content: 'Today was amazing! I felt so grateful for everything.',
        emotion: 'joyful',
        moodScore: 9
      };

      const result = await generateAIReflection(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.summary).toBe(mockLLMResponse.summary);
      expect(result.data?.emotion).toBe(mockLLMResponse.emotion);
      expect(result.data?.encouragement).toBe(mockLLMResponse.encouragement);
      expect(result.data?.reflection_question).toBe(mockLLMResponse.reflection_question);
    });

    it('should fallback to mock response when local LLM fails', async () => {
      // Mock service unavailable
      mockFetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const input: AIReflectionInput = {
        title: 'Test Entry',
        content: 'Test content',
        emotion: 'neutral',
        moodScore: 5
      };

      const result = await generateAIReflection(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.summary).toBeDefined();
      expect(result.data?.encouragement).toBeDefined();
    });
  });

  describe('AI Conversation Service', () => {
    it('should generate conversation response using local LLM', async () => {
      const mockConversationResponse = {
        message: 'That sounds really meaningful. Can you tell me more about what made it special?',
        message_type: 'follow_up'
      };

      // Mock the local LLM service availability check and response
      mockFetch
        .mockResolvedValueOnce({ ok: true }) // Service availability check
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: JSON.stringify(mockConversationResponse) } }]
          })
        });

      const input: AIConversationInput = {
        user_message: 'I had a really meaningful conversation with my friend today.',
        conversation_history: [],
        journal_entry: {
          title: 'Great Day',
          content: 'Had an amazing day with friends',
          emotion: 'happy',
          mood_score: 8
        }
      };

      const result = await generateAIConversationResponse(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.message).toBe(mockConversationResponse.message);
      expect(result.data?.message_type).toBe(mockConversationResponse.message_type);
    });

    it('should fallback to mock response when local LLM conversation fails', async () => {
      // Mock service unavailable
      mockFetch.mockRejectedValueOnce(new Error('Service unavailable'));

      const input: AIConversationInput = {
        user_message: 'Hello',
        conversation_history: [],
        journal_entry: {
          title: 'Test',
          content: 'Test content',
          emotion: 'neutral',
          mood_score: 5
        }
      };

      const result = await generateAIConversationResponse(input);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.message).toBeDefined();
      expect(result.data?.message_type).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid JSON responses gracefully', async () => {
      mockFetch
        .mockResolvedValueOnce({ ok: true }) // Service availability check
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            choices: [{ message: { content: 'invalid json' } }]
          })
        });

      const input: AIReflectionInput = {
        title: 'Test',
        content: 'Test content',
        emotion: 'neutral',
        moodScore: 5
      };

      const result = await generateAIReflection(input);

      // Should fallback to mock response
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle network timeouts', async () => {
      mockFetch.mockImplementationOnce(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('timeout')), 100)
        )
      );

      const messages = [
        { role: 'user' as const, content: 'Hello' }
      ];

      await expect(callLocalLLM(messages, { timeout: 50 })).rejects.toThrow();
    });
  });
});
