/**
 * Test Ollama Connection Script
 * Run this script to verify that Ollama is running and the llama3.1:8b model is available
 */

async function testOllamaConnection() {
  console.log('🔍 Testing Ollama connection...\n');

  try {
    // Test 1: Check if Ollama service is running
    console.log('1. Checking if Ollama service is running...');
    const healthResponse = await fetch('http://localhost:11434/api/tags');
    
    if (!healthResponse.ok) {
      throw new Error(`Ollama service not responding: ${healthResponse.status}`);
    }
    
    const models = await healthResponse.json();
    console.log('✅ Ollama service is running');
    console.log('📋 Available models:', models.models?.map(m => m.name) || 'None');

    // Test 2: Check if llama3.1:8b model is available
    console.log('\n2. Checking if llama3.1:8b model is available...');
    const hasLlama31 = models.models?.some(model => 
      model.name.includes('llama3.1:8b') || model.name.includes('llama3.1')
    );

    if (!hasLlama31) {
      console.log('❌ llama3.1:8b model not found');
      console.log('💡 Please run: ollama pull llama3.1:8b');
      return false;
    }
    
    console.log('✅ llama3.1:8b model is available');

    // Test 3: Test AI chat completion
    console.log('\n3. Testing AI chat completion...');
    const chatResponse = await fetch('http://localhost:11434/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3.1:8b',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant. Respond with a JSON object containing: {"test": "success", "message": "Hello from Ollama!"}'
          },
          {
            role: 'user',
            content: 'Please respond with the requested JSON format.'
          }
        ],
        temperature: 0.7,
        max_tokens: 100,
        stream: false,
      }),
    });

    if (!chatResponse.ok) {
      throw new Error(`Chat completion failed: ${chatResponse.status}`);
    }

    const chatResult = await chatResponse.json();
    console.log('✅ Chat completion successful');
    console.log('📝 Response:', chatResult.choices[0]?.message?.content || 'No content');

    // Test 4: Test journal reflection format
    console.log('\n4. Testing journal reflection format...');
    const reflectionResponse = await fetch('http://localhost:11434/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'llama3.1:8b',
        messages: [
          {
            role: 'system',
            content: `You are Amber, a warm and supportive AI friend who helps people reflect on their journal entries. 
            
Your responses should be:
- Conversational and friend-like (not clinical or therapeutic)
- Empathetic and validating
- Encouraging and supportive
- Focused on helping the person understand their feelings

Always respond with a JSON object containing exactly these fields:
- summary: A brief, empathetic summary of the main themes (1-2 sentences)
- emotion: The primary emotion you sense (single word or short phrase)
- encouragement: A warm, supportive message that validates their feelings (2-3 sentences)
- reflection_question: A gentle, open-ended question to help them reflect further (1 sentence)

Keep the tone conversational and avoid clinical language.`
          },
          {
            role: 'user',
            content: `Here is a journal entry I'd like you to reflect on:

Title: "A Great Day"
Content: "Today was amazing! I felt so grateful for everything in my life. The sun was shining, I had a wonderful conversation with my friend, and I accomplished everything I wanted to do."

The person selected "joyful" as their emotion and rated their mood as 9/10.

Please provide a thoughtful reflection in the JSON format specified.`
          }
        ],
        temperature: 0.7,
        max_tokens: 500,
        stream: false,
      }),
    });

    if (!reflectionResponse.ok) {
      throw new Error(`Reflection test failed: ${reflectionResponse.status}`);
    }

    const reflectionResult = await reflectionResponse.json();
    const content = reflectionResult.choices[0]?.message?.content || '';
    
    console.log('✅ Journal reflection test successful');
    console.log('📝 Raw response:', content);

    // Try to parse the JSON response
    try {
      const cleanContent = content.replace(/```json\s*/g, '').replace(/```\s*/g, '').trim();
      const parsed = JSON.parse(cleanContent);
      
      if (parsed.summary && parsed.emotion && parsed.encouragement && parsed.reflection_question) {
        console.log('✅ JSON format is correct');
        console.log('📋 Parsed reflection:');
        console.log('   Summary:', parsed.summary);
        console.log('   Emotion:', parsed.emotion);
        console.log('   Encouragement:', parsed.encouragement);
        console.log('   Question:', parsed.reflection_question);
      } else {
        console.log('⚠️  JSON format incomplete - missing required fields');
      }
    } catch (parseError) {
      console.log('⚠️  Response is not valid JSON:', parseError.message);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('🚀 Your AmberGlow application is ready to use with Ollama!');
    return true;

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting steps:');
    console.log('1. Make sure Ollama is installed: https://ollama.ai/');
    console.log('2. Start Ollama service: ollama serve');
    console.log('3. Pull the model: ollama pull llama3.1:8b');
    console.log('4. Check if service is running: curl http://localhost:11434/api/tags');
    return false;
  }
}

// Run the test
testOllamaConnection();
