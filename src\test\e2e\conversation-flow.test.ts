/**
 * End-to-End Conversation Flow Tests
 * Integration tests for the complete conversation system
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { AuthContext } from '@/contexts/AuthContext';
import { ConversationThread } from '@/components/features/ConversationThread';
import { JournalEntry, ReflectionConversation, ConversationMessage } from '@/types';

// Mock all services
vi.mock('@/services/conversationService');
vi.mock('@/services/aiConversationService');
vi.mock('sonner');

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  aud: 'authenticated',
  role: 'authenticated',
  email_confirmed_at: '2024-01-01T00:00:00Z',
  phone: '',
  confirmed_at: '2024-01-01T00:00:00Z',
  last_sign_in_at: '2024-01-01T00:00:00Z',
  app_metadata: {},
  user_metadata: {},
  identities: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockJournalEntry: JournalEntry = {
  id: 'entry-123',
  user_id: 'user-123',
  title: 'My Great Day',
  content: 'Today was amazing! I spent time with family and felt really grateful.',
  emotion: 'happy',
  mood_score: 8,
  ai_summary: 'A wonderful day filled with gratitude and family time',
  ai_emotion: 'joy',
  ai_encouragement: 'It sounds like you had a truly meaningful day!',
  ai_reflection_question: 'What specific moment with your family brought you the most joy?',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  const authContextValue = {
    user: mockUser,
    loading: false,
    signIn: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
    updateProfile: vi.fn(),
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={authContextValue}>
        {children}
      </AuthContext.Provider>
    </QueryClientProvider>
  );
};

describe('Complete Conversation Flow', () => {
  let mockConversationService: any;
  let mockAIService: any;
  let conversationId: string;
  let messageId: number;

  beforeEach(() => {
    vi.clearAllMocks();
    
    conversationId = 'conv-123';
    messageId = 1;

    // Mock conversation service
    mockConversationService = require('@/services/conversationService');
    mockAIService = require('@/services/aiConversationService');

    // Initially no conversation exists
    mockConversationService.getConversationByJournalEntry.mockResolvedValue({
      success: true,
      data: null,
    });

    mockConversationService.getConversationMessages.mockResolvedValue({
      success: true,
      data: [],
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should complete full conversation flow from start to finish', async () => {
    const user = userEvent.setup();

    // Step 1: Render conversation thread with no existing conversation
    render(
      <TestWrapper>
        <ConversationThread journalEntry={mockJournalEntry} />
      </TestWrapper>
    );

    // Should show conversation starter
    expect(screen.getByText('Ready to reflect with Amber?')).toBeInTheDocument();
    expect(screen.getByText('Start conversation with Amber')).toBeInTheDocument();

    // Step 2: Start a new conversation
    const newConversation: ReflectionConversation = {
      id: conversationId,
      journal_entry_id: 'entry-123',
      user_id: 'user-123',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    mockConversationService.createReflectionConversation.mockResolvedValue({
      success: true,
      data: newConversation,
    });

    // Update the conversation query to return the new conversation
    mockConversationService.getConversationByJournalEntry.mockResolvedValue({
      success: true,
      data: newConversation,
    });

    const startButton = screen.getByText('Start conversation with Amber');
    await user.click(startButton);

    await waitFor(() => {
      expect(mockConversationService.createReflectionConversation).toHaveBeenCalledWith({
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
      });
    });

    // Step 3: Conversation should now show input field
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Share your thoughts with Amber...')).toBeInTheDocument();
    });

    // Step 4: Send first user message
    const firstUserMessage: ConversationMessage = {
      id: `msg-${messageId++}`,
      conversation_id: conversationId,
      sender_type: 'user',
      message_content: 'I want to talk about my day with my family.',
      message_type: 'text',
      ai_metadata: null,
      created_at: '2024-01-01T00:01:00Z',
    };

    mockConversationService.createConversationMessage.mockResolvedValue({
      success: true,
      data: firstUserMessage,
    });

    // Update messages query to include the new message
    mockConversationService.getConversationMessages.mockResolvedValue({
      success: true,
      data: [firstUserMessage],
    });

    const messageInput = screen.getByPlaceholderText('Share your thoughts with Amber...');
    const sendButton = screen.getByRole('button', { name: '' }); // Send button with icon

    await user.type(messageInput, 'I want to talk about my day with my family.');
    await user.click(sendButton);

    await waitFor(() => {
      expect(mockConversationService.createConversationMessage).toHaveBeenCalledWith({
        conversation_id: conversationId,
        sender_type: 'user',
        message_content: 'I want to talk about my day with my family.',
        message_type: 'text',
      });
    });

    // Step 5: Generate AI response
    const aiResponse = {
      message: 'That sounds wonderful! Family time is so precious. What was the highlight of your time together?',
      message_type: 'follow_up',
      metadata: {
        emotion: 'warm',
        confidence: 0.9,
        processingTime: 200,
        modelVersion: 'gemini-pro',
        context: {
          messageHistory: 1,
          journalContext: true,
          moodScore: 8,
        },
      },
      success: true,
    };

    mockAIService.generateAIConversationResponse.mockResolvedValue({
      success: true,
      data: aiResponse,
    });

    const aiMessage: ConversationMessage = {
      id: `msg-${messageId++}`,
      conversation_id: conversationId,
      sender_type: 'ai',
      message_content: aiResponse.message,
      message_type: 'follow_up',
      ai_metadata: aiResponse.metadata,
      created_at: '2024-01-01T00:02:00Z',
    };

    mockConversationService.createConversationMessage.mockResolvedValueOnce({
      success: true,
      data: aiMessage,
    });

    // Update messages to include both messages
    mockConversationService.getConversationMessages.mockResolvedValue({
      success: true,
      data: [firstUserMessage, aiMessage],
    });

    // Wait for AI response to be generated and displayed
    await waitFor(() => {
      expect(mockAIService.generateAIConversationResponse).toHaveBeenCalledWith({
        user_message: 'I want to talk about my day with my family.',
        conversation_history: [firstUserMessage],
        journal_entry: {
          title: 'My Great Day',
          content: 'Today was amazing! I spent time with family and felt really grateful.',
          emotion: 'happy',
          mood_score: 8,
        },
        initial_reflection: {
          summary: 'A wonderful day filled with gratitude and family time',
          emotion: 'joy',
          encouragement: 'It sounds like you had a truly meaningful day!',
          reflection_question: 'What specific moment with your family brought you the most joy?',
        },
      });
    });

    // Step 6: Verify conversation is displayed correctly
    await waitFor(() => {
      expect(screen.getByText('I want to talk about my day with my family.')).toBeInTheDocument();
      expect(screen.getByText(/That sounds wonderful! Family time is so precious/)).toBeInTheDocument();
    });

    // Step 7: Send follow-up message
    const secondUserMessage: ConversationMessage = {
      id: `msg-${messageId++}`,
      conversation_id: conversationId,
      sender_type: 'user',
      message_content: 'The highlight was definitely playing games with my kids. They were laughing so much!',
      message_type: 'text',
      ai_metadata: null,
      created_at: '2024-01-01T00:03:00Z',
    };

    mockConversationService.createConversationMessage.mockResolvedValueOnce({
      success: true,
      data: secondUserMessage,
    });

    // Clear the input and type new message
    await user.clear(messageInput);
    await user.type(messageInput, 'The highlight was definitely playing games with my kids. They were laughing so much!');
    await user.click(sendButton);

    await waitFor(() => {
      expect(mockConversationService.createConversationMessage).toHaveBeenCalledWith({
        conversation_id: conversationId,
        sender_type: 'user',
        message_content: 'The highlight was definitely playing games with my kids. They were laughing so much!',
        message_type: 'text',
      });
    });

    // Step 8: Generate second AI response
    const secondAIResponse = {
      message: 'How beautiful! Children\'s laughter is one of life\'s greatest gifts. It sounds like you created some wonderful memories together. How did seeing their joy make you feel?',
      message_type: 'reflection_question',
      metadata: {
        emotion: 'joyful',
        confidence: 0.95,
        processingTime: 180,
        modelVersion: 'gemini-pro',
        context: {
          messageHistory: 3,
          journalContext: true,
          moodScore: 8,
        },
      },
      success: true,
    };

    mockAIService.generateAIConversationResponse.mockResolvedValue({
      success: true,
      data: secondAIResponse,
    });

    const secondAIMessage: ConversationMessage = {
      id: `msg-${messageId++}`,
      conversation_id: conversationId,
      sender_type: 'ai',
      message_content: secondAIResponse.message,
      message_type: 'reflection_question',
      ai_metadata: secondAIResponse.metadata,
      created_at: '2024-01-01T00:04:00Z',
    };

    mockConversationService.createConversationMessage.mockResolvedValueOnce({
      success: true,
      data: secondAIMessage,
    });

    // Update messages to include all messages
    const allMessages = [firstUserMessage, aiMessage, secondUserMessage, secondAIMessage];
    mockConversationService.getConversationMessages.mockResolvedValue({
      success: true,
      data: allMessages,
    });

    // Wait for second AI response
    await waitFor(() => {
      expect(screen.getByText(/How beautiful! Children's laughter/)).toBeInTheDocument();
    });

    // Step 9: Verify conversation history and context
    expect(mockAIService.generateAIConversationResponse).toHaveBeenLastCalledWith({
      user_message: 'The highlight was definitely playing games with my kids. They were laughing so much!',
      conversation_history: [firstUserMessage, aiMessage, secondUserMessage],
      journal_entry: {
        title: 'My Great Day',
        content: 'Today was amazing! I spent time with family and felt really grateful.',
        emotion: 'happy',
        mood_score: 8,
      },
      initial_reflection: {
        summary: 'A wonderful day filled with gratitude and family time',
        emotion: 'joy',
        encouragement: 'It sounds like you had a truly meaningful day!',
        reflection_question: 'What specific moment with your family brought you the most joy?',
      },
    });

    // Step 10: Verify message count is displayed
    expect(screen.getByText('(4 messages)')).toBeInTheDocument();

    // Step 11: Test conversation deletion
    mockConversationService.deleteConversation.mockResolvedValue({
      success: true,
      data: undefined,
    });

    // Mock window.confirm
    const originalConfirm = window.confirm;
    window.confirm = vi.fn().mockReturnValue(true);

    const deleteButton = screen.getByTitle('Delete conversation');
    await user.click(deleteButton);

    await waitFor(() => {
      expect(mockConversationService.deleteConversation).toHaveBeenCalledWith(conversationId, 'user-123');
    });

    // Restore original confirm
    window.confirm = originalConfirm;

    // Verify the complete flow worked as expected
    expect(mockConversationService.createReflectionConversation).toHaveBeenCalledTimes(1);
    expect(mockConversationService.createConversationMessage).toHaveBeenCalledTimes(4); // 2 user + 2 AI
    expect(mockAIService.generateAIConversationResponse).toHaveBeenCalledTimes(2);
    expect(mockConversationService.deleteConversation).toHaveBeenCalledTimes(1);
  });

  it('should handle errors gracefully throughout the flow', async () => {
    const user = userEvent.setup();

    render(
      <TestWrapper>
        <ConversationThread journalEntry={mockJournalEntry} />
      </TestWrapper>
    );

    // Test conversation creation failure
    mockConversationService.createReflectionConversation.mockResolvedValue({
      success: false,
      error: { message: 'Failed to create conversation', code: 'CREATE_ERROR' },
    });

    const startButton = screen.getByText('Start conversation with Amber');
    await user.click(startButton);

    // Should handle error gracefully (error handling is in the hooks)
    await waitFor(() => {
      expect(mockConversationService.createReflectionConversation).toHaveBeenCalled();
    });

    // Test AI service failure
    mockConversationService.createReflectionConversation.mockResolvedValue({
      success: true,
      data: {
        id: conversationId,
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
    });

    mockAIService.generateAIConversationResponse.mockResolvedValue({
      success: false,
      error: { message: 'AI service unavailable', code: 'AI_ERROR' },
    });

    // The system should still function with fallback responses
    // This tests the resilience of the conversation system
  });
});
