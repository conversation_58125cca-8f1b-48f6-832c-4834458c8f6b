/**
 * Analytics Types
 * TypeScript interfaces for mood analytics and data visualization
 */

import { EmotionType, MoodScore } from './journal';

/**
 * Main analytics data structure containing all analytics information
 */
export interface MoodAnalyticsData {
  /** Overall statistics */
  stats: MoodAnalyticsStats;
  /** Mood trend over time */
  moodTrend: MoodTrendData[];
  /** Emotion distribution */
  emotionDistribution: EmotionDistributionData[];
  /** Daily mood scores for calendar heatmap */
  dailyMoods: DailyMoodData[];
  /** Mood vs AI emotion comparison */
  moodEmotionCorrelation: MoodEmotionCorrelationData[];
  /** Weekly/monthly patterns */
  patterns: MoodPatternData;
}

/**
 * Overall mood analytics statistics
 */
export interface MoodAnalyticsStats {
  /** Total number of journal entries */
  totalEntries: number;
  /** Average mood score across all entries */
  averageMood: number;
  /** Overall mood trend direction */
  moodTrend: 'improving' | 'declining' | 'stable';
  /** Most frequently used emotion */
  mostCommonEmotion: EmotionType;
  /** Mood score range (min and max) */
  moodRange: { min: MoodScore; max: MoodScore };
  /** Current streak of consecutive days with entries */
  streakDays: number;
  /** Number of entries in the current week */
  entriesThisWeek: number;
  /** Number of entries in the current month */
  entriesThisMonth: number;
  /** Percentage change in average mood from previous period */
  moodChangePercentage: number;
}

/**
 * Mood trend data point for time series visualization
 */
export interface MoodTrendData {
  /** Date in ISO string format */
  date: string;
  /** Average mood score for this date */
  averageMood: number;
  /** Number of entries on this date */
  entryCount: number;
  /** Most common emotion on this date */
  dominantEmotion: EmotionType | null;
  /** Moving average for smoothed trend line */
  movingAverage?: number;
}

/**
 * Emotion distribution data for pie/bar charts
 */
export interface EmotionDistributionData {
  /** Emotion type */
  emotion: EmotionType;
  /** Number of entries with this emotion */
  count: number;
  /** Percentage of total entries */
  percentage: number;
  /** Average mood score for this emotion */
  averageMood: number;
  /** Color for chart visualization */
  color: string;
}

/**
 * Daily mood data for calendar heatmap visualization
 */
export interface DailyMoodData {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Mood score for this day (null if no entry) */
  mood: MoodScore | null;
  /** Primary emotion for this day */
  emotion: EmotionType | null;
  /** Whether user made an entry on this day */
  hasEntry: boolean;
  /** Number of entries on this day */
  entryCount: number;
}

/**
 * Correlation between user emotions and AI-detected emotions
 */
export interface MoodEmotionCorrelationData {
  /** User-selected emotion */
  userEmotion: EmotionType;
  /** AI-detected emotion */
  aiEmotion: string;
  /** Associated mood score */
  moodScore: MoodScore;
  /** Number of occurrences */
  count: number;
  /** Correlation strength (0-1) */
  correlationStrength: number;
}

/**
 * Mood patterns analysis (weekly, monthly, time-of-day)
 */
export interface MoodPatternData {
  /** Weekly pattern (0=Sunday, 6=Saturday) */
  weeklyPattern: { dayOfWeek: number; dayName: string; averageMood: number; entryCount: number }[];
  /** Monthly pattern by week */
  monthlyPattern: { week: number; averageMood: number; entryCount: number }[];
  /** Time of day pattern (if timestamps available) */
  timeOfDayPattern: { hour: number; averageMood: number; entryCount: number }[];
  /** Best and worst days */
  bestDay: { dayOfWeek: number; dayName: string; averageMood: number };
  worstDay: { dayOfWeek: number; dayName: string; averageMood: number };
}

/**
 * Time range options for analytics filtering
 */
export type AnalyticsTimeRange = '7d' | '30d' | '90d' | '1y';

/**
 * Analytics filters for customizing data queries
 */
export interface AnalyticsFilters {
  /** Time range for analytics */
  timeRange: AnalyticsTimeRange;
  /** Filter by specific emotions */
  emotions?: EmotionType[];
  /** Filter by mood score range */
  moodRange?: { min: MoodScore; max: MoodScore };
  /** Include AI emotion analysis */
  includeAIAnalysis?: boolean;
}

/**
 * Chart configuration for consistent styling
 */
export interface ChartConfig {
  /** Primary color (amber theme) */
  primaryColor: string;
  /** Secondary color (orange theme) */
  secondaryColor: string;
  /** Accent colors for multi-series charts */
  accentColors: string[];
  /** Grid and axis styling */
  gridColor: string;
  /** Text color */
  textColor: string;
}

/**
 * Analytics export data structure
 */
export interface AnalyticsExportData {
  /** Export metadata */
  metadata: {
    exportDate: string;
    timeRange: AnalyticsTimeRange;
    totalEntries: number;
    userId: string;
  };
  /** Raw analytics data */
  analytics: MoodAnalyticsData;
  /** Summary insights */
  insights: string[];
}

/**
 * Mood insights generated from analytics
 */
export interface MoodInsight {
  /** Insight type */
  type: 'trend' | 'pattern' | 'achievement' | 'recommendation';
  /** Insight title */
  title: string;
  /** Detailed description */
  description: string;
  /** Confidence level (0-1) */
  confidence: number;
  /** Associated data for visualization */
  data?: any;
  /** Icon name for UI display */
  icon: string;
  /** Priority level for display order */
  priority: 'high' | 'medium' | 'low';
}
