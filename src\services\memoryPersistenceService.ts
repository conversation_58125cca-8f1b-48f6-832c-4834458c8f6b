/**
 * Memory Persistence Service
 * Service for managing user memories with Supabase persistence
 * Enhanced with relevance-based memory retrieval
 */

import { supabase } from '@/integrations/supabase/client';
import { UserMemory, MemoryCategory, ApiResponse } from '@/types';
import { evaluateMemoryImportance } from './memoryImportanceService';
import { generateEmbedding } from './embeddingService';

/**
 * Database types for user memories
 */
export interface DatabaseMemory {
  id: string;
  user_id: string;
  key: string;
  value: string;
  category: MemoryCategory;
  importance?: number;
  embedding?: number[] | null; // 384-dimensional semantic embedding vector
  created_at: string;
  updated_at: string;
}

// Note: Memory extraction statistics are not implemented in the current schema
// This can be added later if needed

/**
 * Convert database memory to UserMemory format
 */
const convertDatabaseMemoryToUserMemory = (dbMemory: DatabaseMemory): UserMemory & { created_at?: string; updated_at?: string; embedding?: number[] | null } => ({
  key: dbMemory.key,
  value: dbMemory.value,
  category: dbMemory.category,
  importance: dbMemory.importance || 5, // Default to 5 for backward compatibility
  embedding: dbMemory.embedding || null, // Include embedding if available
  created_at: dbMemory.created_at,
  updated_at: dbMemory.updated_at,
});

/**
 * Memory retrieval options
 */
export interface MemoryRetrievalOptions {
  /** Maximum number of memories to retrieve */
  limit?: number;
  /** Minimum importance score (1-10) */
  minImportance?: number;
  /** Whether to include all memories regardless of importance */
  includeAllMemories?: boolean;
  /** Whether to order by importance first, then by updated_at */
  orderByImportance?: boolean;
  /** Enable debug logging */
  debug?: boolean;
}

/**
 * Relevance-based memory retrieval options
 */
export interface RelevanceRetrievalOptions {
  /** Maximum number of memories to return (default: 10) */
  limit?: number;
  /** Minimum importance score to include (default: 4) */
  minImportance?: number;
  /** Minimum relevance score to include (default: 0.2) */
  minRelevance?: number;
  /** Optional category filter */
  category?: MemoryCategory;
  /** Whether to factor in recency when calculating relevance (default: true) */
  includeRecency?: boolean;
  /** Relevance weight in combined score calculation (default: 0.6) */
  relevanceWeight?: number;
  /** Importance weight in combined score calculation (default: 0.4) */
  importanceWeight?: number;
}

/**
 * Get memories for the current user with importance filtering
 */
export const getUserMemories = async (options: MemoryRetrievalOptions = {}): Promise<ApiResponse<UserMemory[]>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const {
      limit = options.includeAllMemories ? undefined : 10,
      minImportance = 4,
      includeAllMemories = false,
      orderByImportance = true,
    } = options;

    let query = supabase
      .from('memory')
      .select('*')
      .eq('user_id', user.id);

    // Apply importance filtering unless overridden
    if (!includeAllMemories) {
      query = query.gte('importance', minImportance);
    }

    // Apply ordering
    if (orderByImportance) {
      query = query.order('importance', { ascending: false })
                   .order('updated_at', { ascending: false });
    } else {
      query = query.order('updated_at', { ascending: false });
    }

    // Apply limit
    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching user memories:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    const memories = data.map(convertDatabaseMemoryToUserMemory);

    // Only log in development environment and when debug is explicitly enabled
    if (process.env.NODE_ENV === 'development' && options.debug) {
      console.log('🧠 [DEBUG] Retrieved memories:', {
        total: memories.length,
        options
      });
    }

    return {
      success: true,
      data: memories,
    };
  } catch (error) {
    console.error('Unexpected error fetching user memories:', error);
    return {
      success: false,
      error: { message: 'Failed to fetch memories' },
    };
  }
};

/**
 * Calculate similarity between two strings using Levenshtein distance
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  const len1 = str1.length;
  const len2 = str2.length;

  if (len1 === 0 && len2 === 0) return 1.0;
  if (len1 === 0 || len2 === 0) return 0.0;

  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));

  for (let i = 0; i <= len1; i++) matrix[0][i] = i;
  for (let j = 0; j <= len2; j++) matrix[j][0] = j;

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j - 1][i] + 1,     // deletion
        matrix[j][i - 1] + 1,     // insertion
        matrix[j - 1][i - 1] + cost // substitution
      );
    }
  }

  const maxLen = Math.max(len1, len2);
  return (maxLen - matrix[len2][len1]) / maxLen;
};

/**
 * Check if two memories are similar based on key and value patterns
 */
const areMemoriesSimilar = (memory1: UserMemory, memory2: UserMemory): boolean => {
  // Must be same category
  if (memory1.category !== memory2.category) return false;

  // Normalize keys and values for comparison
  const normalizeText = (text: string) => text.toLowerCase().replace(/[_\s-]+/g, ' ').trim();

  const key1 = normalizeText(memory1.key);
  const key2 = normalizeText(memory2.key);
  const value1 = normalizeText(memory1.value);
  const value2 = normalizeText(memory2.value);

  // Check for exact matches after normalization
  if (key1 === key2 || value1 === value2) {
    return true;
  }

  // Check for common patterns in keys (e.g., "pet_injury" vs "pet_health_concern")
  const keyWords1 = key1.split(' ').filter(word => word.length > 2);
  const keyWords2 = key2.split(' ').filter(word => word.length > 2);

  // If keys share significant words, check values for similarity
  const sharedKeyWords = keyWords1.filter(word => keyWords2.includes(word));
  if (sharedKeyWords.length > 0 && sharedKeyWords.length >= Math.min(keyWords1.length, keyWords2.length) * 0.5) {
    // Keys are similar, now check if values are related
    const valueWords1 = value1.split(' ').filter(word => word.length > 2);
    const valueWords2 = value2.split(' ').filter(word => word.length > 2);
    const sharedValueWords = valueWords1.filter(word => valueWords2.includes(word));

    if (sharedValueWords.length > 0) {
      console.log('🧠 [DEBUG] Found similar memories by key+value words:', {
        memory1: { key: memory1.key, value: memory1.value },
        memory2: { key: memory2.key, value: memory2.value },
        sharedKeyWords,
        sharedValueWords
      });
      return true;
    }
  }

  // Check for similar values with different keys (e.g., different emotional states)
  const valueWords1 = value1.split(' ').filter(word => word.length > 3);
  const valueWords2 = value2.split(' ').filter(word => word.length > 3);
  const sharedValueWords = valueWords1.filter(word => valueWords2.includes(word));

  // If values share many significant words, consider them similar
  if (sharedValueWords.length >= 2 && sharedValueWords.length >= Math.min(valueWords1.length, valueWords2.length) * 0.6) {
    console.log('🧠 [DEBUG] Found similar memories by value words:', {
      memory1: { key: memory1.key, value: memory1.value },
      memory2: { key: memory2.key, value: memory2.value },
      sharedValueWords
    });
    return true;
  }

  // Use Levenshtein distance as fallback for very similar strings
  const keySimilarity = calculateSimilarity(key1, key2);
  const valueSimilarity = calculateSimilarity(value1, value2);

  const isKeySimilar = keySimilarity > 0.8;
  const isValueSimilar = valueSimilarity > 0.8;

  if (isKeySimilar || isValueSimilar) {
    console.log('🧠 [DEBUG] Found similar memories by Levenshtein distance:', {
      memory1: { key: memory1.key, value: memory1.value },
      memory2: { key: memory2.key, value: memory2.value },
      keySimilarity,
      valueSimilarity
    });
    return true;
  }

  return false;
};

/**
 * Find similar existing memory for deduplication
 */
const findSimilarMemory = async (newMemory: UserMemory, userId: string): Promise<DatabaseMemory | null> => {
  try {
    // Get all existing memories for the user in the same category
    const { data: existingMemories, error } = await supabase
      .from('memory')
      .select('*')
      .eq('user_id', userId)
      .eq('category', newMemory.category);

    if (error || !existingMemories) {
      console.error('🧠 [DEBUG] Error fetching existing memories:', error);
      return null;
    }

    // Check each existing memory for similarity
    for (const existing of existingMemories) {
      const existingUserMemory = convertDatabaseMemoryToUserMemory(existing);
      if (areMemoriesSimilar(newMemory, existingUserMemory)) {
        console.log('🧠 [DEBUG] Found similar memory:', existing);
        return existing;
      }
    }

    return null;
  } catch (error) {
    console.error('🧠 [DEBUG] Error in findSimilarMemory:', error);
    return null;
  }
};

/**
 * Add a new memory for the current user with deduplication
 */
export const addUserMemory = async (
  memory: UserMemory,
  source: 'manual' | 'journal_entry' | 'conversation' | 'ai_extraction' = 'manual',
  confidenceScore: number = 1.0,
  metadata: Record<string, any> = {}
): Promise<ApiResponse<UserMemory>> => {
  try {
    console.log('🧠 [DEBUG] addUserMemory called with:', { memory, source, confidenceScore, metadata });

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('🧠 [DEBUG] User not authenticated');
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    console.log('🧠 [DEBUG] User authenticated:', user.id);

    // Evaluate memory importance if not already set
    let memoryImportance = memory.importance;
    if (!memoryImportance) {
      console.log('🧠 [DEBUG] Evaluating memory importance...');
      const importanceResult = await evaluateMemoryImportance(memory);
      if (importanceResult.success && importanceResult.data) {
        memoryImportance = importanceResult.data.importance;
        console.log('🧠 [DEBUG] Memory importance evaluated:', memoryImportance);
      } else {
        // Fallback to default importance
        memoryImportance = 5;
        console.log('🧠 [DEBUG] Using default importance:', memoryImportance);
      }
    }

    // Skip saving memories with very low importance (3 or lower)
    if (memoryImportance <= 3) {
      console.log('🧠 [DEBUG] Skipping memory with low importance:', memoryImportance);
      return {
        success: true,
        data: { ...memory, importance: memoryImportance },
        meta: { skipped: true, reason: 'Low importance score' },
      };
    }

    // Check for similar existing memories
    const similarMemory = await findSimilarMemory(memory, user.id);

    if (similarMemory) {
      console.log('🧠 [DEBUG] Similar memory found, updating instead of creating duplicate');

      // Update the existing memory with the new value (keeping the more recent/detailed one)
      const updatedValue = memory.value.length > similarMemory.value.length ? memory.value : similarMemory.value;

      const { data, error } = await supabase
        .from('memory')
        .update({
          value: updatedValue,
          updated_at: new Date().toISOString(),
        })
        .eq('id', similarMemory.id)
        .select()
        .single();

      if (error) {
        console.error('🧠 [DEBUG] Error updating similar memory:', error);
        // Fall through to create new memory if update fails
      } else {
        console.log('🧠 [DEBUG] Successfully updated similar memory:', data);
        return {
          success: true,
          data: convertDatabaseMemoryToUserMemory(data),
        };
      }
    }

    // Generate embedding for the memory
    console.log('🧠 [DEBUG] Generating embedding for memory...');
    const embeddingText = `${memory.key} ${memory.value}`;
    const embeddingResult = await generateEmbedding(embeddingText);

    let embedding: number[] | null = null;
    if (embeddingResult.success) {
      embedding = embeddingResult.data;
      console.log('🧠 [DEBUG] Successfully generated embedding with', embedding.length, 'dimensions');
    } else {
      console.warn('🧠 [DEBUG] Failed to generate embedding:', embeddingResult.error);
      // Continue without embedding - fallback to word matching will be used
    }

    // No similar memory found or update failed, create new memory
    const insertData = {
      user_id: user.id,
      key: memory.key,
      value: memory.value,
      category: memory.category,
      importance: memoryImportance,
      embedding: embedding ? JSON.stringify(embedding) : null, // Store as JSON string
    };

    console.log('🧠 [DEBUG] Inserting new memory data:', { ...insertData, embedding: embedding ? `[${embedding.length} dimensions]` : null });

    const { data, error } = await supabase
      .from('memory')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('🧠 [DEBUG] Supabase error adding user memory:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    console.log('🧠 [DEBUG] Successfully inserted memory:', data);

    return {
      success: true,
      data: convertDatabaseMemoryToUserMemory(data),
    };
  } catch (error) {
    console.error('🧠 [DEBUG] Unexpected error adding user memory:', error);
    return {
      success: false,
      error: { message: 'Failed to add memory' },
    };
  }
};

/**
 * Update an existing memory for the current user
 */
export const updateUserMemory = async (
  oldKey: string,
  updatedMemory: UserMemory
): Promise<ApiResponse<UserMemory>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const { data, error } = await supabase
      .from('memory')
      .update({
        key: updatedMemory.key,
        value: updatedMemory.value,
        category: updatedMemory.category,
      })
      .eq('user_id', user.id)
      .eq('key', oldKey)
      .select()
      .single();

    if (error) {
      console.error('Error updating user memory:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    return {
      success: true,
      data: convertDatabaseMemoryToUserMemory(data),
    };
  } catch (error) {
    console.error('Unexpected error updating user memory:', error);
    return {
      success: false,
      error: { message: 'Failed to update memory' },
    };
  }
};

/**
 * Remove a memory for the current user
 */
export const removeUserMemory = async (key: string): Promise<ApiResponse<void>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const { error } = await supabase
      .from('memory')
      .delete()
      .eq('user_id', user.id)
      .eq('key', key);

    if (error) {
      console.error('Error removing user memory:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    return {
      success: true,
      data: undefined,
    };
  } catch (error) {
    console.error('Unexpected error removing user memory:', error);
    return {
      success: false,
      error: { message: 'Failed to remove memory' },
    };
  }
};

/**
 * Remove duplicate memories for the current user
 */
export const deduplicateUserMemories = async (): Promise<ApiResponse<{ removedCount: number; mergedCount: number }>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    console.log('🧠 [DEBUG] Starting memory deduplication for user:', user.id);

    // Get all memories for the user
    const { data: allMemories, error: fetchError } = await supabase
      .from('memory')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: true }); // Keep older memories as primary

    if (fetchError || !allMemories) {
      console.error('🧠 [DEBUG] Error fetching memories for deduplication:', fetchError);
      return {
        success: false,
        error: { message: fetchError?.message || 'Failed to fetch memories' },
      };
    }

    console.log('🧠 [DEBUG] Found', allMemories.length, 'memories to check for duplicates');

    let removedCount = 0;
    let mergedCount = 0;
    const processedIds = new Set<string>();

    // Group memories by category for more efficient processing
    const memoriesByCategory = allMemories.reduce((acc, memory) => {
      if (!acc[memory.category]) acc[memory.category] = [];
      acc[memory.category].push(memory);
      return acc;
    }, {} as Record<string, DatabaseMemory[]>);

    // Process each category separately
    for (const [category, memories] of Object.entries(memoriesByCategory)) {
      console.log('🧠 [DEBUG] Processing category:', category, 'with', memories.length, 'memories');

      for (let i = 0; i < memories.length; i++) {
        const primaryMemory = memories[i];

        if (processedIds.has(primaryMemory.id)) continue;

        const primaryUserMemory = convertDatabaseMemoryToUserMemory(primaryMemory);
        const duplicates: DatabaseMemory[] = [];

        // Find all similar memories
        for (let j = i + 1; j < memories.length; j++) {
          const candidateMemory = memories[j];

          if (processedIds.has(candidateMemory.id)) continue;

          const candidateUserMemory = convertDatabaseMemoryToUserMemory(candidateMemory);

          if (areMemoriesSimilar(primaryUserMemory, candidateUserMemory)) {
            duplicates.push(candidateMemory);
            processedIds.add(candidateMemory.id);
          }
        }

        if (duplicates.length > 0) {
          console.log('🧠 [DEBUG] Found', duplicates.length, 'duplicates for memory:', primaryMemory.key);

          // Merge values - keep the longest/most detailed value
          const allValues = [primaryMemory.value, ...duplicates.map(d => d.value)];
          const bestValue = allValues.reduce((longest, current) =>
            current.length > longest.length ? current : longest
          );

          // Update the primary memory with the best value
          if (bestValue !== primaryMemory.value) {
            const { error: updateError } = await supabase
              .from('memory')
              .update({
                value: bestValue,
                updated_at: new Date().toISOString(),
              })
              .eq('id', primaryMemory.id);

            if (updateError) {
              console.error('🧠 [DEBUG] Error updating merged memory:', updateError);
            } else {
              mergedCount++;
              console.log('🧠 [DEBUG] Merged memory updated:', primaryMemory.key);
            }
          }

          // Remove duplicate memories
          const duplicateIds = duplicates.map(d => d.id);
          const { error: deleteError } = await supabase
            .from('memory')
            .delete()
            .in('id', duplicateIds);

          if (deleteError) {
            console.error('🧠 [DEBUG] Error removing duplicate memories:', deleteError);
          } else {
            removedCount += duplicates.length;
            console.log('🧠 [DEBUG] Removed', duplicates.length, 'duplicate memories');
          }
        }

        processedIds.add(primaryMemory.id);
      }
    }

    console.log('🧠 [DEBUG] Deduplication complete. Removed:', removedCount, 'Merged:', mergedCount);

    return {
      success: true,
      data: { removedCount, mergedCount },
    };
  } catch (error) {
    console.error('🧠 [DEBUG] Unexpected error during deduplication:', error);
    return {
      success: false,
      error: { message: 'Failed to deduplicate memories' },
    };
  }
};

/**
 * Clear all memories for the current user
 */
export const clearAllUserMemories = async (): Promise<ApiResponse<void>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const { error } = await supabase
      .from('memory')
      .delete()
      .eq('user_id', user.id);

    if (error) {
      console.error('Error clearing user memories:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    return {
      success: true,
      data: undefined,
    };
  } catch (error) {
    console.error('Unexpected error clearing user memories:', error);
    return {
      success: false,
      error: { message: 'Failed to clear memories' },
    };
  }
};

/**
 * Search memories by text content
 */
export const searchUserMemories = async (query: string): Promise<ApiResponse<UserMemory[]>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const { data, error } = await supabase
      .from('memory')
      .select('*')
      .eq('user_id', user.id)
      .textSearch('key,value', query)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching user memories:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    const memories = data.map(convertDatabaseMemoryToUserMemory);
    return {
      success: true,
      data: memories,
    };
  } catch (error) {
    console.error('Unexpected error searching user memories:', error);
    return {
      success: false,
      error: { message: 'Failed to search memories' },
    };
  }
};

/**
 * Get memories by category with importance filtering
 */
export const getUserMemoriesByCategory = async (
  category: MemoryCategory,
  options: MemoryRetrievalOptions = {}
): Promise<ApiResponse<UserMemory[]>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const {
      limit = options.includeAllMemories ? undefined : 10,
      minImportance = 4,
      includeAllMemories = false,
      orderByImportance = true,
    } = options;

    let query = supabase
      .from('memory')
      .select('*')
      .eq('user_id', user.id)
      .eq('category', category);

    // Apply importance filtering unless overridden
    if (!includeAllMemories) {
      query = query.gte('importance', minImportance);
    }

    // Apply ordering
    if (orderByImportance) {
      query = query.order('importance', { ascending: false })
                   .order('updated_at', { ascending: false });
    } else {
      query = query.order('updated_at', { ascending: false });
    }

    // Apply limit
    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching user memories by category:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    const memories = data.map(convertDatabaseMemoryToUserMemory);
    return {
      success: true,
      data: memories,
    };
  } catch (error) {
    console.error('Unexpected error fetching user memories by category:', error);
    return {
      success: false,
      error: { message: 'Failed to fetch memories by category' },
    };
  }
};

/**
 * Get memory extraction statistics (simplified version)
 */
export const getMemoryExtractionStats = async (): Promise<ApiResponse<{
  totalExtractions: number;
  successfulExtractions: number;
  totalMemoriesExtracted: number;
  averageProcessingTime: number;
}>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    // For now, return basic stats based on memory count
    const { data, error } = await supabase
      .from('memory')
      .select('id')
      .eq('user_id', user.id);

    if (error) {
      console.error('Error fetching memory stats:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    const memoryCount = data.length;

    return {
      success: true,
      data: {
        totalExtractions: memoryCount > 0 ? Math.ceil(memoryCount / 2) : 0, // Estimate
        successfulExtractions: memoryCount > 0 ? Math.ceil(memoryCount / 2) : 0, // Estimate
        totalMemoriesExtracted: memoryCount,
        averageProcessingTime: 1500, // Default estimate
      },
    };
  } catch (error) {
    console.error('Unexpected error fetching memory extraction stats:', error);
    return {
      success: false,
      error: { message: 'Failed to fetch extraction stats' },
    };
  }
};

/**
 * Update memory extraction statistics (simplified - just logs for now)
 */
export const updateMemoryExtractionStats = async (
  sourceType: 'journal_entry' | 'conversation',
  totalExtractions: number = 1,
  successfulExtractions: number = 0,
  memoriesExtracted: number = 0,
  processingTimeMs: number = 0
): Promise<ApiResponse<void>> => {
  // This is a simplified version that just logs the stats
  // In a future version, this could store stats in the database
  console.log(' [DEBUG] Memory extraction stats:', {
    sourceType,
    totalExtractions,
    successfulExtractions,
    memoriesExtracted,
    processingTimeMs,
  });

  return {
    success: true,
  };
};

/**
 * Calculate relevance score between query and memory
 * @returns Score between 0-1 where 1 is most relevant
 */
const calculateRelevanceScore = (
  query: string, 
  memory: UserMemory,
  context?: { category?: MemoryCategory, recency?: number }
): number => {
  // Normalize text for comparison
  const normalizeText = (text: string) => text.toLowerCase().trim();
  const queryText = normalizeText(query);
  const memoryKey = normalizeText(memory.key);
  const memoryValue = normalizeText(memory.value);
  
  // Calculate text similarity (TF-IDF style approach)
  const queryWords = queryText.split(/\s+/).filter(w => w.length > 2);
  const memoryWords = [...memoryKey.split(/\s+/), ...memoryValue.split(/\s+/)].filter(w => w.length > 2);
  
  // Count matching words
  let matchCount = 0;
  for (const word of queryWords) {
    if (memoryWords.some(mw => mw.includes(word) || word.includes(mw))) {
      matchCount++;
    }
  }
  
  // Base relevance on word matches
  let relevance = queryWords.length > 0 ? matchCount / queryWords.length : 0;
  
  // Boost relevance for category matches
  if (context?.category && memory.category === context.category) {
    relevance *= 1.25; // 25% boost for matching category
  }
  
  // Apply recency factor if provided
  if (context?.recency !== undefined) {
    // Recency is normalized 0-1 where 1 is most recent
    relevance = (relevance * 0.7) + (context.recency * 0.3);
  }
  
  return Math.min(1, relevance); // Cap at 1.0
};

/**
 * Get memories based on relevance to query and importance
 */
export const getRelevantMemories = async (
  query: string,
  options: RelevanceRetrievalOptions = {}
): Promise<ApiResponse<Array<UserMemory & { relevance: number; combinedScore: number }>>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { message: 'User not authenticated' },
      };
    }

    const {
      limit = 10,
      minImportance = 4,
      minRelevance = 0.2,
      category,
      includeRecency = true,
      relevanceWeight = 0.6,
      importanceWeight = 0.4,
    } = options;

    // First, get all memories that meet the minimum importance threshold
    let dbQuery = supabase
      .from('memory')
      .select('*')
      .eq('user_id', user.id)
      .gte('importance', minImportance);
      
    // Apply category filter if specified
    if (category) {
      dbQuery = dbQuery.eq('category', category);
    }
    
    // Get all potential memories (no limit yet)
    const { data, error } = await dbQuery;

    if (error) {
      console.error('Error fetching memories for relevance calculation:', error);
      return {
        success: false,
        error: { message: error.message },
      };
    }

    // Convert to UserMemory format and calculate relevance scores
    const memories = data.map((dbMemory) => {
      const memory = convertDatabaseMemoryToUserMemory(dbMemory);
      
      // Calculate recency factor if needed (0-1 scale)
      let recency: number | undefined;
      if (includeRecency) {
        const updatedAt = new Date(dbMemory.updated_at);
        const now = new Date();
        const ageInDays = (now.getTime() - updatedAt.getTime()) / (1000 * 60 * 60 * 24);
        recency = Math.max(0, Math.min(1, 1 - (ageInDays / 30))); // Scale: 0 days = 1.0, 30+ days = 0.0
      }
      
      // Calculate relevance score
      const relevance = calculateRelevanceScore(query, memory, { 
        category, 
        recency 
      });
      
      return { ...memory, relevance };
    });

    // Filter by minimum relevance
    const relevantMemories = memories.filter(m => m.relevance >= minRelevance);
    
    // Calculate combined score (importance + relevance)
    const scoredMemories = relevantMemories.map(memory => {
      // Normalize importance to 0-1 scale
      const importanceScore = (memory.importance || 5) / 10;
      
      // Combined score: weighted combination of relevance and importance
      const combinedScore = (memory.relevance * relevanceWeight) + (importanceScore * importanceWeight);
      
      return { ...memory, combinedScore };
    });
    
    // Sort by combined score and take top results
    const sortedMemories = scoredMemories
      .sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, limit);
    
    console.log(' Retrieved relevant memories:', {
      query,
      total: sortedMemories.length,
      options,
      topScores: sortedMemories.slice(0, 3).map(m => ({
        key: m.key,
        relevance: m.relevance,
        importance: m.importance,
        combinedScore: m.combinedScore
      }))
    });

    return {
      success: true,
      data: sortedMemories,
    };
  } catch (error) {
    console.error('Unexpected error fetching relevant memories:', error);
    return {
      success: false,
      error: { message: 'Failed to fetch relevant memories' },
    };
  }
};
