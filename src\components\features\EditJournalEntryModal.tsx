/**
 * Edit Journal Entry Modal Component
 * Modal for editing existing journal entries with AI reflection regeneration
 */

import React, { useState, useEffect } from 'react';
import { FormModal } from '@/components/ui/form-modal';
import { JournalEntryForm, useJournalEntryForm } from '@/components/forms/JournalEntryForm';
import { AmberButton } from '@/components/ui/amber-button';
import { AIReflection } from './AIReflection';
import { Sparkles, Edit3 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { generateAIReflection } from '@/services/aiReflectionService';
import { extractMemoriesFromJournalEntry } from '@/services/memoryExtractionService';
import {
  FormattedJournalEntry,
  AIReflection as AIReflectionType,
} from '@/types';

interface EditJournalEntryModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Function called when entry is successfully updated */
  onSave: (entry: FormattedJournalEntry) => void;
  /** The journal entry to edit */
  entry: FormattedJournalEntry | null;
}

export const EditJournalEntryModal = ({
  isOpen,
  onClose,
  onSave,
  entry,
}: EditJournalEntryModalProps) => {
  const {
    data: formData,
    setData: setFormData,
    errors,
    validate,
    reset,
  } = useJournalEntryForm();

  const [isLoading, setIsLoading] = useState(false);
  const [aiReflection, setAiReflection] = useState<AIReflectionType>({
    summary: '',
    emotion: '',
    encouragement: '',
    reflection_question: '',
    reflection: '',
  });
  const [isGeneratingReflection, setIsGeneratingReflection] = useState(false);
  const [showReflection, setShowReflection] = useState(false);

  // Populate form when entry changes
  useEffect(() => {
    if (entry && isOpen) {
      setFormData({
        title: entry.title,
        content: entry.content,
        emotion: entry.emotion,
        moodScore: entry.mood_score,
      });

      // Set existing AI reflection if available
      if (entry.ai_summary || entry.ai_encouragement || entry.ai_reflection) {
        setAiReflection({
          summary: entry.ai_summary || '',
          emotion: entry.ai_emotion || '',
          encouragement: entry.ai_encouragement || '',
          reflection_question: entry.ai_reflection_question || '',
          reflection: entry.ai_reflection || '',
        });
        setShowReflection(true);
      } else {
        setShowReflection(false);
      }
    }
  }, [entry, isOpen]); // Removed setFormData - React useState setters are stable

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setAiReflection({
        summary: '',
        emotion: '',
        encouragement: '',
        reflection_question: '',
        reflection: '',
      });
      setShowReflection(false);
      setIsGeneratingReflection(false);
      setIsLoading(false);
    }
  }, [isOpen, reset]);

  const handleGenerateReflection = async () => {
    if (!formData.title.trim() || !formData.content.trim() || !formData.emotion) {
      toast.error('Please fill in all fields before generating a reflection');
      return;
    }

    setIsGeneratingReflection(true);
    setShowReflection(true);

    try {
      const reflectionResult = await generateAIReflection({
        title: formData.title.trim(),
        content: formData.content.trim(),
        emotion: formData.emotion,
        moodScore: formData.moodScore,
      });

      if (reflectionResult.success && reflectionResult.data) {
        setAiReflection({
          summary: reflectionResult.data.summary,
          emotion: reflectionResult.data.emotion,
          encouragement: reflectionResult.data.encouragement,
          reflection_question: reflectionResult.data.reflection_question,
          reflection: reflectionResult.data.reflection,
        });
      } else {
        toast.error('Failed to generate reflection. Please try again.');
        setShowReflection(false);
      }
    } catch (error) {
      console.error('Error generating reflection:', error);
      toast.error('Failed to generate reflection. Please try again.');
      setShowReflection(false);
    } finally {
      setIsGeneratingReflection(false);
    }
  };

  const handleSave = () => {
    if (!entry) return;

    if (!validate()) {
      return;
    }

    // Build updated entry from current form state
    const updatedEntry: FormattedJournalEntry = {
      ...entry,
      title: formData.title.trim(),
      content: formData.content.trim(),
      emotion: formData.emotion,
      mood_score: formData.moodScore,
      ai_reflection: aiReflection.reflection || null,
      ai_summary: aiReflection.summary || null,
      ai_emotion: aiReflection.emotion || null,
      ai_encouragement: aiReflection.encouragement || null,
      ai_reflection_question: aiReflection.reflection_question || null,
      updated_at: new Date().toISOString(), // Mark as locally updated
    };

    // Optimistic update - pass fresh data from form state
    onSave(updatedEntry);
    onClose();

    // Automatically extract memories in the background (non-blocking)
    setTimeout(async () => {
      try {
        console.log('🧠 Automatically extracting memories from edited journal entry...');
        const memoryResult = await extractMemoriesFromJournalEntry({
          title: formData.title,
          content: formData.content,
          emotion: formData.emotion,
          moodScore: formData.moodScore,
        });

        if (memoryResult.success && memoryResult.memories.length > 0) {
          console.log(`🧠 Successfully extracted ${memoryResult.memories.length} memories from edited journal entry`);
          toast.success(`💾 Discovered ${memoryResult.memories.length} new memories from your updated entry`, {
            duration: 3000,
          });
        }
      } catch (error) {
        // Memory extraction failure should not affect the user experience
        console.warn('🧠 Automatic memory extraction failed (non-critical):', error);
      }
    }, 100); // Small delay to ensure UI updates first
  };



  if (!entry) return null;

  // AI Reflection section to add to the form
  const aiReflectionSection = (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">AI Reflection</Label>
        <AmberButton
          variant="primary"
          onClick={handleGenerateReflection}
          disabled={
            isGeneratingReflection || isLoading || !formData.title.trim() || !formData.content.trim() || !formData.emotion
          }
          icon={<Sparkles className="w-4 h-4" />}
          isLoading={isGeneratingReflection}
          loadingText="Generating..."
        >
          Regenerate Reflection
        </AmberButton>
      </div>

      {showReflection && (
        <AIReflection
          summary={aiReflection.summary}
          emotion={aiReflection.emotion}
          encouragement={aiReflection.encouragement}
          reflection_question={aiReflection.reflection_question}
          reflection={aiReflection.reflection}
          isGenerating={isGeneratingReflection}
          journalEntry={entry}
          showConversation={true}
          className="border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50"
        />
      )}
    </div>
  );

  return (
    <FormModal
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSave}
      title="Edit Journal Entry"
      description="Update your journal entry and regenerate AI reflection if needed"
      submitText="Update Entry"
      cancelText="Cancel"
      isLoading={isLoading}
      size="xl"
      icon={<Edit3 className="w-4 h-4 text-amber-600" />}
      iconBgColor="bg-amber-100"
      testId="edit-journal-entry-modal"
    >
      <JournalEntryForm
        data={formData}
        onChange={setFormData}
        onSubmit={handleSave}
        onCancel={onClose}
        isLoading={isLoading}
        errors={errors}
        submitText="Update Entry"
        cancelText="Cancel"
        additionalContent={aiReflectionSection}
        hideActions={true}
        testId="edit-journal-entry-form"
      />
    </FormModal>
  );
};
