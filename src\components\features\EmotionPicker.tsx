import React from 'react';
import { Button } from '@/components/ui/button';
import { EmotionType } from '@/types';
import { useStableCallback } from '@/utils/performance.utils';

interface EmotionOption {
  name: EmotionType;
  emoji: string;
  color: string;
}

const emotions: EmotionOption[] = [
  { name: 'grateful', emoji: '🙏', color: 'bg-green-100 hover:bg-green-200 text-green-800' },
  { name: 'joyful', emoji: '😊', color: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-800' },
  { name: 'calm', emoji: '😌', color: 'bg-blue-100 hover:bg-blue-200 text-blue-800' },
  { name: 'excited', emoji: '🤩', color: 'bg-orange-100 hover:bg-orange-200 text-orange-800' },
  { name: 'anxious', emoji: '😰', color: 'bg-purple-100 hover:bg-purple-200 text-purple-800' },
  { name: 'sad', emoji: '😢', color: 'bg-indigo-100 hover:bg-indigo-200 text-indigo-800' },
  { name: 'frustrated', emoji: '😤', color: 'bg-red-100 hover:bg-red-200 text-red-800' },
  { name: 'neutral', emoji: '🤔', color: 'bg-gray-100 hover:bg-gray-200 text-gray-800' },
  { name: 'hopeful', emoji: '🌟', color: 'bg-pink-100 hover:bg-pink-200 text-pink-800' },
  { name: 'overwhelmed', emoji: '😵', color: 'bg-slate-100 hover:bg-slate-200 text-slate-800' },
];

interface EmotionPickerProps {
  selectedEmotion: EmotionType | '';
  onEmotionSelect: (emotion: EmotionType) => void;
}

const EmotionPickerComponent = ({ selectedEmotion, onEmotionSelect }: EmotionPickerProps) => {
  // Performance monitoring - temporarily disabled
  // useRenderCount('EmotionPicker');

  // Stable callback for emotion selection
  const handleEmotionSelect = useStableCallback(
    (emotion: EmotionType) => {
      onEmotionSelect(emotion);
    },
    [onEmotionSelect],
    'EmotionPicker.handleEmotionSelect'
  );

  // Memoize emotion buttons to prevent unnecessary re-renders
  const emotionButtons = React.useMemo(
    () =>
      emotions.map(emotion => (
        <Button
          key={emotion.name}
          type="button"
          variant={selectedEmotion === emotion.name ? 'default' : 'outline'}
          onClick={() => handleEmotionSelect(emotion.name)}
          className={`
          h-auto py-3 px-4 flex flex-col items-center gap-2 transition-all duration-200
          ${
            selectedEmotion === emotion.name
              ? 'bg-amber-500 hover:bg-amber-600 text-white border-amber-500'
              : `${emotion.color} border-2 border-transparent hover:border-amber-300`
          }
        `}
        >
          <span className="text-2xl">{emotion.emoji}</span>
          <span className="text-sm font-medium capitalize">{emotion.name}</span>
        </Button>
      )),
    [selectedEmotion, handleEmotionSelect]
  );

  return <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">{emotionButtons}</div>;
};

// Memoized component
export const EmotionPicker = React.memo(EmotionPickerComponent, (prevProps, nextProps) => {
  return (
    prevProps.selectedEmotion === nextProps.selectedEmotion &&
    prevProps.onEmotionSelect === nextProps.onEmotionSelect
  );
});
