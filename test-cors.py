#!/usr/bin/env python3
"""
Simple test script to verify Flask-CORS is working
"""

from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/test', methods=['GET'])
def test():
    return jsonify({"message": "CORS test successful"})

if __name__ == '__main__':
    print("Starting simple CORS test server on port 5006...")
    app.run(host='0.0.0.0', port=5006, debug=True)
