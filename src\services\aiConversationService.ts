/**
 * AI Conversation Service
 * Handles conversational AI interactions for journal reflection conversations
 * Migrated from Google Gemini to Ollama local LLM integration
 */

import {
  AIConversationInput,
  AIConversationResponse,
  ConversationMessage,
  AIMessageMetadata,
  MessageType,
  ApiResponse,
  AIServiceConfig,
  LLMMessage,
} from '@/types';
import { handleApiError } from '@/utils/errorHandler';
import { getAIConfig } from '@/config/ai.config';
import {
  generateLocalLLMConversation,
  LocalLLMError,
  LocalLLMErrorType
} from './localLLMService';
import { recallRelevantMemories, formatMemoriesForAIContext } from './memoryRecallService';

/**
 * Error types specific to conversation AI
 */
export type ConversationAIErrorType = 
  | 'context_too_long'
  | 'invalid_conversation_history'
  | 'conversation_generation_failed'
  | 'rate_limit_exceeded'
  | 'api_unavailable'
  | 'unknown_error';

/**
 * Conversation AI error interface
 */
export interface ConversationAIError {
  type: ConversationAIErrorType;
  message: string;
  details?: string;
  retryable: boolean;
}

/**
 * Configuration for conversation AI
 */
interface ConversationAIConfig extends AIServiceConfig {
  /** Maximum number of messages to include in context */
  maxContextMessages: number;
  /** Maximum total characters in conversation context */
  maxContextLength: number;
  /** Whether to include journal entry context in every request */
  includeJournalContext: boolean;
}

/**
 * Get default conversation AI configuration
 */
const getConversationAIConfig = (): ConversationAIConfig => {
  const baseConfig = getAIConfig();
  return {
    ...baseConfig,
    maxContextMessages: 10,
    maxContextLength: 4000,
    includeJournalContext: true,
  };
};

/**
 * Check if local LLM service is available for conversations
 */
const isLocalLLMAvailableForConversation = async (config?: ConversationAIConfig): Promise<boolean> => {
  try {
    const aiConfig = config || getConversationAIConfig();
    const endpoint = aiConfig.localLLMEndpoint || 'http://localhost:11434/v1/chat/completions';
    // Use the correct Ollama API endpoint for checking service availability
    const checkEndpoint = endpoint.replace('/v1/chat/completions', '/api/tags');
    const response = await fetch(checkEndpoint, {
      method: 'GET',
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });
    return response.ok;
  } catch (error) {
    console.warn('Local LLM service not available for conversation:', error);
    return false;
  }
};

/**
 * Build conversation context from message history
 */
const buildConversationContext = (
  messages: ConversationMessage[],
  config: ConversationAIConfig
): string => {
  // Sort messages by creation time
  const sortedMessages = [...messages].sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  );

  // Take the most recent messages up to the limit
  const recentMessages = sortedMessages.slice(-config.maxContextMessages);

  // Build context string
  let context = 'Previous conversation:\n';
  let totalLength = context.length;

  for (const message of recentMessages) {
    const messageText = `${message.sender_type === 'user' ? 'User' : 'Amber'}: ${message.message_content}\n`;
    
    if (totalLength + messageText.length > config.maxContextLength) {
      break;
    }
    
    context += messageText;
    totalLength += messageText.length;
  }

  return context;
};

/**
 * Create conversation prompt for Gemini
 */
const createConversationPrompt = (input: AIConversationInput): string => {
  const config = getConversationAIConfig();
  
  let prompt = `You are Amber, a warm and supportive AI friend helping with journal reflection. `;
  prompt += `You have a conversational, friend-like tone and provide thoughtful, empathetic responses. `;
  prompt += `Avoid clinical or therapeutic language - speak like a caring friend would.\n\n`;

  // Include journal entry context if enabled
  if (config.includeJournalContext && input.journal_entry) {
    prompt += `Original journal entry context:\n`;
    prompt += `Title: "${input.journal_entry.title}"\n`;
    prompt += `Content: "${input.journal_entry.content}"\n`;
    prompt += `User's emotion: ${input.journal_entry.emotion}\n`;
    prompt += `User's mood score: ${input.journal_entry.mood_score}/10\n\n`;
  }

  // Include initial reflection if available
  if (input.initial_reflection) {
    prompt += `Your initial reflection:\n`;
    prompt += `Summary: ${input.initial_reflection.summary}\n`;
    prompt += `Emotion detected: ${input.initial_reflection.emotion}\n`;
    prompt += `Encouragement: ${input.initial_reflection.encouragement}\n`;
    prompt += `Question asked: ${input.initial_reflection.reflection_question}\n\n`;
  }

  // Include conversation history
  if (input.conversation_history.length > 0) {
    prompt += buildConversationContext(input.conversation_history, config);
    prompt += '\n';
  }

  // Add the current user message
  prompt += `User's latest message: "${input.user_message}"\n\n`;

  // Instructions for response
  prompt += `Please respond as Amber with a JSON object containing:\n`;
  prompt += `- message: Your warm, supportive response to the user's message\n`;
  prompt += `- message_type: Either "follow_up" for continuing conversation or "reflection_question" if asking a new reflection question\n`;
  prompt += `- metadata: An object with emotion (detected from user's message), confidence (0-1), and any other relevant context\n\n`;
  
  prompt += `Keep your response conversational and supportive. If the user seems to want to explore deeper, `;
  prompt += `ask thoughtful follow-up questions. If they seem satisfied or want to move on, provide `;
  prompt += `encouraging closure. Always validate their feelings and experiences.`;

  return prompt;
};

/**
 * Retry mechanism with exponential backoff
 */
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.warn(`Conversation AI attempt ${attempt} failed, retrying in ${delay}ms:`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw new Error('Max retries exceeded');
};

/**
 * Convert conversation history to LLM message format
 */
const convertConversationToLLMMessages = (input: AIConversationInput): LLMMessage[] => {
  const messages: LLMMessage[] = [];

  // Add conversation history (limit to recent messages to avoid context overflow)
  const recentHistory = input.conversation_history.slice(-10); // Last 10 messages

  for (const msg of recentHistory) {
    if (msg.sender_type === 'user') {
      messages.push({ role: 'user', content: msg.message_content });
    } else if (msg.sender_type === 'ai') {
      messages.push({ role: 'assistant', content: msg.message_content });
    }
  }

  return messages;
};

/**
 * Generate AI conversation response using Local LLM
 */
const generateLocalLLMConversationResponse = async (
  input: AIConversationInput,
  config?: ConversationAIConfig,
  memoryContext?: string
): Promise<AIConversationResponse> => {
  const aiConfig = config || getConversationAIConfig();
  const startTime = Date.now();

  try {
    // Check if local LLM is available
    const isAvailable = await isLocalLLMAvailableForConversation(aiConfig);
    if (!isAvailable) {
      throw new LocalLLMError('service_unavailable', 'Local LLM service is not available for conversation');
    }

    // Recall relevant memories for the user's message
    let enhancedMemoryContext = memoryContext || '';
    try {
      console.log('🧠 [CHAT] Recalling relevant memories for conversation context');
      const memoryRecallResult = await recallRelevantMemories(input.user_message, {
        maxMemories: 6,
        minSimilarity: 0.25,
        minImportance: 3,
        similarityWeight: 0.8,
        includeHighRelevance: true,
        highRelevanceThreshold: 0.7,
      });

      if (memoryRecallResult.success && memoryRecallResult.data.memories.length > 0) {
        const memoryContextString = formatMemoriesForAIContext(memoryRecallResult.data.memories);
        enhancedMemoryContext = memoryContext ? `${memoryContext}${memoryContextString}` : memoryContextString;

        console.log(`🧠 [CHAT] Added ${memoryRecallResult.data.memories.length} relevant memories to conversation context`);
        console.log(`🧠 [CHAT] Memory recall stats: ${memoryRecallResult.data.memoriesWithEmbeddings}/${memoryRecallResult.data.totalConsidered} memories had embeddings`);
      } else {
        console.log('🧠 [CHAT] No relevant memories found for conversation context');
      }
    } catch (memoryError) {
      console.warn('🧠 [CHAT] Memory recall failed (non-critical):', memoryError);
    }

    // Convert conversation history to LLM message format
    const conversationHistory = convertConversationToLLMMessages(input);

    // Generate response using local LLM with enhanced memory context
    const result = await generateLocalLLMConversation(
      input.user_message,
      conversationHistory,
      {
        title: input.journal_entry.title,
        content: input.journal_entry.content,
        emotion: input.journal_entry.emotion,
        moodScore: input.journal_entry.mood_score,
      },
      aiConfig,
      enhancedMemoryContext
    );

    const metadata: AIMessageMetadata = {
      emotion: 'supportive',
      confidence: 0.8,
      processingTime: Date.now() - startTime,
      modelVersion: aiConfig.model || 'llama3',
      context: {
        messageHistory: input.conversation_history.length,
        journalContext: !!input.journal_entry,
        moodScore: input.journal_entry?.mood_score,
        memoryContext: enhancedMemoryContext.length > (memoryContext?.length || 0),
        memoryContextLength: enhancedMemoryContext.length,
      },
    };

    return {
      message: result.message,
      message_type: result.message_type,
      metadata,
      success: true,
    };

  } catch (error) {
    console.error('❌ Local LLM conversation generation failed:', error);

    // Provide different fallback messages based on error type
    let fallbackMessage = "I appreciate you sharing that with me. Sometimes I need a moment to process - could you tell me a bit more about how you're feeling right now?";

    if (error instanceof LocalLLMError) {
      switch (error.type) {
        case 'service_unavailable':
          fallbackMessage = "I'm having trouble connecting right now, but I'm here to listen. Can you tell me more about what's on your mind?";
          break;
        case 'invalid_response':
          fallbackMessage = "I'm processing what you shared - it sounds really meaningful. What part of this experience stands out most to you?";
          break;
        case 'timeout':
          fallbackMessage = "I want to give your thoughts the attention they deserve. Could you share a bit more about how this is affecting you?";
          break;
        default:
          fallbackMessage = "Thank you for sharing that with me. I'm here to support you - what would be most helpful to talk about right now?";
      }
    }

    // Return a fallback response
    return {
      message: fallbackMessage,
      message_type: 'follow_up',
      metadata: {
        emotion: 'supportive',
        confidence: 0.6,
        processingTime: Date.now() - startTime,
        modelVersion: 'fallback',
        context: {
          messageHistory: input.conversation_history.length,
          journalContext: !!input.journal_entry,
          moodScore: input.journal_entry?.mood_score,
        },
      },
      success: false,
    };
  }
};

/**
 * Generate mock conversation response for testing/fallback
 */
const generateMockConversationResponse = (input: AIConversationInput): AIConversationResponse => {
  const responses = [
    "That's really insightful. How do you think this connects to what you wrote in your journal?",
    "I can hear the emotion in your words. What would you say to a friend going through something similar?",
    "Thank you for sharing that with me. What feels most important to you about this experience?",
    "It sounds like you're processing a lot right now. What's one small thing that might help you feel better?",
    "I appreciate your honesty. How has your perspective on this changed since you first wrote about it?",
  ];

  const randomResponse = responses[Math.floor(Math.random() * responses.length)];

  return {
    message: randomResponse,
    message_type: 'follow_up',
    metadata: {
      emotion: 'supportive',
      confidence: 0.7,
      processingTime: 50,
      modelVersion: 'mock',
      context: {
        messageHistory: input.conversation_history.length,
        journalContext: !!input.journal_entry,
        moodScore: input.journal_entry?.mood_score,
      },
    },
    success: true,
  };
};

/**
 * Main function to generate AI conversation responses
 */
export const generateAIConversationResponse = async (
  input: AIConversationInput,
  config?: ConversationAIConfig,
  memoryContext?: string
): Promise<ApiResponse<AIConversationResponse>> => {
  const aiConfig = config || getConversationAIConfig();

  try {
    // Validate input
    if (!input.user_message?.trim()) {
      throw new Error('User message is required');
    }

    if (!input.journal_entry) {
      throw new Error('Journal entry context is required');
    }

    let response: AIConversationResponse;

    try {
      // Try Local LLM first
      console.log('Generating conversation response using Local LLM...');
      response = await generateLocalLLMConversationResponse(input, aiConfig, memoryContext);
    } catch (error) {
      console.warn('Local LLM failed, using mock response:', error);
      response = generateMockConversationResponse(input);
    }

    return {
      success: true,
      data: response,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };

  } catch (error) {
    console.error('Conversation AI generation failed:', error);

    // Handle unexpected errors
    await handleApiError(
      {
        code: 'CONVERSATION_ERROR',
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        retryable: false,
      },
      'aiConversationService',
      'generate_conversation',
      { showToast: false }
    );

    // Return fallback response even for errors
    const fallbackResponse = generateMockConversationResponse(input);

    return {
      success: true, // Still return success with fallback
      data: fallbackResponse,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  }
};
