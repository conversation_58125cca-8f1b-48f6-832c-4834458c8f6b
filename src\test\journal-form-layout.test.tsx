/**
 * Journal Form Layout Tests
 * Tests to verify the mood slider appears above the emotion selector
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { JournalEntryForm } from '@/components/forms/JournalEntryForm';

const mockFormData = {
  title: '',
  content: '',
  emotion: '' as const,
  moodScore: 5 as const,
};

const mockProps = {
  data: mockFormData,
  onChange: vi.fn(),
  onSubmit: vi.fn(),
  onCancel: vi.fn(),
  isLoading: false,
  errors: {},
};

describe('Journal Form Layout', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render mood slider above emotion picker', () => {
    render(<JournalEntryForm {...mockProps} />);

    // Check that both elements are present
    const moodSliderLabel = screen.getByText(/Overall mood \(1-10\)/);
    const emotionPickerLabel = screen.getByText('How are you feeling?');

    expect(moodSliderLabel).toBeInTheDocument();
    expect(emotionPickerLabel).toBeInTheDocument();

    // Check the order by comparing their positions in the DOM
    const moodSliderElement = moodSliderLabel.closest('.space-y-4');
    const emotionPickerElement = emotionPickerLabel.closest('.space-y-4');

    expect(moodSliderElement).toBeInTheDocument();
    expect(emotionPickerElement).toBeInTheDocument();

    // Check that mood slider comes before emotion picker in DOM order
    const moodSliderPosition = moodSliderElement?.compareDocumentPosition(emotionPickerElement!);
    expect(moodSliderPosition).toBe(Node.DOCUMENT_POSITION_FOLLOWING);
  });

  it('should render mood slider with correct label and value', () => {
    render(<JournalEntryForm {...mockProps} />);
    
    // Check that mood slider label is present and shows current value
    expect(screen.getByText('Overall mood (1-10): 5')).toBeInTheDocument();
    
    // Check that Low/High labels are present
    expect(screen.getByText('Low')).toBeInTheDocument();
    expect(screen.getByText('High')).toBeInTheDocument();
  });

  it('should render emotion picker with correct label', () => {
    render(<JournalEntryForm {...mockProps} />);
    
    // Check that emotion picker label is present
    expect(screen.getByText('How are you feeling?')).toBeInTheDocument();
    
    // Check that emotion buttons are present
    expect(screen.getByText('grateful')).toBeInTheDocument();
    expect(screen.getByText('joyful')).toBeInTheDocument();
    expect(screen.getByText('calm')).toBeInTheDocument();
  });

  it('should use vertical layout with proper spacing', () => {
    const { container } = render(<JournalEntryForm {...mockProps} />);
    
    // Find the form section containing mood and emotion components
    const formSection = container.querySelector('.flex.flex-col.gap-6');
    expect(formSection).toBeInTheDocument();
    
    // Check that it has the correct gap class for large spacing
    expect(formSection).toHaveClass('gap-6');
  });

  it('should maintain proper spacing within each section', () => {
    render(<JournalEntryForm {...mockProps} />);

    // Find sections by their labels and check spacing
    const moodSliderLabel = screen.getByText(/Overall mood \(1-10\)/);
    const emotionPickerLabel = screen.getByText('How are you feeling?');

    // Check that both sections have proper spacing classes
    const moodSliderSection = moodSliderLabel.closest('.space-y-4');
    const emotionPickerSection = emotionPickerLabel.closest('.space-y-4');

    expect(moodSliderSection).toHaveClass('space-y-4');
    expect(emotionPickerSection).toHaveClass('space-y-4');
  });

  it('should hide mood section when showMoodSection is false', () => {
    render(<JournalEntryForm {...mockProps} showMoodSection={false} />);
    
    // Mood slider should not be present
    expect(screen.queryByText(/Overall mood/)).not.toBeInTheDocument();
    
    // Emotion picker should not be present
    expect(screen.queryByText('How are you feeling?')).not.toBeInTheDocument();
  });

  it('should show mood section by default', () => {
    render(<JournalEntryForm {...mockProps} />);
    
    // Both mood slider and emotion picker should be present
    expect(screen.getByText(/Overall mood/)).toBeInTheDocument();
    expect(screen.getByText('How are you feeling?')).toBeInTheDocument();
  });
});
