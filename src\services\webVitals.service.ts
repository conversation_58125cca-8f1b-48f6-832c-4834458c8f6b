/**
 * Core Web Vitals Monitoring Service
 * Tracks and reports Core Web Vitals metrics for performance optimization
 */

import { onCLS, onINP, onFCP, onLCP, onTTFB, type Metric } from 'web-vitals';
import { getEnvironmentConfig } from '@/config/environment.config';
import { handleError } from '@/utils/errorHandler';

/**
 * Web Vitals metric data
 */
export interface WebVitalsMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  timestamp: number;
  url: string;
  userAgent: string;
}

/**
 * Web Vitals thresholds based on Google's recommendations
 */
const WEB_VITALS_THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  INP: { good: 200, poor: 500 }, // INP replaced FID
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 },
};

/**
 * Core Web Vitals monitoring service
 */
class WebVitalsService {
  private metrics: WebVitalsMetric[] = [];
  private isEnabled: boolean = false;
  private reportingEndpoint?: string;

  constructor() {
    const env = getEnvironmentConfig();
    this.isEnabled = env.isProduction || env.features.debugMode;
    
    if (this.isEnabled) {
      this.initialize();
    }
  }

  /**
   * Initialize Web Vitals monitoring
   */
  private initialize(): void {
    console.log('[WebVitals] Initializing Core Web Vitals monitoring...');

    try {
      // Track all Core Web Vitals
      onCLS(this.handleMetric.bind(this));
      onINP(this.handleMetric.bind(this)); // INP replaced FID in newer web-vitals
      onFCP(this.handleMetric.bind(this));
      onLCP(this.handleMetric.bind(this));
      onTTFB(this.handleMetric.bind(this));

      console.log('[WebVitals] Core Web Vitals monitoring initialized');
    } catch (error) {
      console.error('[WebVitals] Failed to initialize:', error);
      handleError(error as Error, {
        source: 'WebVitalsService',
        action: 'initialize',
      });
    }
  }

  /**
   * Handle incoming metric data
   */
  private handleMetric(metric: Metric): void {
    const webVitalsMetric: WebVitalsMetric = {
      name: metric.name,
      value: metric.value,
      rating: this.getRating(metric.name, metric.value),
      delta: metric.delta,
      id: metric.id,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    // Store metric
    this.metrics.push(webVitalsMetric);

    // Log in development
    if (getEnvironmentConfig().isDevelopment) {
      console.log(`[WebVitals] ${metric.name}:`, webVitalsMetric);
    }

    // Report metric
    this.reportMetric(webVitalsMetric);

    // Check for performance issues
    this.checkPerformanceIssues(webVitalsMetric);
  }

  /**
   * Get performance rating based on thresholds
   */
  private getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = WEB_VITALS_THRESHOLDS[name as keyof typeof WEB_VITALS_THRESHOLDS];
    
    if (!thresholds) {
      return 'good';
    }

    if (value <= thresholds.good) {
      return 'good';
    } else if (value <= thresholds.poor) {
      return 'needs-improvement';
    } else {
      return 'poor';
    }
  }

  /**
   * Report metric to analytics/monitoring service
   */
  private reportMetric(metric: WebVitalsMetric): void {
    // In a real application, you would send this to your analytics service
    // For now, we'll just log it and store it locally
    
    if (this.reportingEndpoint) {
      // Send to external service
      fetch(this.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metric),
      }).catch(error => {
        console.warn('[WebVitals] Failed to report metric:', error);
      });
    }

    // Store in localStorage for debugging
    if (getEnvironmentConfig().features.debugMode) {
      const storedMetrics = JSON.parse(localStorage.getItem('webVitalsMetrics') || '[]');
      storedMetrics.push(metric);
      
      // Keep only last 100 metrics
      if (storedMetrics.length > 100) {
        storedMetrics.splice(0, storedMetrics.length - 100);
      }
      
      localStorage.setItem('webVitalsMetrics', JSON.stringify(storedMetrics));
    }
  }

  /**
   * Check for performance issues and alert
   */
  private checkPerformanceIssues(metric: WebVitalsMetric): void {
    if (metric.rating === 'poor') {
      console.warn(`[WebVitals] Poor ${metric.name} performance detected:`, metric.value);
      
      // You could trigger alerts, notifications, or automatic optimizations here
      this.triggerPerformanceAlert(metric);
    }
  }

  /**
   * Trigger performance alert
   */
  private triggerPerformanceAlert(metric: WebVitalsMetric): void {
    const alertMessage = `Poor ${metric.name} performance: ${metric.value.toFixed(2)}ms`;
    
    // Log the alert
    console.warn('[WebVitals] Performance Alert:', alertMessage);
    
    // In development, you might want to show a toast or notification
    if (getEnvironmentConfig().isDevelopment) {
      // Could integrate with toast system here
    }
  }

  /**
   * Get all collected metrics
   */
  public getMetrics(): WebVitalsMetric[] {
    return [...this.metrics];
  }

  /**
   * Get metrics summary
   */
  public getMetricsSummary(): Record<string, {
    latest: number;
    average: number;
    rating: string;
    count: number;
  }> {
    const summary: Record<string, any> = {};

    for (const metricName of ['CLS', 'INP', 'FCP', 'LCP', 'TTFB']) {
      const metrics = this.metrics.filter(m => m.name === metricName);
      
      if (metrics.length > 0) {
        const latest = metrics[metrics.length - 1];
        const average = metrics.reduce((sum, m) => sum + m.value, 0) / metrics.length;
        
        summary[metricName] = {
          latest: latest.value,
          average,
          rating: latest.rating,
          count: metrics.length,
        };
      }
    }

    return summary;
  }

  /**
   * Clear stored metrics
   */
  public clearMetrics(): void {
    this.metrics = [];
    localStorage.removeItem('webVitalsMetrics');
  }

  /**
   * Set reporting endpoint
   */
  public setReportingEndpoint(endpoint: string): void {
    this.reportingEndpoint = endpoint;
  }

  /**
   * Check if monitoring is enabled
   */
  public isMonitoringEnabled(): boolean {
    return this.isEnabled;
  }
}

// Export singleton instance
export const webVitalsService = new WebVitalsService();

// Export utility functions
export const getWebVitalsMetrics = () => webVitalsService.getMetrics();
export const getWebVitalsSummary = () => webVitalsService.getMetricsSummary();
export const clearWebVitalsMetrics = () => webVitalsService.clearMetrics();
