/**
 * Conversation Thread Component Tests
 * Integration tests for conversation UI components
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConversationThread } from '@/components/features/ConversationThread';
import { AuthContext } from '@/contexts/AuthContext';
import { JournalEntry, ConversationMessage, ReflectionConversation } from '@/types';
import { toast } from 'sonner';

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('@/hooks/useConversation', () => ({
  useConversationManager: vi.fn(),
}));

vi.mock('@/services/conversationService', () => ({
  createReflectionConversation: vi.fn(),
  createConversationMessage: vi.fn(),
  deleteConversation: vi.fn(),
}));

vi.mock('@/services/aiConversationService', () => ({
  generateAIConversationResponse: vi.fn(),
}));

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  aud: 'authenticated',
  role: 'authenticated',
  email_confirmed_at: '2024-01-01T00:00:00Z',
  phone: '',
  confirmed_at: '2024-01-01T00:00:00Z',
  last_sign_in_at: '2024-01-01T00:00:00Z',
  app_metadata: {},
  user_metadata: {},
  identities: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockJournalEntry: JournalEntry = {
  id: 'entry-123',
  user_id: 'user-123',
  title: 'Test Entry',
  content: 'This is a test journal entry.',
  emotion: 'happy',
  mood_score: 7,
  ai_summary: 'A positive entry',
  ai_emotion: 'joy',
  ai_encouragement: 'Keep up the good work!',
  ai_reflection_question: 'What made you happiest today?',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockConversation: ReflectionConversation = {
  id: 'conv-123',
  journal_entry_id: 'entry-123',
  user_id: 'user-123',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockMessages: ConversationMessage[] = [
  {
    id: 'msg-1',
    conversation_id: 'conv-123',
    sender_type: 'ai',
    message_content: 'What made you happiest today?',
    message_type: 'reflection_question',
    ai_metadata: {
      emotion: 'curious',
      confidence: 0.9,
      processingTime: 150,
    },
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'msg-2',
    conversation_id: 'conv-123',
    sender_type: 'user',
    message_content: 'Spending time with my family made me happiest.',
    message_type: 'text',
    ai_metadata: null,
    created_at: '2024-01-01T00:01:00Z',
  },
];

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  const authContextValue = {
    user: mockUser,
    loading: false,
    signIn: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
    updateProfile: vi.fn(),
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={authContextValue}>
        {children}
      </AuthContext.Provider>
    </QueryClientProvider>
  );
};

describe('ConversationThread', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('when no conversation exists', () => {
    beforeEach(() => {
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: null,
          messages: [],
          isLoading: false,
          isGenerating: false,
          error: null,
          isInitialized: true,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: vi.fn(),
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });
    });

    it('should render conversation starter when no conversation exists and auto-initialize is disabled', () => {
      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} autoInitialize={false} />
        </TestWrapper>
      );

      expect(screen.getByText('Conversation with Amber')).toBeInTheDocument();
      expect(screen.getByText('Ready to reflect with Amber?')).toBeInTheDocument();
      expect(screen.getByText('Start conversation with Amber')).toBeInTheDocument();
    });

    it('should show conversation starter by default when auto-initialize is disabled', async () => {
      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} autoInitialize={false} />
        </TestWrapper>
      );

      expect(screen.getByText('Ready to reflect with Amber?')).toBeInTheDocument();
      expect(screen.getByText('Start conversation with Amber')).toBeInTheDocument();
    });

    it('should show conversation interface immediately when auto-initialize is enabled', async () => {
      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} autoInitialize={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Conversation with Amber')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Share your thoughts with Amber...')).toBeInTheDocument();
      // Should not show the conversation starter button
      expect(screen.queryByText('Start conversation with Amber')).not.toBeInTheDocument();
    });

    it('should start conversation when button is clicked', async () => {
      const mockStartConversation = vi.fn();
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: null,
          messages: [],
          isLoading: false,
          isGenerating: false,
          error: null,
          isInitialized: true,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: mockStartConversation,
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });

      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      const startButton = screen.getByText('Start conversation with Amber');
      await userEvent.click(startButton);

      expect(mockStartConversation).toHaveBeenCalledWith('entry-123');
    });
  });

  describe('when conversation exists', () => {
    beforeEach(() => {
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: mockConversation,
          messages: mockMessages,
          isLoading: false,
          isGenerating: false,
          error: null,
          isInitialized: true,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: vi.fn(),
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });
    });

    it('should display conversation messages', () => {
      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      expect(screen.getByText('What made you happiest today?')).toBeInTheDocument();
      expect(screen.getByText('Spending time with my family made me happiest.')).toBeInTheDocument();
      expect(screen.getByText('(2 messages)')).toBeInTheDocument();
    });

    it('should show message input field', () => {
      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      expect(screen.getByPlaceholderText('Share your thoughts with Amber...')).toBeInTheDocument();
    });

    it('should send message when user types and presses send', async () => {
      const mockSendMessage = vi.fn();
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: mockConversation,
          messages: mockMessages,
          isLoading: false,
          isGenerating: false,
          error: null,
          isInitialized: true,
        },
        actions: {
          sendMessage: mockSendMessage,
          startConversation: vi.fn(),
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });

      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      const input = screen.getByPlaceholderText('Share your thoughts with Amber...');
      const sendButton = screen.getByRole('button', { name: '' }); // Send button with icon

      await userEvent.type(input, 'This is a test message');
      await userEvent.click(sendButton);

      expect(mockSendMessage).toHaveBeenCalledWith('This is a test message');
    });

    it('should show delete confirmation and delete conversation', async () => {
      const mockDeleteConversation = vi.fn().mockResolvedValue({ success: true });
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: mockConversation,
          messages: mockMessages,
          isLoading: false,
          isGenerating: false,
          error: null,
          isInitialized: true,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: vi.fn(),
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: mockDeleteConversation },
        },
      });

      // Mock window.confirm
      const originalConfirm = window.confirm;
      window.confirm = vi.fn().mockReturnValue(true);

      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      const deleteButton = screen.getByTitle('Delete conversation');
      await userEvent.click(deleteButton);

      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to delete this conversation? This action cannot be undone.'
      );
      expect(mockDeleteConversation).toHaveBeenCalledWith('conv-123');

      // Restore original confirm
      window.confirm = originalConfirm;
    });
  });

  describe('loading and error states', () => {
    it('should show loading state', () => {
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: null,
          messages: [],
          isLoading: true,
          isGenerating: false,
          error: null,
          isInitialized: false,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: vi.fn(),
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });

      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      expect(screen.getByText('Loading conversation...')).toBeInTheDocument();
    });

    it('should show error state with retry button', async () => {
      const mockLoadConversation = vi.fn();
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: { id: 'conv-123' },
          messages: [],
          isLoading: false,
          isGenerating: false,
          error: 'Failed to load conversation',
          isInitialized: true,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: vi.fn(),
          loadConversation: mockLoadConversation,
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });

      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      expect(screen.getByText('Failed to load conversation')).toBeInTheDocument();
      
      const retryButton = screen.getByText('Retry');
      await userEvent.click(retryButton);

      expect(mockLoadConversation).toHaveBeenCalledWith('conv-123');
    });

    it('should show typing indicator when AI is generating', () => {
      const { useConversationManager } = require('@/hooks/useConversation');
      vi.mocked(useConversationManager).mockReturnValue({
        state: {
          conversation: mockConversation,
          messages: mockMessages,
          isLoading: false,
          isGenerating: true,
          error: null,
          isInitialized: true,
        },
        actions: {
          sendMessage: vi.fn(),
          startConversation: vi.fn(),
          loadConversation: vi.fn(),
          clearConversation: vi.fn(),
          retryLastResponse: vi.fn(),
        },
        mutations: {
          createConversation: { isPending: false, mutateAsync: vi.fn() },
          sendMessage: { isPending: false, mutateAsync: vi.fn() },
          generateAIResponse: { isPending: false, mutateAsync: vi.fn() },
          deleteConversation: { isPending: false, mutateAsync: vi.fn() },
        },
      });

      render(
        <TestWrapper>
          <ConversationThread journalEntry={mockJournalEntry} />
        </TestWrapper>
      );

      expect(screen.getByText('Amber is thinking...')).toBeInTheDocument();
    });
  });
});
