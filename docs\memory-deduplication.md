# Memory Deduplication System

## Overview

The memory deduplication system prevents duplicate or similar memories from being stored in the database, ensuring a clean and organized memory collection for each user.

## Features

### 1. Automatic Deduplication During Memory Creation

When new memories are extracted from journal entries or conversations, the system automatically checks for existing similar memories before saving:

- **Key Similarity**: Compares normalized memory keys (e.g., "pet_injury" vs "pet_health_concern")
- **Value Similarity**: Compares memory values for semantic similarity
- **Word-based Matching**: Identifies shared significant words between memories
- **Levenshtein Distance**: Uses edit distance as a fallback for very similar strings

### 2. Manual Deduplication

Users can manually trigger deduplication through the Settings page:

- **Remove Duplicates Button**: Available in the Memory Management section
- **Batch Processing**: Processes all memories by category for efficiency
- **Smart Merging**: Keeps the most detailed/longest value when merging similar memories
- **Progress Feedback**: Shows count of removed duplicates and merged memories

## How It Works

### Similarity Detection Algorithm

The system uses a multi-layered approach to detect similar memories:

1. **Exact Match Check**: After text normalization (lowercase, remove underscores/spaces)
2. **Key Word Analysis**: Identifies shared significant words in memory keys
3. **Value Word Analysis**: Compares meaningful words in memory values
4. **Levenshtein Distance**: Calculates edit distance for very similar strings

### Similarity Thresholds

- **Key Similarity**: Memories with >50% shared key words are considered similar
- **Value Similarity**: Memories with >60% shared value words are considered similar
- **Edit Distance**: Strings with >80% similarity are considered duplicates

### Memory Merging Strategy

When similar memories are found:

1. **Keep Primary**: The older memory (by creation date) is kept as primary
2. **Merge Values**: The longest/most detailed value is preserved
3. **Remove Duplicates**: Similar memories are deleted from the database
4. **Update Timestamp**: The primary memory's updated_at field is refreshed

## Implementation Details

### Core Functions

- `areMemoriesSimilar()`: Determines if two memories are similar
- `findSimilarMemory()`: Searches for existing similar memories
- `addUserMemory()`: Enhanced to check for duplicates before insertion
- `deduplicateUserMemories()`: Bulk deduplication function

### Database Constraints

- **Unique Constraint**: `(user_id, key)` prevents exact key duplicates
- **Category Grouping**: Only memories in the same category are compared
- **User Isolation**: Deduplication only occurs within a user's memories

## Usage

### Automatic Prevention

The system automatically prevents duplicates when:
- Extracting memories from journal entries
- Extracting memories from AI conversations
- Manually adding memories through the UI

### Manual Cleanup

Users can clean up existing duplicates by:
1. Going to Settings page
2. Scrolling to Memory Management section
3. Clicking "Remove Duplicates" button
4. Reviewing the results in the success message

## Examples

### Similar Memories Detected

```
Memory 1: { key: "pet_injury", value: "Enzo hurt his paw", category: "event" }
Memory 2: { key: "pet_health_concern", value: "Enzo has health issues", category: "event" }
Result: Similar (shared key word "pet" + related values)
```

```
Memory 1: { key: "work_stress", value: "feeling overwhelmed about quarterly reviews", category: "emotion" }
Memory 2: { key: "work_concern", value: "stressed about quarterly review process", category: "emotion" }
Result: Similar (shared key word "work" + shared value words "quarterly", "review")
```

### Different Memories (Not Similar)

```
Memory 1: { key: "pet", value: "Enzo the cat", category: "fact" }
Memory 2: { key: "work_location", value: "San Francisco office", category: "fact" }
Result: Not similar (no shared words, different contexts)
```

## Benefits

1. **Cleaner Memory Collection**: Eliminates redundant information
2. **Better AI Context**: Provides more focused memory context to AI
3. **Improved Performance**: Reduces memory storage and retrieval overhead
4. **Enhanced User Experience**: Easier to browse and manage memories

## Configuration

The deduplication system can be tuned by adjusting:
- Word length thresholds (currently 2-3 characters minimum)
- Similarity percentages (currently 50-80% depending on method)
- Levenshtein distance thresholds (currently 80% similarity)

These settings are defined in `src/services/memoryPersistenceService.ts`.
