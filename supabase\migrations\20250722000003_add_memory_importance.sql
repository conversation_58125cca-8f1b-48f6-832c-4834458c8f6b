-- Add importance field to memory table for prioritizing meaningful memories
-- Migration: 20250722000003_add_memory_importance.sql

-- Add importance column to memory table
ALTER TABLE public.memory 
ADD COLUMN importance INTEGER DEFAULT 5 CHECK (importance >= 1 AND importance <= 10);

-- Add comment to document the importance field
COMMENT ON COLUMN public.memory.importance IS 'Memory importance score from 1 (not important) to 10 (highly important), default 5';

-- Create index for efficient querying by importance
CREATE INDEX IF NOT EXISTS idx_memory_importance ON public.memory(importance DESC);

-- Create composite index for importance + updated_at for efficient top-N queries
CREATE INDEX IF NOT EXISTS idx_memory_importance_updated_at ON public.memory(importance DESC, updated_at DESC);

-- Create composite index for user-specific importance queries
CREATE INDEX IF NOT EXISTS idx_memory_user_importance ON public.memory(user_id, importance DESC, updated_at DESC);

-- Update existing memories to have default importance of 5 (already handled by DEFAULT)
-- This ensures backward compatibility for existing records

-- Add function to automatically set importance based on memory content length as fallback
CREATE OR REPLACE FUNCTION calculate_default_importance(memory_value TEXT)
R<PERSON>URNS INTEGER AS $$
BEGIN
    -- Simple heuristic: longer, more detailed memories are likely more important
    -- This is a fallback for when AI scoring is not available
    CASE 
        WHEN LENGTH(memory_value) > 200 THEN RETURN 7;
        WHEN LENGTH(memory_value) > 100 THEN RETURN 6;
        WHEN LENGTH(memory_value) > 50 THEN RETURN 5;
        WHEN LENGTH(memory_value) > 20 THEN RETURN 4;
        ELSE RETURN 3;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION calculate_default_importance TO authenticated;

-- Add trigger to update importance for existing records that don't have it set
-- (This is mainly for safety, as we're using DEFAULT 5)
CREATE OR REPLACE FUNCTION ensure_memory_importance()
RETURNS TRIGGER AS $$
BEGIN
    -- If importance is not set or is null, calculate a default
    IF NEW.importance IS NULL THEN
        NEW.importance := calculate_default_importance(NEW.value);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new inserts (backup for when importance is not provided)
CREATE TRIGGER trigger_ensure_memory_importance
    BEFORE INSERT ON public.memory
    FOR EACH ROW
    EXECUTE FUNCTION ensure_memory_importance();

-- Update the existing updated_at trigger to handle importance changes
CREATE OR REPLACE FUNCTION update_memory_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the updated_at trigger
DROP TRIGGER IF EXISTS trigger_update_memory_updated_at ON public.memory;
CREATE TRIGGER trigger_update_memory_updated_at
    BEFORE UPDATE ON public.memory
    FOR EACH ROW
    EXECUTE FUNCTION update_memory_updated_at();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION ensure_memory_importance TO authenticated;
GRANT EXECUTE ON FUNCTION update_memory_updated_at TO authenticated;
