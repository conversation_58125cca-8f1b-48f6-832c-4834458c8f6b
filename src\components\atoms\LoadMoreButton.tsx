/**
 * Load More Button Component
 * Reusable button component for pagination with amber/orange branding and loading states
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';
import { ChevronDown, MoreHorizontal } from 'lucide-react';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface LoadMoreButtonProps extends BaseComponentProps {
  /** Whether the button is in loading state */
  isLoading?: boolean;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Click handler for load more action */
  onClick?: () => void;
  /** Custom loading text */
  loadingText?: string;
  /** Custom button text */
  text?: string;
  /** Button size variant */
  size?: 'sm' | 'default' | 'lg';
  /** Whether to show full width */
  fullWidth?: boolean;
  /** Icon to display */
  icon?: 'chevron' | 'dots' | 'none';
  /** Variant style */
  variant?: 'default' | 'outline' | 'ghost';
}

/**
 * Load More Button component with amber/orange branding
 * 
 * @param props - Component props
 * @returns JSX element
 */
export const LoadMoreButton: React.FC<LoadMoreButtonProps> = ({
  isLoading = false,
  disabled = false,
  onClick,
  loadingText = 'Loading more...',
  text = 'Load More',
  size = 'default',
  fullWidth = false,
  icon = 'chevron',
  variant = 'outline',
  className,
  testId = 'load-more-button',
  ...props
}) => {
  // Determine which icon to show
  const renderIcon = () => {
    if (isLoading) {
      return <LoadingSpinner size="sm" />;
    }

    switch (icon) {
      case 'chevron':
        return <ChevronDown className="h-4 w-4" />;
      case 'dots':
        return <MoreHorizontal className="h-4 w-4" />;
      case 'none':
      default:
        return null;
    }
  };

  // Custom amber/orange styling
  const amberStyles = cn(
    // Base amber/orange styling
    'border-amber-200 text-amber-700 hover:bg-amber-50 hover:text-amber-800',
    'focus:ring-amber-500 focus:border-amber-500',
    'dark:border-amber-600 dark:text-amber-400 dark:hover:bg-amber-900/20 dark:hover:text-amber-300',
    
    // Loading state styling
    isLoading && 'cursor-wait opacity-75',
    
    // Disabled state styling
    disabled && 'opacity-50 cursor-not-allowed',
    
    // Full width styling
    fullWidth && 'w-full',
    
    // Enhanced focus and hover states for accessibility
    'transition-all duration-200 ease-in-out',
    'hover:shadow-md hover:scale-[1.02]',
    'active:scale-[0.98]',
    
    className
  );

  return (
    <div className={cn('flex justify-center', fullWidth && 'w-full')}>
      <Button
        variant={variant}
        size={size}
        onClick={onClick}
        disabled={disabled || isLoading}
        className={amberStyles}
        data-testid={testId}
        aria-label={isLoading ? loadingText : text}
        aria-busy={isLoading}
        {...props}
      >
        {renderIcon()}
        <span className="ml-2">
          {isLoading ? loadingText : text}
        </span>
      </Button>
    </div>
  );
};

/**
 * Load More Button with enhanced accessibility and ARIA support
 */
export const AccessibleLoadMoreButton: React.FC<LoadMoreButtonProps & {
  /** Total number of items loaded */
  loadedCount?: number;
  /** Total number of items available */
  totalCount?: number;
  /** Whether there are more items to load */
  hasMore?: boolean;
}> = ({
  loadedCount,
  totalCount,
  hasMore,
  ...props
}) => {
  // Generate accessible description
  const getAriaDescription = () => {
    if (props.isLoading) {
      return 'Loading more journal entries';
    }
    
    if (!hasMore) {
      return 'All journal entries have been loaded';
    }
    
    if (loadedCount && totalCount) {
      return `Showing ${loadedCount} of ${totalCount} journal entries. Click to load more.`;
    }
    
    if (loadedCount) {
      return `${loadedCount} journal entries loaded. Click to load more.`;
    }
    
    return 'Click to load more journal entries';
  };

  return (
    <div
      role="region"
      aria-label="Pagination controls"
      aria-describedby="load-more-description"
    >
      <LoadMoreButton
        {...props}
        disabled={props.disabled || !hasMore}
        aria-describedby="load-more-description"
      />
      <div
        id="load-more-description"
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
      >
        {getAriaDescription()}
      </div>
    </div>
  );
};

// Export both components
export default LoadMoreButton;
