# Amberglow Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Application Configuration
VITE_APP_NAME=Amberglow
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Supabase Configuration
# Get these values from your Supabase project dashboard at https://supabase.com/dashboard
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# AI Configuration
# Local LLM (Ollama) Configuration - Default setup
# Make sure Ollama is running locally with llama3.1:8b model
# Download: https://ollama.ai/
# Run: ollama pull llama3.1:8b && ollama serve
VITE_LOCAL_LLM_ENDPOINT=http://localhost:11434/v1/chat/completions

# Google Gemini AI Configuration (Legacy - Optional)
# Get your API key from: https://makersuite.google.com/app/apikey
# This is optional - now using local LLM by default
VITE_GEMINI_API_KEY=your-gemini-api-key-here

# Feature Flags
# Enable or disable AI-powered features (true/false)
VITE_ENABLE_AI_FEATURES=true

# Enable analytics tracking (true/false)
VITE_ENABLE_ANALYTICS=false

# Enable debug mode for development (true/false)
VITE_ENABLE_DEBUG_MODE=true

# API Configuration
# Request timeout in milliseconds
VITE_API_TIMEOUT=30000

# Number of retry attempts for failed requests
VITE_API_RETRY_ATTEMPTS=3

# Security Configuration
# Enforce HTTPS-only connections (true/false)
VITE_ENABLE_HTTPS_ONLY=true

# Enable strict Content Security Policy (true/false)
VITE_ENABLE_STRICT_CSP=false

# Environment-specific examples:

# Development Environment
# VITE_APP_ENVIRONMENT=development
# VITE_ENABLE_DEBUG_MODE=true
# VITE_ENABLE_ANALYTICS=false
# VITE_ENABLE_STRICT_CSP=false

# Staging Environment
# VITE_APP_ENVIRONMENT=staging
# VITE_ENABLE_DEBUG_MODE=false
# VITE_ENABLE_ANALYTICS=true
# VITE_ENABLE_STRICT_CSP=true

# Production Environment
# VITE_APP_ENVIRONMENT=production
# VITE_ENABLE_DEBUG_MODE=false
# VITE_ENABLE_ANALYTICS=true
# VITE_ENABLE_STRICT_CSP=true
# VITE_ENABLE_HTTPS_ONLY=true
