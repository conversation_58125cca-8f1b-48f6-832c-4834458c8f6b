/**
 * Error Boundary Component
 * React error boundary with enhanced error handling and user-friendly fallback UI
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { createErrorBoundaryHandler } from '@/utils/errorHandler';
import { BaseComponentProps } from '@/types';

interface ErrorBoundaryProps extends BaseComponentProps {
  /** Child components to render */
  children: ReactNode;
  /** Custom fallback component */
  fallback?: (error: Error, retry: () => void) => ReactNode;
  /** Component name for error tracking */
  componentName?: string;
  /** Whether to show detailed error information in development */
  showErrorDetails?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Callback when error occurs */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

/**
 * Default fallback UI component
 */
const DefaultErrorFallback = ({
  error,
  retry,
  errorMessage,
  showErrorDetails = false,
}: {
  error: Error;
  retry: () => void;
  errorMessage?: string;
  showErrorDetails?: boolean;
}) => (
  <div className="min-h-[400px] flex items-center justify-center p-4">
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
          <AlertTriangle className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle className="text-xl">Something went wrong</CardTitle>
        <CardDescription>
          {errorMessage || 'An unexpected error occurred. Please try refreshing the page.'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {showErrorDetails && process.env.NODE_ENV === 'development' && (
          <div className="rounded-md bg-gray-50 p-3">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Error Details:</h4>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap break-words">
              {error.message}
            </pre>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-2">
          <Button onClick={retry} className="flex-1" variant="default">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
          <Button onClick={() => (window.location.href = '/')} variant="outline" className="flex-1">
            <Home className="mr-2 h-4 w-4" />
            Go Home
          </Button>
        </div>
      </CardContent>
    </Card>
  </div>
);

/**
 * React Error Boundary with enhanced error handling
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private errorHandler: (error: Error, errorInfo: any) => void;

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };

    // Create error handler for this component
    this.errorHandler = createErrorBoundaryHandler(props.componentName || 'ErrorBoundary');
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `boundary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error using our centralized error handler
    this.errorHandler(error, errorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Update state with error info
    this.setState({
      errorInfo,
    });
  }

  /**
   * Retry function to reset error boundary
   */
  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  render() {
    const {
      children,
      fallback,
      errorMessage,
      showErrorDetails = false,
      className,
      testId,
    } = this.props;

    const { hasError, error } = this.state;

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, this.handleRetry);
      }

      // Use default fallback
      return (
        <div className={className} data-testid={testId}>
          <DefaultErrorFallback
            error={error}
            retry={this.handleRetry}
            errorMessage={errorMessage}
            showErrorDetails={showErrorDetails}
          />
        </div>
      );
    }

    return children;
  }
}

/**
 * Higher-order component to wrap components with error boundary
 */
export const withErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WithErrorBoundaryComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundaryComponent;
};

/**
 * Hook to manually trigger error boundary (for functional components)
 */
export const useErrorBoundary = () => {
  const [, setState] = React.useState();

  return React.useCallback((error: Error) => {
    setState(() => {
      throw error;
    });
  }, []);
};

/**
 * Async error boundary hook for handling async errors
 */
export const useAsyncErrorBoundary = () => {
  const throwError = useErrorBoundary();

  return React.useCallback(
    (error: Error) => {
      // Use setTimeout to ensure error is thrown in next tick
      setTimeout(() => {
        throwError(error);
      }, 0);
    },
    [throwError]
  );
};
