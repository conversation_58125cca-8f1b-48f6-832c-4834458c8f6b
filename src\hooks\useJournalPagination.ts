/**
 * Journal Pagination Hook
 * Custom hook for managing paginated journal entries with React Query integration
 */

import { useState, useCallback, useMemo } from 'react';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import {
  FormattedJournalEntry,
  JournalPaginationState,
  JournalPaginationOptions,
  JournalPaginationResult,
  JournalPaginationActions,
  JournalEntryFilters,
} from '@/types';
import { getJournalEntries, formatJournalEntryForDisplay } from '@/services/journalService';
import { queryKeys } from '@/config/queryClient.config';

/**
 * Default pagination configuration
 */
const DEFAULT_PAGE_SIZE = 10;
const DEFAULT_STALE_TIME = 5 * 60 * 1000; // 5 minutes

/**
 * Hook return type combining state and actions
 */
export interface UseJournalPaginationReturn extends JournalPaginationState, JournalPaginationActions {
  /** Error state from React Query */
  error: Error | null;
  /** Whether any operation is currently loading */
  isLoading: boolean;
  /** Whether the hook is fetching data */
  isFetching: boolean;
  /** Whether the initial load failed */
  isError: boolean;
}

/**
 * Custom hook for paginated journal entries
 * 
 * @param options - Configuration options for pagination
 * @returns Pagination state and actions
 */
export const useJournalPagination = (
  options: JournalPaginationOptions = {}
): UseJournalPaginationReturn => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    pageSize = DEFAULT_PAGE_SIZE,
    filters = {},
    staleTime = DEFAULT_STALE_TIME,
  } = options;

  // Local state for tracking manual operations
  const [localEntries, setLocalEntries] = useState<FormattedJournalEntry[]>([]);
  const [hasLocalChanges, setHasLocalChanges] = useState(false);

  // Create query key with filters
  const queryKey = useMemo(
    () => queryKeys.journalEntries.list(user?.id || '', { ...filters, pageSize }),
    [user?.id, JSON.stringify(filters), pageSize] // Stabilize filters object
  );

  // Infinite query for pagination
  const {
    data,
    error,
    isLoading,
    isFetching,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteQuery<JournalPaginationResult, Error>({
    queryKey,
    initialPageParam: 0,
    queryFn: async ({ pageParam = 0 }) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const queryFilters: JournalEntryFilters = {
        ...filters,
        limit: pageSize,
        offset: pageParam as number,
      };

      const response = await getJournalEntries(user.id, queryFilters);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to fetch journal entries');
      }

      const formattedEntries = response.data.map(formatJournalEntryForDisplay);
      
      return {
        entries: formattedEntries,
        hasMore: formattedEntries.length === pageSize,
        total: response.meta?.pagination?.total || 0,
        offset: pageParam as number,
        limit: pageSize,
      };
    },
    getNextPageParam: (lastPage: JournalPaginationResult) => {
      if (!lastPage.hasMore) return undefined;
      return lastPage.offset + lastPage.limit;
    },
    enabled: !!user?.id,
    staleTime,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: 'always', // Always refetch when component mounts
  });

  // Combine all loaded entries
  const allLoadedEntries = useMemo(() => {
    const pages = data?.pages as JournalPaginationResult[] | undefined;
    const queryEntries = pages?.flatMap(page => page.entries) || [];

    if (hasLocalChanges) {
      const entryMap = new Map<string, FormattedJournalEntry>();

      queryEntries.forEach(entry => entryMap.set(entry.id, entry));
      localEntries.forEach(entry => {
        if ((entry as any).deleted) {
          entryMap.delete(entry.id); // Remove from final list
        } else {
          entryMap.set(entry.id, entry);
        }
      });

      return Array.from(entryMap.values()).sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
    }

    // Return query entries when no local changes
    return queryEntries.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }, [data?.pages, hasLocalChanges, localEntries]);


  // Calculate pagination state
  const paginationState: JournalPaginationState = useMemo(() => {
    const pages = data?.pages as JournalPaginationResult[] | undefined;
    return {
      loadedEntries: allLoadedEntries,
      currentOffset: pages?.reduce((acc, page) => acc + page.entries.length, 0) || 0,
      hasMoreEntries: hasNextPage || false,
      isLoadingMore: isFetchingNextPage,
      isInitialLoading: isLoading,
      pageSize,
      totalEntries: pages?.[0]?.total || 0,
    };
  }, [allLoadedEntries, data?.pages, hasNextPage, isFetchingNextPage, isLoading, pageSize]);

  // Action handlers
  const loadMore = useCallback(async (): Promise<void> => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const refresh = useCallback(async (): Promise<void> => {
    setLocalEntries([]);
    setHasLocalChanges(false);
    await refetch();
  }, [refetch]);

  const reset = useCallback((): void => {
    setLocalEntries([]);
    setHasLocalChanges(false);
    queryClient.removeQueries({ queryKey });
  }, [queryClient, queryKey]);

  const addEntry = useCallback((entry: FormattedJournalEntry): void => {
    setLocalEntries(prev => [entry, ...prev]);
    setHasLocalChanges(true);
  }, []);

const updateEntry = useCallback((id: string, updates: Partial<FormattedJournalEntry>): void => {
  setLocalEntries(prev => {
    const updatedSet = new Map<string, FormattedJournalEntry>();

    // Always start with existing local entries (excluding any duplicates)
    prev.forEach(entry => {
      if (entry.id !== id) {
        updatedSet.set(entry.id, entry);
      }
    });

    // Overwrite with the new updated version
    const existingEntry =
      prev.find(entry => entry.id === id) ||
      allLoadedEntries.find(entry => entry.id === id);

    if (existingEntry) {
      updatedSet.set(id, { ...existingEntry, ...updates } as FormattedJournalEntry);
    }

    return Array.from(updatedSet.values());
  });

  setHasLocalChanges(true);
}, [allLoadedEntries]);


const removeEntry = useCallback((id: string): void => {
  // Add a "deleted" marker to localEntries
  setLocalEntries(prev => {
    // Remove it from local
    const filtered = prev.filter(entry => entry.id !== id);

    // Also add a dummy "tombstone" entry to override the query one
    const tombstone = { id, deleted: true } as unknown as FormattedJournalEntry;

    return [tombstone, ...filtered];
  });

  setHasLocalChanges(true);
}, []);


  

  const actions: JournalPaginationActions = {
    loadMore,
    refresh,
    reset,
    addEntry,
    updateEntry,
    removeEntry,
  };

  return {
    ...paginationState,
    ...actions,
    error,
    isLoading: isLoading || isFetchingNextPage,
    isFetching,
    isError,
  };
};
