/**
 * Form Modal Component
 * Specialized modal component for forms with consistent styling and behavior
 * Consolidates form modal patterns with validation and submission handling
 */

import React from 'react';
import { GlassModal } from './glass-modal';
import { AmberButtonPair } from './amber-button';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface FormModalProps extends BaseComponentProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Form submission handler */
  onSubmit: () => void;
  /** Modal title */
  title: string;
  /** Modal description */
  description?: string;
  /** Form content */
  children: React.ReactNode;
  /** Submit button text */
  submitText?: string;
  /** Cancel button text */
  cancelText?: string;
  /** Whether the form is in loading/submitting state */
  isLoading?: boolean;
  /** Whether the submit button should be disabled */
  submitDisabled?: boolean;
  /** Modal size */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  /** Icon to display in header */
  icon?: React.ReactNode;
  /** Icon background color */
  iconBgColor?: string;
  /** Whether to validate form before submission */
  validateOnSubmit?: boolean;
  /** Validation function */
  validate?: () => boolean;
  /** Whether to reset form on close */
  resetOnClose?: boolean;
  /** Reset function */
  onReset?: () => void;
  /** Whether to show unsaved changes warning */
  showUnsavedWarning?: boolean;
  /** Function to check if form has unsaved changes */
  hasUnsavedChanges?: () => boolean;
}

/**
 * FormModal component with form-specific behavior and styling
 */
export const FormModal: React.FC<FormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  title,
  description,
  children,
  submitText = 'Save',
  cancelText = 'Cancel',
  isLoading = false,
  submitDisabled = false,
  size = 'lg',
  icon,
  iconBgColor = 'bg-amber-100',
  validateOnSubmit = true,
  validate,
  resetOnClose = true,
  onReset,
  showUnsavedWarning = false,
  hasUnsavedChanges,
  className,
  testId,
  ...props
}) => {
  const [showWarning, setShowWarning] = React.useState(false);

  const handleSubmit = () => {
    // Validate form if validation is enabled
    if (validateOnSubmit && validate && !validate()) {
      return;
    }

    onSubmit();
  };

  const handleClose = () => {
    // Check for unsaved changes
    if (showUnsavedWarning && hasUnsavedChanges && hasUnsavedChanges()) {
      setShowWarning(true);
      return;
    }

    // Reset form if enabled
    if (resetOnClose && onReset) {
      onReset();
    }

    onClose();
  };

  const handleConfirmClose = () => {
    setShowWarning(false);
    
    // Reset form if enabled
    if (resetOnClose && onReset) {
      onReset();
    }

    onClose();
  };

  const handleCancelClose = () => {
    setShowWarning(false);
  };

  // Content wrapper without form element (child components handle their own forms)
  const formContent = (
    <div className={cn('space-y-6', className)}>
      {children}
    </div>
  );

  // Footer with action buttons
  const footer = (
    <AmberButtonPair
      cancelText={cancelText}
      actionText={submitText}
      onCancel={handleClose}
      onAction={handleSubmit}
      isLoading={isLoading}
      actionVariant="primary"
      variant="form"
      testId={testId ? `${testId}-actions` : undefined}
    />
  );

  return (
    <>
      {/* Main Form Modal */}
      <GlassModal
        isOpen={isOpen && !showWarning}
        onClose={handleClose}
        title={title}
        description={description}
        size={size}
        icon={icon}
        iconBgColor={iconBgColor}
        footer={footer}
        closeOnOutsideClick={!showUnsavedWarning || !hasUnsavedChanges?.()}
        testId={testId}
        {...props}
      >
        {formContent}
      </GlassModal>

      {/* Unsaved Changes Warning Modal */}
      {showUnsavedWarning && (
        <GlassModal
          isOpen={showWarning}
          onClose={handleCancelClose}
          title="Unsaved Changes"
          description="You have unsaved changes. Are you sure you want to close without saving?"
          size="sm"
          icon={
            <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          }
          footer={
            <AmberButtonPair
              cancelText="Keep Editing"
              actionText="Discard Changes"
              onCancel={handleCancelClose}
              onAction={handleConfirmClose}
              actionVariant="destructive"
              testId={testId ? `${testId}-warning-actions` : undefined}
            />
          }
          testId={testId ? `${testId}-warning` : undefined}
        />
      )}
    </>
  );
};

/**
 * Hook for managing form modal state
 */
export const useFormModal = (initialOpen = false) => {
  const [isOpen, setIsOpen] = React.useState(initialOpen);
  const [isLoading, setIsLoading] = React.useState(false);

  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);
  
  const withLoading = async (fn: () => Promise<void> | void) => {
    setIsLoading(true);
    try {
      await fn();
    } finally {
      setIsLoading(false);
    }
  };

  const submitAndClose = async (fn: () => Promise<void> | void) => {
    await withLoading(fn);
    close();
  };

  return {
    isOpen,
    isLoading,
    open,
    close,
    setIsLoading,
    withLoading,
    submitAndClose,
  };
};

/**
 * EditModal component for editing existing items
 */
interface EditModalProps<T> extends Omit<FormModalProps, 'onSubmit' | 'title'> {
  /** Item being edited */
  item: T | null;
  /** Save handler */
  onSave: (item: T) => void;
  /** Title prefix */
  titlePrefix?: string;
  /** Function to get item name for title */
  getItemName?: (item: T) => string;
}

export function EditModal<T>({
  item,
  onSave,
  titlePrefix = 'Edit',
  getItemName,
  ...props
}: EditModalProps<T>) {
  const title = item && getItemName 
    ? `${titlePrefix} ${getItemName(item)}`
    : titlePrefix;

  const handleSubmit = () => {
    if (item) {
      onSave(item);
    }
  };

  return (
    <FormModal
      {...props}
      title={title}
      onSubmit={handleSubmit}
      isOpen={!!item && props.isOpen}
    />
  );
}
