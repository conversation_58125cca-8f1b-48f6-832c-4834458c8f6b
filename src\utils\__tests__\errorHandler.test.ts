/**
 * Error Handler Test Suite
 * Tests for error handling utilities
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleError, createErrorBoundaryHandler } from '../errorHandler';

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

describe('handleError', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  it('should handle basic error', () => {
    const error = new Error('Test error');
    const context = {
      source: 'test',
      action: 'test-action',
    };

    expect(() => {
      handleError(error, context);
    }).not.toThrow();
  });

  it('should handle error with custom options', () => {
    const error = new Error('Test error');
    const context = {
      source: 'test',
      action: 'test-action',
    };
    const options = {
      showToast: true,
      logToConsole: true,
      customMessage: 'Custom error message',
    };

    expect(() => {
      handleError(error, context, options);
    }).not.toThrow();
  });

  it('should handle error with metadata', () => {
    const error = new Error('Test error');
    const context = {
      source: 'test',
      action: 'test-action',
      metadata: { userId: '123', timestamp: Date.now() },
    };

    expect(() => {
      handleError(error, context);
    }).not.toThrow();
  });
});

describe('createErrorBoundaryHandler', () => {
  it('should create error boundary handler', () => {
    const handler = createErrorBoundaryHandler('TestComponent');
    expect(typeof handler).toBe('function');
  });

  it('should handle error boundary errors', () => {
    const handler = createErrorBoundaryHandler('TestComponent');
    const error = new Error('Component error');
    const errorInfo = { componentStack: 'test stack' };

    expect(() => {
      handler(error, errorInfo);
    }).not.toThrow();
  });
});
