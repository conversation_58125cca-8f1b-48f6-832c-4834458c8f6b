/**
 * Date Utilities
 * Centralized date handling functions to ensure consistent timezone handling
 * across the application, especially for journal entry dates
 */

/**
 * Convert a UTC timestamp to a local date string in YYYY-MM-DD format
 * This ensures that journal entries display the date in the user's local timezone
 * rather than UTC, which can cause date discrepancies.
 * 
 * @param utcTimestamp - UTC timestamp string (ISO format)
 * @returns Local date string in YYYY-MM-DD format
 */
export function utcTimestampToLocalDateString(utcTimestamp: string): string {
  const localDate = new Date(utcTimestamp);
  const localDateString = localDate.getFullYear() + '-' + 
    String(localDate.getMonth() + 1).padStart(2, '0') + '-' + 
    String(localDate.getDate()).padStart(2, '0');
  return localDateString;
}

/**
 * Parse a local date string (YYYY-MM-DD) to a Date object
 * This avoids timezone issues that can occur when using new Date(dateString)
 * with ISO date strings, which are interpreted as UTC.
 * 
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Date object representing the local date
 */
export function parseLocalDateString(dateString: string): Date {
  const [year, month, day] = dateString.split('-').map(Number);
  return new Date(year, month - 1, day); // month is 0-indexed
}

/**
 * Format a date string for display with full date format
 * 
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Formatted date string (e.g., "Saturday, July 12, 2025")
 */
export function formatDateForDisplay(dateString: string, fullDate: boolean = true): string {
  const dateObj = parseLocalDateString(dateString);

  if (fullDate) {
    return dateObj.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  return dateObj.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
}

/**
 * Get the current local date as a string in YYYY-MM-DD format
 * This is useful for displaying the current date on the write page
 * 
 * @returns Current local date string in YYYY-MM-DD format
 */
export function getCurrentLocalDateString(): string {
  const now = new Date();
  return now.getFullYear() + '-' + 
    String(now.getMonth() + 1).padStart(2, '0') + '-' + 
    String(now.getDate()).padStart(2, '0');
}

/**
 * Get the current local date formatted for display
 * 
 * @returns Current local date formatted (e.g., "Saturday, July 12, 2025")
 */
export function getCurrentLocalDateForDisplay(): string {
  return new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}
