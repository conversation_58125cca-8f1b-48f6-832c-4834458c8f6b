/**
 * Secure Config Test Suite
 * Tests for secure configuration utilities
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { maskSensitiveValue, validateApiKey, checkCriticalServices } from '../secureConfig';

// Mock environment config
vi.mock('@/config/environment.config', () => ({
  getEnvironmentConfig: vi.fn(() => ({
    environment: 'test',
    supabase: {
      url: 'https://test.supabase.co',
      anonKey: 'test-anon-key',
    },
    ai: {
      geminiApiKey: 'test-gemini-key',
    },
    features: {
      aiEnabled: true,
    },
  })),
}));

describe('maskSensitiveValue', () => {
  it('should mask sensitive values correctly', () => {
    const result = maskSensitiveValue('secretkey123', 4);
    expect(result).toBe('secr********'); // 'secretkey123' = 12 chars, 4 visible = 8 masked
  });

  it('should handle short values', () => {
    const result = maskSensitiveValue('abc', 4);
    expect(result).toBe('***');
  });

  it('should handle empty values', () => {
    const result = maskSensitiveValue('', 4);
    expect(result).toBe('***');
  });

  it('should use default visible characters', () => {
    const result = maskSensitiveValue('secretkey123');
    expect(result).toBe('secr********'); // 'secretkey123' = 12 chars, 4 visible = 8 masked
  });
});

describe('validateApiKey', () => {
  it('should validate API key format', () => {
    const validKey = 'sk-1234567890abcdef';
    const result = validateApiKey(validKey, 'sk');
    expect(result).toBe(true);
  });

  it('should reject invalid API key format', () => {
    const invalidKey = 'invalid-key';
    const result = validateApiKey(invalidKey, 'sk');
    expect(result).toBe(false);
  });

  it('should reject empty API key', () => {
    const result = validateApiKey('', 'sk');
    expect(result).toBe(false);
  });
});

describe('checkCriticalServices', () => {
  it('should check critical services status', () => {
    const result = checkCriticalServices();
    expect(result).toHaveProperty('supabase');
    expect(result).toHaveProperty('ai');
    expect(result).toHaveProperty('overall');
    expect(typeof result.supabase).toBe('boolean');
    expect(typeof result.ai).toBe('boolean');
    expect(typeof result.overall).toBe('boolean');
  });
});
