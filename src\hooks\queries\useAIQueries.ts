/**
 * AI Queries Hook
 * React Query hooks for AI-powered features with intelligent caching
 */

import {
  useMutation,
  useQuery,
  useQueryClient,
  UseMutationOptions,
  UseQueryOptions,
} from '@tanstack/react-query';
import { AIReflectionInput, AIReflectionResponse, GeminiAIResponse, ApiResponse } from '@/types';
import { generateAIReflection } from '@/services/aiReflectionService';
import { queryKeys } from '@/config/queryClient.config';
import { getEnvironmentConfig } from '@/config/environment.config';
import { toast } from '@/components/ui/sonner';

/**
 * Hook to generate AI reflection for journal entries
 */
export const useGenerateAIReflection = (
  options?: UseMutationOptions<AIReflectionResponse, Error, AIReflectionInput>
) => {
  const queryClient = useQueryClient();
  const env = getEnvironmentConfig();

  return useMutation({
    mutationFn: async (input: AIReflectionInput): Promise<AIReflectionResponse> => {
      if (!env.features.aiEnabled) {
        throw new Error('AI features are disabled');
      }

      const response = await generateAIReflection(input);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to generate AI reflection');
      }

      return response.data;
    },
    onSuccess: (data, variables) => {
      // Cache the AI reflection result for potential reuse
      queryClient.setQueryData(queryKeys.aiReflections.generate(variables), data);

      // Show success message only if explicitly requested
      if (env.features.debugMode) {
        toast.success('AI reflection generated successfully!');
      }
    },
    onError: error => {
      // Handle AI-specific errors gracefully
      if (error.message.includes('API key')) {
        toast.error('AI service is not configured. Using fallback response.');
      } else if (error.message.includes('rate limit')) {
        toast.error('AI service is temporarily unavailable. Please try again later.');
      } else {
        toast.error('Failed to generate AI reflection. Using fallback response.');
      }
    },
    // Configure retry behavior for AI requests
    retry: (failureCount, error) => {
      // Don't retry on configuration errors
      if (error.message.includes('API key') || error.message.includes('disabled')) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 5000),
    ...options,
  });
};

/**
 * Hook to get cached AI reflection if available
 */
export const useCachedAIReflection = (
  input: AIReflectionInput,
  options?: Omit<UseQueryOptions<AIReflectionResponse, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: queryKeys.aiReflections.generate(input),
    queryFn: () => {
      // This will only return cached data, not make a new request
      throw new Error('No cached reflection available');
    },
    enabled: false, // Never automatically fetch
    staleTime: 30 * 60 * 1000, // 30 minutes - AI reflections are relatively stable
    cacheTime: 60 * 60 * 1000, // 1 hour
    ...options,
  });
};

/**
 * Hook to prefetch AI reflection (useful for performance optimization)
 */
export const usePrefetchAIReflection = () => {
  const queryClient = useQueryClient();
  const env = getEnvironmentConfig();

  const prefetchReflection = async (input: AIReflectionInput) => {
    if (!env.features.aiEnabled) return;

    // Check if we already have this reflection cached
    const cachedData = queryClient.getQueryData(queryKeys.aiReflections.generate(input));
    if (cachedData) return;

    try {
      await queryClient.prefetchQuery({
        queryKey: queryKeys.aiReflections.generate(input),
        queryFn: async () => {
          const response = await generateAIReflection(input);
          if (!response.success || !response.data) {
            throw new Error(response.error?.message || 'Failed to generate AI reflection');
          }
          return response.data;
        },
        staleTime: 30 * 60 * 1000, // 30 minutes
      });
    } catch (error) {
      // Silently fail prefetch attempts
      console.warn('Failed to prefetch AI reflection:', error);
    }
  };

  return { prefetchReflection };
};

/**
 * Hook to manage AI reflection history for a user
 */
export const useAIReflectionHistory = (
  userId: string,
  options?: Omit<UseQueryOptions<AIReflectionResponse[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: queryKeys.aiReflections.history(userId),
    queryFn: async (): Promise<AIReflectionResponse[]> => {
      // This would typically fetch from a backend service
      // For now, we'll return cached reflections
      const cache = queryClient.getQueryCache();
      const reflections: AIReflectionResponse[] = [];

      cache.getAll().forEach(query => {
        if (
          query.queryKey[0] === 'aiReflections' &&
          query.queryKey[1] === 'generate' &&
          query.state.data
        ) {
          reflections.push(query.state.data as AIReflectionResponse);
        }
      });

      return reflections.slice(0, 50); // Limit to last 50 reflections
    },
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    ...options,
  });
};

/**
 * Hook to get AI service status and configuration
 */
export const useAIServiceStatus = (
  options?: Omit<
    UseQueryOptions<{ available: boolean; configured: boolean; features: any }, Error>,
    'queryKey' | 'queryFn'
  >
) => {
  return useQuery({
    queryKey: ['aiService', 'status'],
    queryFn: async () => {
      const env = getEnvironmentConfig();

      return {
        available: env.features.aiEnabled,
        configured: !!env.ai.geminiApiKey,
        features: {
          reflectionEnabled: env.features.aiEnabled,
          fallbackEnabled: true,
          showSource: env.features.debugMode,
        },
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

/**
 * Hook to clear AI reflection cache
 */
export const useAIReflectionCacheUtils = () => {
  const queryClient = useQueryClient();

  return {
    clearAllReflections: () => {
      queryClient.removeQueries({
        queryKey: queryKeys.aiReflections.all,
      });
    },
    clearReflection: (input: AIReflectionInput) => {
      queryClient.removeQueries({
        queryKey: queryKeys.aiReflections.generate(input),
      });
    },
    invalidateHistory: (userId: string) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.aiReflections.history(userId),
      });
    },
    getReflectionFromCache: (input: AIReflectionInput) => {
      return queryClient.getQueryData<AIReflectionResponse>(
        queryKeys.aiReflections.generate(input)
      );
    },
  };
};

/**
 * Hook for optimistic AI reflection updates
 */
export const useOptimisticAIReflection = () => {
  const queryClient = useQueryClient();

  const setOptimisticReflection = (
    input: AIReflectionInput,
    optimisticData: Partial<AIReflectionResponse>
  ) => {
    const fallbackReflection: AIReflectionResponse = {
      summary: 'Generating your reflection...',
      emotion: input.emotion || 'thoughtful',
      encouragement:
        "Thank you for sharing your thoughts. I'm preparing a personalized reflection for you.",
      reflection_question: 'What would you like to explore further about this experience?',
      reflection: 'Generating reflection...',
      success: true,
      ...optimisticData,
    };

    queryClient.setQueryData(queryKeys.aiReflections.generate(input), fallbackReflection);

    return fallbackReflection;
  };

  const clearOptimisticReflection = (input: AIReflectionInput) => {
    queryClient.removeQueries({
      queryKey: queryKeys.aiReflections.generate(input),
    });
  };

  return {
    setOptimisticReflection,
    clearOptimisticReflection,
  };
};
