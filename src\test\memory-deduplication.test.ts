/**
 * Memory Deduplication Tests
 * Tests for the memory deduplication functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UserMemory } from '@/types';

// Mock the similarity calculation function
const calculateSimilarity = (str1: string, str2: string): number => {
  const len1 = str1.length;
  const len2 = str2.length;

  if (len1 === 0 && len2 === 0) return 1.0;
  if (len1 === 0 || len2 === 0) return 0.0;

  const matrix = Array(len2 + 1).fill(null).map(() => Array(len1 + 1).fill(null));

  for (let i = 0; i <= len1; i++) matrix[0][i] = i;
  for (let j = 0; j <= len2; j++) matrix[j][0] = j;

  for (let j = 1; j <= len2; j++) {
    for (let i = 1; i <= len1; i++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j - 1][i] + 1,     // deletion
        matrix[j][i - 1] + 1,     // insertion
        matrix[j - 1][i - 1] + cost // substitution
      );
    }
  }

  const maxLen = Math.max(len1, len2);
  return (maxLen - matrix[len2][len1]) / maxLen;
};

const areMemoriesSimilar = (memory1: UserMemory, memory2: UserMemory): boolean => {
  // Must be same category
  if (memory1.category !== memory2.category) return false;
  
  // Calculate key similarity (normalize to lowercase and remove underscores)
  const normalizeKey = (key: string) => key.toLowerCase().replace(/_/g, ' ');
  const keySimilarity = calculateSimilarity(
    normalizeKey(memory1.key), 
    normalizeKey(memory2.key)
  );
  
  // Calculate value similarity (normalize to lowercase)
  const valueSimilarity = calculateSimilarity(
    memory1.value.toLowerCase(), 
    memory2.value.toLowerCase()
  );
  
  // Consider similar if key similarity > 0.6 OR value similarity > 0.7
  const isKeySimilar = keySimilarity > 0.6;
  const isValueSimilar = valueSimilarity > 0.7;
  
  return isKeySimilar || isValueSimilar;
};

describe('Memory Deduplication', () => {
  describe('calculateSimilarity', () => {
    it('should return 1.0 for identical strings', () => {
      expect(calculateSimilarity('hello', 'hello')).toBe(1.0);
    });

    it('should return 0.0 for completely different strings', () => {
      const similarity = calculateSimilarity('abc', 'xyz');
      expect(similarity).toBe(0.0);
    });

    it('should return high similarity for similar strings', () => {
      const similarity = calculateSimilarity('pet_injury', 'pet_health');
      expect(similarity).toBeGreaterThan(0.3);
    });

    it('should handle empty strings', () => {
      expect(calculateSimilarity('', '')).toBe(1.0);
      expect(calculateSimilarity('hello', '')).toBe(0.0);
      expect(calculateSimilarity('', 'hello')).toBe(0.0);
    });
  });

  describe('areMemoriesSimilar', () => {
    it('should detect similar memories with similar keys', () => {
      const memory1: UserMemory = {
        key: 'pet_injury',
        value: 'Enzo hurt his paw',
        category: 'event'
      };

      const memory2: UserMemory = {
        key: 'pet_health_concern',
        value: 'Enzo has a health issue',
        category: 'event'
      };

      expect(areMemoriesSimilar(memory1, memory2)).toBe(true);
    });

    it('should detect similar memories with similar values', () => {
      const memory1: UserMemory = {
        key: 'work_stress',
        value: 'feeling overwhelmed about quarterly reviews',
        category: 'emotion'
      };

      const memory2: UserMemory = {
        key: 'work_concern',
        value: 'feeling overwhelmed about quarterly review process',
        category: 'emotion'
      };

      expect(areMemoriesSimilar(memory1, memory2)).toBe(true);
    });

    it('should not consider memories similar if different categories', () => {
      const memory1: UserMemory = {
        key: 'pet',
        value: 'Enzo the cat',
        category: 'fact'
      };

      const memory2: UserMemory = {
        key: 'pet',
        value: 'Enzo the cat',
        category: 'emotion'
      };

      expect(areMemoriesSimilar(memory1, memory2)).toBe(false);
    });

    it('should not consider completely different memories similar', () => {
      const memory1: UserMemory = {
        key: 'pet',
        value: 'Enzo the cat',
        category: 'fact'
      };

      const memory2: UserMemory = {
        key: 'work_location',
        value: 'Remote office in San Francisco',
        category: 'fact'
      };

      expect(areMemoriesSimilar(memory1, memory2)).toBe(false);
    });

    it('should handle underscore normalization in keys', () => {
      const memory1: UserMemory = {
        key: 'work_stress',
        value: 'feeling stressed',
        category: 'emotion'
      };

      const memory2: UserMemory = {
        key: 'work stress',
        value: 'feeling anxious',
        category: 'emotion'
      };

      expect(areMemoriesSimilar(memory1, memory2)).toBe(true);
    });

    it('should be case insensitive', () => {
      const memory1: UserMemory = {
        key: 'Pet',
        value: 'ENZO THE CAT',
        category: 'fact'
      };

      const memory2: UserMemory = {
        key: 'pet',
        value: 'enzo the cat',
        category: 'fact'
      };

      expect(areMemoriesSimilar(memory1, memory2)).toBe(true);
    });
  });

  describe('Real-world duplicate scenarios', () => {
    it('should detect duplicates from the screenshot examples', () => {
      const testCases = [
        {
          memory1: { key: 'pet_injury', value: 'Enzo hurt his paw', category: 'event' as const },
          memory2: { key: 'pet_health_concern', value: 'Enzo has health issues', category: 'event' as const },
          shouldBeSimilar: true
        },
        {
          memory1: { key: 'work_stress', value: 'feeling overwhelmed at work', category: 'emotion' as const },
          memory2: { key: 'work_concern', value: 'stressed about work deadlines', category: 'emotion' as const },
          shouldBeSimilar: true
        },
        {
          memory1: { key: 'emotional_response', value: 'felt sad today', category: 'emotion' as const },
          memory2: { key: 'emotional_state', value: 'feeling down', category: 'emotion' as const },
          shouldBeSimilar: true
        },
        {
          memory1: { key: 'pet', value: 'Enzo the cat', category: 'fact' as const },
          memory2: { key: 'work_location', value: 'San Francisco office', category: 'fact' as const },
          shouldBeSimilar: false
        }
      ];

      testCases.forEach(({ memory1, memory2, shouldBeSimilar }, index) => {
        const result = areMemoriesSimilar(memory1, memory2);
        expect(result).toBe(shouldBeSimilar, 
          `Test case ${index + 1}: Expected ${shouldBeSimilar} but got ${result} for memories: ${memory1.key} vs ${memory2.key}`
        );
      });
    });
  });
});
