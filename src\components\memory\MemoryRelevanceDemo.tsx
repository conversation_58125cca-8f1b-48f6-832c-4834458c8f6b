/**
 * Memory Relevance Demo Component
 * Demonstrates the use of relevance-based memory retrieval
 */

import React, { useState, useEffect } from 'react';
import { useMemoryContext } from '@/hooks/useMemoryContext';
import { RelevanceRetrievalOptions } from '@/services/memoryRelevanceService';

interface MemoryRelevanceDemoProps {
  initialQuery?: string;
}

export const MemoryRelevanceDemo: React.FC<MemoryRelevanceDemoProps> = ({ initialQuery = '' }) => {
  const [query, setQuery] = useState(initialQuery);
  const [isLoading, setIsLoading] = useState(false);
  const [options, setOptions] = useState<RelevanceRetrievalOptions>({
    minImportance: 4,
    minRelevance: 0.2,
    relevanceWeight: 0.6,
    importanceWeight: 0.4,
    limit: 10,
  });
  
  const { 
    relevantMemories,
    getRelevantMemoriesForQuery,
    getRelevantMemoriesForContext
  } = useMemoryContext();

  // Search for relevant memories
  const handleSearch = async () => {
    if (!query.trim()) return;
    
    setIsLoading(true);
    try {
      await getRelevantMemoriesForQuery(query, options);
    } catch (error) {
      console.error('Error searching for relevant memories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle option changes
  const handleOptionChange = (key: keyof RelevanceRetrievalOptions, value: number) => {
    setOptions(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Memory Relevance Search</h2>
      
      {/* Search input */}
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search your memories..."
          className="flex-1 p-2 border rounded"
        />
        <button
          onClick={handleSearch}
          disabled={isLoading || !query.trim()}
          className="px-4 py-2 bg-blue-600 text-white rounded disabled:bg-blue-300"
        >
          {isLoading ? 'Searching...' : 'Search'}
        </button>
      </div>
      
      {/* Options */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-1">
            Min Importance (1-10)
          </label>
          <input
            type="range"
            min="1"
            max="10"
            step="1"
            value={options.minImportance || 4}
            onChange={(e) => handleOptionChange('minImportance', parseInt(e.target.value))}
            className="w-full"
          />
          <div className="text-xs text-gray-500">{options.minImportance}</div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            Min Relevance (0-1)
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.05"
            value={options.minRelevance || 0.2}
            onChange={(e) => handleOptionChange('minRelevance', parseFloat(e.target.value))}
            className="w-full"
          />
          <div className="text-xs text-gray-500">{options.minRelevance?.toFixed(2)}</div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            Relevance Weight (0-1)
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.05"
            value={options.relevanceWeight || 0.6}
            onChange={(e) => handleOptionChange('relevanceWeight', parseFloat(e.target.value))}
            className="w-full"
          />
          <div className="text-xs text-gray-500">{options.relevanceWeight?.toFixed(2)}</div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">
            Importance Weight (0-1)
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.05"
            value={options.importanceWeight || 0.4}
            onChange={(e) => handleOptionChange('importanceWeight', parseFloat(e.target.value))}
            className="w-full"
          />
          <div className="text-xs text-gray-500">{options.importanceWeight?.toFixed(2)}</div>
        </div>
      </div>
      
      {/* Results */}
      <div>
        <h3 className="font-medium mb-2">Results ({relevantMemories.length})</h3>
        
        {relevantMemories.length === 0 ? (
          <div className="text-gray-500 italic">No relevant memories found</div>
        ) : (
          <div className="space-y-2">
            {relevantMemories.map((memory, index) => (
              <div key={memory.key + index} className="border p-3 rounded">
                <div className="flex justify-between">
                  <span className="font-medium">{memory.key}</span>
                  <div className="text-sm">
                    <span className="mr-2 px-2 py-0.5 bg-blue-100 rounded">
                      Relevance: {(memory.relevance * 100).toFixed(0)}%
                    </span>
                    <span className="px-2 py-0.5 bg-green-100 rounded">
                      Importance: {memory.importance || 5}/10
                    </span>
                  </div>
                </div>
                <p className="text-gray-700 mt-1">{memory.value}</p>
                <div className="text-xs text-gray-500 mt-1">
                  Category: {memory.category}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MemoryRelevanceDemo;
