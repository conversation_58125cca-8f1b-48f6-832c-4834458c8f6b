/**
 * Secure Configuration Utilities
 * Helper functions for handling sensitive configuration data securely
 */

import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * Mask sensitive values for logging
 */
export const maskSensitiveValue = (value: string, visibleChars: number = 4): string => {
  if (!value || value.length <= visibleChars) {
    return '***';
  }

  const visible = value.slice(0, visibleChars);
  const masked = '*'.repeat(Math.max(value.length - visibleChars, 3));
  return `${visible}${masked}`;
};

/**
 * Validate API key format
 */
export const validateApiKey = (apiKey: string, expectedPrefix?: string): boolean => {
  if (!apiKey || typeof apiKey !== 'string') {
    return false;
  }

  // Check minimum length
  if (apiKey.length < 10) {
    return false;
  }

  // Check expected prefix if provided
  if (expectedPrefix && !apiKey.startsWith(expectedPrefix)) {
    return false;
  }

  return true;
};

/**
 * Validate URL format
 */
export const validateUrl = (url: string, requiredProtocol?: 'http' | 'https'): boolean => {
  try {
    const urlObj = new URL(url);

    if (requiredProtocol && urlObj.protocol !== `${requiredProtocol}:`) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
};

/**
 * Get secure configuration summary for logging
 */
export const getConfigSummary = () => {
  const env = getEnvironmentConfig();

  return {
    environment: env.environment,
    app: {
      name: env.app.name,
      version: env.app.version,
    },
    supabase: {
      url: env.supabase.url ? maskSensitiveValue(env.supabase.url, 20) : 'NOT_SET',
      anonKey: env.supabase.anonKey ? maskSensitiveValue(env.supabase.anonKey, 8) : 'NOT_SET',
    },
    ai: {
      geminiApiKey: env.ai.geminiApiKey ? maskSensitiveValue(env.ai.geminiApiKey, 6) : 'NOT_SET',
      enabled: env.ai.enabled,
    },
    features: env.features,
    security: env.security,
  };
};

/**
 * Validate all sensitive configuration
 */
export const validateSensitiveConfig = (): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const env = getEnvironmentConfig();
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate Supabase URL
  if (!validateUrl(env.supabase.url, 'https')) {
    errors.push('Supabase URL is invalid or not using HTTPS');
  }

  // Validate Supabase key
  if (!validateApiKey(env.supabase.anonKey, 'eyJ')) {
    errors.push('Supabase anonymous key appears to be invalid');
  }

  // Validate Gemini API key (if AI features are enabled)
  if (env.features.aiEnabled) {
    if (!env.ai.geminiApiKey) {
      warnings.push('AI features are enabled but no Gemini API key is configured');
    } else if (!validateApiKey(env.ai.geminiApiKey, 'AIza')) {
      errors.push('Gemini API key appears to be invalid');
    }
  }

  // Security warnings
  if (env.isProduction) {
    if (env.features.debugMode) {
      warnings.push('Debug mode is enabled in production environment');
    }

    if (!env.security.httpsOnly) {
      warnings.push('HTTPS-only mode is disabled in production environment');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Initialize secure configuration with validation
 */
export const initializeSecureConfig = (): void => {
  const validation = validateSensitiveConfig();
  const summary = getConfigSummary();

  console.log('🔒 Secure configuration initialized:', summary);

  if (validation.warnings.length > 0) {
    console.warn('⚠️ Configuration warnings:', validation.warnings);
  }

  if (!validation.isValid) {
    console.error('❌ Configuration errors:', validation.errors);

    // In development, provide helpful error messages
    if (summary.environment === 'development') {
      console.error(
        '💡 To fix configuration errors:\n' +
          '1. Check your .env file exists and contains all required variables\n' +
          '2. Ensure API keys are valid and properly formatted\n' +
          '3. Verify URLs are using HTTPS protocol\n' +
          '4. Restart your development server after making changes'
      );
    }
  }
};

/**
 * Runtime configuration check for critical services
 */
export const checkCriticalServices = (): {
  supabase: boolean;
  ai: boolean;
  overall: boolean;
} => {
  const env = getEnvironmentConfig();

  const supabaseOk = !!(env.supabase.url && env.supabase.anonKey);
  const aiOk = !env.features.aiEnabled || !!env.ai.geminiApiKey;

  return {
    supabase: supabaseOk,
    ai: aiOk,
    overall: supabaseOk && aiOk,
  };
};

/**
 * Get environment-specific security headers
 */
export const getSecurityHeaders = (): Record<string, string> => {
  const env = getEnvironmentConfig();
  const headers: Record<string, string> = {};

  // Add security headers based on environment
  if (env.security.httpsOnly) {
    headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains';
  }

  if (env.security.strictCSP) {
    headers['Content-Security-Policy'] =
      "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      "connect-src 'self' https://rgcrjnljnqdnhmntouso.supabase.co https://generativelanguage.googleapis.com;";
  }

  // Add application info
  headers['X-App-Name'] = env.app.name;
  headers['X-App-Version'] = env.app.version;
  headers['X-Environment'] = env.environment;

  return headers;
};
