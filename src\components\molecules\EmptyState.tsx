/**
 * Empty State Molecule
 * Reusable component for displaying empty states with icon, message, and action
 */

import { Button } from '@/components/ui/button';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';
import { LucideIcon } from 'lucide-react';

interface EmptyStateProps extends BaseComponentProps {
  /** Icon to display */
  icon: LucideIcon;
  /** Main heading */
  title: string;
  /** Description text */
  description: string;
  /** Action button text */
  actionText?: string;
  /** Action button handler */
  onAction?: () => void;
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Icon color */
  iconColor?: string;
}

const sizeClasses = {
  sm: {
    container: 'py-8',
    icon: 'w-12 h-12',
    title: 'text-lg',
    description: 'text-sm',
  },
  md: {
    container: 'py-12',
    icon: 'w-16 h-16',
    title: 'text-xl',
    description: 'text-base',
  },
  lg: {
    container: 'py-16',
    icon: 'w-20 h-20',
    title: 'text-2xl',
    description: 'text-lg',
  },
};

export const EmptyState = ({
  icon: Icon,
  title,
  description,
  actionText,
  onAction,
  size = 'md',
  iconColor = 'text-amber-300',
  className,
  testId,
}: EmptyStateProps) => {
  const classes = sizeClasses[size];

  return (
    <div className={cn('text-center', classes.container, className)} data-testid={testId}>
      <Icon className={cn(classes.icon, iconColor, 'mx-auto mb-4')} />
      <h3 className={cn('font-semibold mb-2', classes.title)}>{title}</h3>
      <p className={cn('text-muted-foreground mb-6', classes.description)}>{description}</p>
      {actionText && onAction && (
        <Button onClick={onAction} className="bg-amber-500 hover:bg-amber-600 text-white">
          {actionText}
        </Button>
      )}
    </div>
  );
};
