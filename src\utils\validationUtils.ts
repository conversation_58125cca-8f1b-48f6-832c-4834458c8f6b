/**
 * Form Validation Utilities
 * Reusable validation utilities and patterns for forms across the application
 * Consolidates validation logic from journal entry forms, auth forms, and other components
 */

import { EmotionType, MoodScore } from '@/types';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Field validation rule interface
 */
export interface ValidationRule<T = any> {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: T) => string | null;
}

/**
 * Validation schema interface
 */
export type ValidationSchema<T> = {
  [K in keyof T]?: ValidationRule<T[K]>;
};

/**
 * Common validation patterns
 */
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-()]+$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s]+$/,
} as const;

/**
 * Common validation messages
 */
export const VALIDATION_MESSAGES = {
  required: (field: string) => `${field} is required`,
  minLength: (field: string, min: number) => `${field} must be at least ${min} characters`,
  maxLength: (field: string, max: number) => `${field} must be no more than ${max} characters`,
  email: 'Please enter a valid email address',
  pattern: (field: string) => `${field} format is invalid`,
  custom: (message: string) => message,
} as const;

/**
 * Validate a single field against rules
 */
export const validateField = <T>(
  value: T,
  rules: ValidationRule<T>,
  fieldName: string
): string | null => {
  // Required validation
  if (rules.required) {
    if (value === null || value === undefined || value === '') {
      return VALIDATION_MESSAGES.required(fieldName);
    }
    if (typeof value === 'string' && !value.trim()) {
      return VALIDATION_MESSAGES.required(fieldName);
    }
  }

  // Skip other validations if value is empty and not required
  if (!rules.required && (value === null || value === undefined || value === '')) {
    return null;
  }

  const stringValue = String(value);

  // Min length validation
  if (rules.minLength && stringValue.length < rules.minLength) {
    return VALIDATION_MESSAGES.minLength(fieldName, rules.minLength);
  }

  // Max length validation
  if (rules.maxLength && stringValue.length > rules.maxLength) {
    return VALIDATION_MESSAGES.maxLength(fieldName, rules.maxLength);
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    return VALIDATION_MESSAGES.pattern(fieldName);
  }

  // Custom validation
  if (rules.custom) {
    const customError = rules.custom(value);
    if (customError) {
      return customError;
    }
  }

  return null;
};

/**
 * Validate an object against a schema
 */
export const validateSchema = <T extends Record<string, any>>(
  data: T,
  schema: ValidationSchema<T>
): ValidationResult => {
  const errors: Record<string, string> = {};

  for (const [field, rules] of Object.entries(schema)) {
    if (rules) {
      const error = validateField(data[field], rules, field);
      if (error) {
        errors[field] = error;
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Journal entry validation schema
 */
export const journalEntryValidationSchema: ValidationSchema<{
  title: string;
  content: string;
  emotion: EmotionType | '';
  moodScore: MoodScore;
}> = {
  title: {
    required: true,
    minLength: 1,
    maxLength: 200,
    custom: (value: string) => {
      if (value && !value.trim()) {
        return 'Title cannot be empty or just whitespace';
      }
      return null;
    },
  },
  content: {
    required: true,
    minLength: 10,
    maxLength: 10000,
    custom: (value: string) => {
      if (value && !value.trim()) {
        return 'Content cannot be empty or just whitespace';
      }
      return null;
    },
  },
  emotion: {
    required: true,
    custom: (value: EmotionType | '') => {
      if (!value) {
        return 'Please select how you\'re feeling';
      }
      return null;
    },
  },
  moodScore: {
    custom: (value: MoodScore) => {
      if (value < 1 || value > 10) {
        return 'Mood score must be between 1 and 10';
      }
      return null;
    },
  },
};

/**
 * Authentication validation schemas
 */
export const signInValidationSchema: ValidationSchema<{
  email: string;
  password: string;
}> = {
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
    custom: (value: string) => {
      if (value && !VALIDATION_PATTERNS.email.test(value)) {
        return VALIDATION_MESSAGES.email;
      }
      return null;
    },
  },
  password: {
    required: true,
    minLength: 1,
  },
};

export const signUpValidationSchema: ValidationSchema<{
  email: string;
  password: string;
  fullName?: string;
  confirmPassword?: string;
}> = {
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
    custom: (value: string) => {
      if (value && !VALIDATION_PATTERNS.email.test(value)) {
        return VALIDATION_MESSAGES.email;
      }
      return null;
    },
  },
  password: {
    required: true,
    minLength: 6,
    maxLength: 128,
  },
  fullName: {
    required: true,
    minLength: 1,
    maxLength: 100,
    custom: (value?: string) => {
      if (value !== undefined && !value.trim()) {
        return 'Full name cannot be empty or just whitespace';
      }
      return null;
    },
  },
  confirmPassword: {
    required: true,
    custom: (value?: string, data?: any) => {
      if (data && value !== data.password) {
        return 'Passwords do not match';
      }
      return null;
    },
  },
};

/**
 * Validate journal entry data
 */
export const validateJournalEntry = (data: {
  title: string;
  content: string;
  emotion: EmotionType | '';
  moodScore: MoodScore;
}): ValidationResult => {
  return validateSchema(data, journalEntryValidationSchema);
};

/**
 * Validate sign-in data
 */
export const validateSignIn = (data: {
  email: string;
  password: string;
}): ValidationResult => {
  return validateSchema(data, signInValidationSchema);
};

/**
 * Validate sign-up data
 */
export const validateSignUp = (data: {
  email: string;
  password: string;
  fullName?: string;
  confirmPassword?: string;
}): ValidationResult => {
  // First validate individual fields
  const result = validateSchema(data, signUpValidationSchema);

  // Then validate password confirmation
  if (data.confirmPassword !== data.password) {
    result.errors.confirmPassword = 'Passwords do not match';
    result.isValid = false;
  }

  return result;
};

/**
 * Real-time validation hook for forms
 */
export const useFormValidation = <T extends Record<string, any>>(
  schema: ValidationSchema<T>
) => {
  const validateField = (field: keyof T, value: any, allData?: T): string | null => {
    const rules = schema[field];
    if (!rules) return null;

    return validateField(value, rules, String(field));
  };

  const validateAll = (data: T): ValidationResult => {
    return validateSchema(data, schema);
  };

  return {
    validateField,
    validateAll,
  };
};

/**
 * Debounced validation for real-time feedback
 */
export const createDebouncedValidator = <T>(
  validator: (data: T) => ValidationResult,
  delay: number = 300
) => {
  let timeout: NodeJS.Timeout;

  return (data: T, callback: (result: ValidationResult) => void) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      const result = validator(data);
      callback(result);
    }, delay);
  };
};

/**
 * Common field validators
 */
export const fieldValidators = {
  email: (value: string): string | null => {
    if (!value) return 'Email is required';
    if (!VALIDATION_PATTERNS.email.test(value)) return VALIDATION_MESSAGES.email;
    return null;
  },

  password: (value: string, minLength: number = 6): string | null => {
    if (!value) return 'Password is required';
    if (value.length < minLength) return `Password must be at least ${minLength} characters`;
    return null;
  },

  required: (value: any, fieldName: string): string | null => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return `${fieldName} is required`;
    }
    return null;
  },

  minLength: (value: string, min: number, fieldName: string): string | null => {
    if (value && value.length < min) {
      return `${fieldName} must be at least ${min} characters`;
    }
    return null;
  },

  maxLength: (value: string, max: number, fieldName: string): string | null => {
    if (value && value.length > max) {
      return `${fieldName} must be no more than ${max} characters`;
    }
    return null;
  },
};
