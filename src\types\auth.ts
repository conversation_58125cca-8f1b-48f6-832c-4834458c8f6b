/**
 * Authentication Types
 * TypeScript interfaces for user authentication and session management
 */

import { User, Session } from '@supabase/supabase-js';

/**
 * User profile information
 */
export interface UserProfile {
  /** User's unique identifier */
  id: string;
  /** User's email address */
  email: string;
  /** User's display name */
  full_name?: string;
  /** User's avatar URL */
  avatar_url?: string;
  /** User's timezone */
  timezone?: string;
  /** User's preferred language */
  language?: string;
  /** Account creation date */
  created_at: string;
  /** Last profile update */
  updated_at: string;
}

/**
 * Authentication context type
 */
export interface AuthContextType {
  /** Current authenticated user */
  user: User | null;
  /** Current session */
  session: Session | null;
  /** Loading state for auth operations */
  loading: boolean;
  /** Sign out function */
  signOut: () => Promise<void>;
}

/**
 * Sign up form data
 */
export interface SignUpFormData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
}

/**
 * Sign in form data
 */
export interface SignInFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Password reset form data
 */
export interface PasswordResetFormData {
  email: string;
}

/**
 * Password update form data
 */
export interface PasswordUpdateFormData {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

/**
 * Profile update form data
 */
export interface ProfileUpdateFormData {
  fullName?: string;
  timezone?: string;
  language?: string;
}

/**
 * Authentication error types
 */
export type AuthErrorType =
  | 'invalid_credentials'
  | 'email_not_confirmed'
  | 'user_not_found'
  | 'weak_password'
  | 'email_already_exists'
  | 'network_error'
  | 'unknown_error';

/**
 * Authentication error interface
 */
export interface AuthError {
  type: AuthErrorType;
  message: string;
  details?: string;
}

/**
 * Authentication response interface
 */
export interface AuthResponse<T = any> {
  success: boolean;
  data?: T;
  error?: AuthError;
}

/**
 * Session state for persistence
 */
export interface SessionState {
  user: User | null;
  session: Session | null;
  lastActivity: string;
  expiresAt: string;
}
