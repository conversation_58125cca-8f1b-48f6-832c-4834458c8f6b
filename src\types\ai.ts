/**
 * AI Integration Types
 * TypeScript interfaces for AI services and responses
 */

/**
 * Input data for AI reflection generation
 */
export interface AIReflectionInput {
  /** Journal entry title */
  title: string;
  /** Journal entry content */
  content: string;
  /** User-selected emotion */
  emotion: string;
  /** User-rated mood score (1-10) */
  moodScore: number;
}

/**
 * OpenAI Chat API message format for local LLM
 */
export interface LLMMessage {
  /** Message role */
  role: 'system' | 'user' | 'assistant';
  /** Message content */
  content: string;
}

/**
 * Raw response from Gemini AI API (legacy)
 * @deprecated Use LocalLLMResponse for new implementations
 */
export interface GeminiAIResponse {
  /** Brief, empathetic summary of the main themes */
  summary: string;
  /** Primary emotion detected by AI */
  emotion: string;
  /** Warm, supportive message that validates feelings */
  encouragement: string;
  /** Gentle, open-ended question to help further reflection */
  reflection_question: string;
}

/**
 * Raw response from Local LLM API
 */
export interface LocalLLMResponse {
  /** Brief, empathetic summary of the main themes */
  summary: string;
  /** Primary emotion detected by AI */
  emotion: string;
  /** Warm, supportive message that validates feelings */
  encouragement: string;
  /** Gentle, open-ended question to help further reflection */
  reflection_question: string;
}

/**
 * Unified AI response interface (supports both Gemini and Local LLM)
 */
export interface AIResponse {
  /** Brief, empathetic summary of the main themes */
  summary: string;
  /** Primary emotion detected by AI */
  emotion: string;
  /** Warm, supportive message that validates feelings */
  encouragement: string;
  /** Gentle, open-ended question to help further reflection */
  reflection_question: string;
}

/**
 * Processed AI reflection response
 */
export interface AIReflectionResponse {
  /** Brief, empathetic summary of the main themes */
  summary: string;
  /** Primary emotion detected by AI */
  emotion: string;
  /** Warm, supportive message that validates feelings */
  encouragement: string;
  /** Gentle, open-ended question to help further reflection */
  reflection_question: string;
  /** Legacy field for backward compatibility */
  reflection: string;
  /** Success status of the AI operation */
  success: boolean;
  /** Error message if operation failed */
  error?: string;
}

/**
 * AI service configuration
 */
export interface AIServiceConfig {
  /** API key (optional for local LLM) */
  apiKey?: string;
  /** AI model to use */
  model: string;
  /** Maximum retry attempts */
  maxRetries: number;
  /** Base delay for retry backoff (ms) */
  baseDelay: number;
  /** Request timeout (ms) */
  timeout: number;
  /** Service type */
  serviceType: 'gemini' | 'local-llm';
  /** Local LLM endpoint URL */
  localLLMEndpoint?: string;
}

/**
 * AI prompt template configuration
 */
export interface AIPromptTemplate {
  /** Template name/identifier */
  name: string;
  /** Prompt template string with placeholders */
  template: string;
  /** Required variables for the template */
  variables: string[];
  /** Optional description of the template */
  description?: string;
}

/**
 * Mock AI response templates for fallback
 */
export interface MockAIResponseTemplate {
  /** Template identifier matching emotion types */
  emotion: string;
  /** Mock summary response */
  summary: string;
  /** Mock detected emotion */
  detectedEmotion: string;
  /** Mock encouragement message */
  encouragement: string;
  /** Mock reflection question */
  reflection_question: string;
}

/**
 * AI service error types
 */
export type AIErrorType =
  | 'api_key_invalid'
  | 'quota_exceeded'
  | 'model_not_found'
  | 'network_error'
  | 'timeout_error'
  | 'invalid_response'
  | 'parsing_error'
  | 'unknown_error';

/**
 * AI service error interface
 */
export interface AIError {
  type: AIErrorType;
  message: string;
  details?: string;
  retryable: boolean;
}

/**
 * AI service response wrapper
 */
export interface AIServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: AIError;
  source: 'ai' | 'fallback';
  processingTime?: number;
}

/**
 * AI analytics and usage tracking
 */
export interface AIUsageStats {
  /** Total AI requests made */
  totalRequests: number;
  /** Successful AI responses */
  successfulResponses: number;
  /** Failed requests that used fallback */
  fallbackResponses: number;
  /** Average response time (ms) */
  averageResponseTime: number;
  /** Most recent request timestamp */
  lastRequestAt: string;
  /** Daily usage count */
  dailyUsage: number;
  /** Monthly usage count */
  monthlyUsage: number;
}

/**
 * AI feature flags and configuration
 */
export interface AIFeatureConfig {
  /** Whether AI reflection is enabled */
  reflectionEnabled: boolean;
  /** Whether fallback responses are enabled */
  fallbackEnabled: boolean;
  /** Whether to show AI source to users */
  showAISource: boolean;
  /** Whether to collect usage analytics */
  analyticsEnabled: boolean;
  /** Custom prompt templates */
  customPrompts?: AIPromptTemplate[];
}

/**
 * Memory extraction categories
 */
export type MemoryCategory = 'fact' | 'emotion' | 'event' | 'goal' | 'preference' | 'identity';

/**
 * Memory object structure for extracted user information
 */
export interface UserMemory {
  /** Concise 1-3 word label (e.g., "pet", "work_stress", "language_goal") */
  key: string;
  /** Specific memory content (e.g., "Enzo the cat", "feeling overwhelmed about quarterly reviews") */
  value: string;
  /** Memory category classification */
  category: MemoryCategory;
  /** Memory importance score from 1 (not important) to 10 (highly important), default 5 */
  importance?: number;
  /** Semantic embedding vector for similarity search (384 dimensions) */
  embedding?: number[] | string | null;
}

/**
 * Input for memory extraction from content
 */
export interface MemoryExtractionInput {
  /** Source type of the content */
  sourceType: 'journal_entry' | 'conversation';
  /** Content to extract memories from */
  content: string;
}

/**
 * Response from memory extraction service
 */
export interface MemoryExtractionResponse {
  /** Array of extracted memory objects */
  memories: UserMemory[];
  /** Success status of the extraction */
  success: boolean;
  /** Error message if extraction failed */
  error?: string;
  /** Processing time in milliseconds */
  processingTime?: number;
}
