/**
 * Journal Entries Presenter
 * Pure presentation component for displaying journal entries using atomic design
 */

import { JournalEntriesList } from '@/components/organisms/JournalEntriesList';
import { FormattedJournalEntry } from '@/types';
import { useServiceWithLoading } from '@/hooks/useServiceWithLoading';

interface JournalEntriesPresenterProps {
  /** Array of journal entries to display */
  entries: FormattedJournalEntry[];
  /** Loading state from service integration */
  loadingState: ReturnType<typeof useServiceWithLoading>['loadingState'];
  /** Handler for creating new entry */
  onCreateEntry: () => void;
  /** Handler for entry actions */
  onEntryAction?: (entryId: string, action: 'edit' | 'delete') => void;
  /** Pagination props */
  pagination?: {
    /** Whether there are more entries to load */
    hasMore: boolean;
    /** Whether load more is in progress */
    isLoadingMore: boolean;
    /** Handler for loading more entries */
    onLoadMore: () => void;
    /** Total number of loaded entries */
    loadedCount?: number;
    /** Total number of available entries */
    totalCount?: number;
  };
}

/**
 * Main journal entries presenter component
 */
export const JournalEntriesPresenter = ({
  entries,
  loadingState,
  onCreateEntry,
  onEntryAction,
  pagination,
}: JournalEntriesPresenterProps) => {
  const handleEditEntry = (entryId: string) => {
    if (onEntryAction) onEntryAction(entryId, 'edit');
  };

  const handleDeleteEntry = (entryId: string) => {
    if (onEntryAction) onEntryAction(entryId, 'delete');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <JournalEntriesList
          entries={entries}
          loadingState={loadingState}
          showEntryActions={!!onEntryAction}
          layout="list"
          entrySize="md"
          onCreateEntry={onCreateEntry}
          onEditEntry={handleEditEntry}
          onDeleteEntry={handleDeleteEntry}
          onRetry={() => window.location.reload()}
          pagination={pagination}
        />
      </div>
    </div>
  );
};
