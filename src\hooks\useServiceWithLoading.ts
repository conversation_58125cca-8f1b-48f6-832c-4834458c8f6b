/**
 * Service Integration Hook
 * Custom hook that integrates services with loading states and error handling
 */

import { useCallback, useState, useMemo } from 'react';
import { useLoadingState, withLoading } from '@/utils/loadingManager';
import { handleApiError, handleAuthError } from '@/utils/errorHandler';
import { ApiResponse, AuthResponse, ApiError, AuthError } from '@/types';

/**
 * Configuration for service operations
 */
interface ServiceOperationConfig {
  /** Operation identifier for loading tracking */
  operationId?: string;
  /** Loading message to display */
  loadingMessage?: string;
  /** Whether to show error toasts */
  showErrorToast?: boolean;
  /** Custom error message */
  customErrorMessage?: string;
  /** Whether operation is retryable */
  enableRetry?: boolean;
  /** Maximum retry attempts */
  maxRetries?: number;
}

/**
 * Hook for integrating services with loading and error handling
 */
export const useServiceWithLoading = (componentName: string = 'UnknownComponent') => {
  const loadingManager = useLoadingState();
  const [retryAttempts, setRetryAttempts] = useState<Record<string, number>>({});

  /**
   * Executes an API service function with loading and error handling
   */
  const executeApiService = useCallback(
    async <T>(
      serviceFn: () => Promise<ApiResponse<T>>,
      actionName: string,
      config: ServiceOperationConfig = {}
    ): Promise<T | null> => {
      const {
        operationId = actionName,
        loadingMessage,
        showErrorToast = true,
        customErrorMessage,
        enableRetry = false,
        maxRetries = 3,
      } = config;

      const wrappedService = withLoading(serviceFn, loadingManager, operationId, loadingMessage);

      try {
        const response = await wrappedService();

        if (response.success) {
          // Reset retry attempts on success
          setRetryAttempts(prev => ({ ...prev, [operationId]: 0 }));
          return response.data || null;
        } else {
          // Handle API error
          const currentAttempts = retryAttempts[operationId] || 0;
          const canRetry = enableRetry && currentAttempts < maxRetries && response.error?.retryable;

          if (showErrorToast) {
            await handleApiError(response.error as ApiError, componentName, actionName, {
              customMessage: customErrorMessage,
              retry: canRetry
                ? {
                    enabled: true,
                    maxAttempts: maxRetries,
                    onRetry: () => {
                      setRetryAttempts(prev => ({
                        ...prev,
                        [operationId]: currentAttempts + 1,
                      }));
                      executeApiService(serviceFn, actionName, config);
                    },
                  }
                : undefined,
            });
          }

          return null;
        }
      } catch (error) {
        // Handle unexpected errors
        if (showErrorToast) {
          await handleApiError(
            {
              code: 'UNEXPECTED_ERROR',
              message: error instanceof Error ? error.message : 'An unexpected error occurred',
              retryable: false,
            },
            componentName,
            actionName,
            { customMessage: customErrorMessage }
          );
        }
        return null;
      }
    },
    [loadingManager, retryAttempts, componentName]
  );

  /**
   * Executes an Auth service function with loading and error handling
   */
  const executeAuthService = useCallback(
    async <T>(
      serviceFn: () => Promise<AuthResponse<T>>,
      actionName: string,
      config: ServiceOperationConfig = {}
    ): Promise<T | null> => {
      const {
        operationId = actionName,
        loadingMessage,
        showErrorToast = true,
        customErrorMessage,
        enableRetry = false,
        maxRetries = 3,
      } = config;

      const wrappedService = withLoading(serviceFn, loadingManager, operationId, loadingMessage);

      try {
        const response = await wrappedService();

        if (response.success) {
          // Reset retry attempts on success
          setRetryAttempts(prev => ({ ...prev, [operationId]: 0 }));
          return response.data || null;
        } else {
          // Handle Auth error
          const currentAttempts = retryAttempts[operationId] || 0;
          const canRetry = enableRetry && currentAttempts < maxRetries;

          if (showErrorToast) {
            await handleAuthError(response.error as AuthError, componentName, actionName, {
              customMessage: customErrorMessage,
              retry: canRetry
                ? {
                    enabled: true,
                    maxAttempts: maxRetries,
                    onRetry: () => {
                      setRetryAttempts(prev => ({
                        ...prev,
                        [operationId]: currentAttempts + 1,
                      }));
                      executeAuthService(serviceFn, actionName, config);
                    },
                  }
                : undefined,
            });
          }

          return null;
        }
      } catch (error) {
        // Handle unexpected errors
        if (showErrorToast) {
          await handleAuthError(
            {
              type: 'unknown_error',
              message: error instanceof Error ? error.message : 'An unexpected error occurred',
            },
            componentName,
            actionName,
            { customMessage: customErrorMessage }
          );
        }
        return null;
      }
    },
    [loadingManager, retryAttempts, componentName]
  );

  /**
   * Creates a memoized service executor for a specific service function
   */
  const createServiceExecutor = useCallback(
    <T>(
      serviceFn: () => Promise<ApiResponse<T>>,
      actionName: string,
      defaultConfig: ServiceOperationConfig = {}
    ) => {
      return (overrideConfig: ServiceOperationConfig = {}) => {
        const finalConfig = { ...defaultConfig, ...overrideConfig };
        return executeApiService(serviceFn, actionName, finalConfig);
      };
    },
    [executeApiService]
  );

  /**
   * Creates a memoized auth service executor for a specific auth function
   */
  const createAuthExecutor = useCallback(
    <T>(
      serviceFn: () => Promise<AuthResponse<T>>,
      actionName: string,
      defaultConfig: ServiceOperationConfig = {}
    ) => {
      return (overrideConfig: ServiceOperationConfig = {}) => {
        const finalConfig = { ...defaultConfig, ...overrideConfig };
        return executeAuthService(serviceFn, actionName, finalConfig);
      };
    },
    [executeAuthService]
  );

  /**
   * Batch execute multiple service operations
   */
  const executeBatch = useCallback(
    async <T>(
      operations: Array<{
        serviceFn: () => Promise<ApiResponse<T>>;
        actionName: string;
        config?: ServiceOperationConfig;
      }>,
      batchConfig: {
        operationId?: string;
        loadingMessage?: string;
        failFast?: boolean; // Stop on first error
      } = {}
    ): Promise<Array<T | null>> => {
      const {
        operationId = 'batch_operation',
        loadingMessage = 'Processing...',
        failFast = false,
      } = batchConfig;

      loadingManager.startLoading(operationId, loadingMessage);

      try {
        const results: Array<T | null> = [];

        for (let i = 0; i < operations.length; i++) {
          const { serviceFn, actionName, config = {} } = operations[i];

          // Update progress
          const progress = ((i + 1) / operations.length) * 100;
          loadingManager.updateProgress(
            operationId,
            progress,
            `${loadingMessage} (${i + 1}/${operations.length})`
          );

          const result = await executeApiService(serviceFn, actionName, {
            ...config,
            showErrorToast: false, // Handle errors at batch level
          });

          results.push(result);

          // Stop on first error if failFast is enabled
          if (failFast && result === null) {
            break;
          }
        }

        return results;
      } finally {
        loadingManager.stopLoading(operationId);
      }
    },
    [loadingManager, executeApiService]
  );

  return useMemo(
    () => ({
      // Loading state
      loadingState: loadingManager.loadingState,

      // Service executors
      executeApiService,
      executeAuthService,
      createServiceExecutor,
      createAuthExecutor,
      executeBatch,

      // Loading control
      startLoading: loadingManager.startLoading,
      stopLoading: loadingManager.stopLoading,
      updateProgress: loadingManager.updateProgress,
      stopAllLoading: loadingManager.stopAllLoading,
      isOperationLoading: loadingManager.isOperationLoading,
      getActiveOperations: loadingManager.getActiveOperations,
    }),
    [
      loadingManager.loadingState,
      executeApiService,
      executeAuthService,
      createServiceExecutor,
      createAuthExecutor,
      executeBatch,
      loadingManager.startLoading,
      loadingManager.stopLoading,
      loadingManager.updateProgress,
      loadingManager.stopAllLoading,
      loadingManager.isOperationLoading,
      loadingManager.getActiveOperations,
    ]
  );
};
