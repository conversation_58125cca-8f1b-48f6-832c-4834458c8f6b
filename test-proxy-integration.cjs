/**
 * Test script to verify the Vite proxy integration with embedding service
 */

const fetch = require('node-fetch');

async function testProxyIntegration() {
    console.log('🔄 Testing Vite Proxy Integration with Embedding Service...\n');
    
    const baseUrl = 'http://localhost:8080/api/embedding';
    
    try {
        // Test 1: Health Check through proxy
        console.log('1. Testing health check through proxy...');
        const healthResponse = await fetch(`${baseUrl}/health`);
        
        if (healthResponse.ok) {
            const healthData = await healthResponse.json();
            console.log('✅ Health check successful');
            console.log(`   Service: ${healthData.service}`);
            console.log(`   Status: ${healthData.status}`);
            console.log(`   Model: ${healthData.model}`);
            console.log(`   Embedding Dimension: ${healthData.embedding_dimension}\n`);
        } else {
            console.log(`❌ Health check failed: ${healthResponse.status}\n`);
            return false;
        }
        
        // Test 2: Embedding generation through proxy
        console.log('2. Testing embedding generation through proxy...');
        const embedResponse = await fetch(`${baseUrl}/embed`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: 'I had a wonderful day hiking in the mountains. The fresh air and beautiful scenery made me feel peaceful and grateful.'
            })
        });
        
        if (embedResponse.ok) {
            const embedData = await embedResponse.json();
            console.log('✅ Embedding generation successful');
            console.log(`   Dimension: ${embedData.dimension}`);
            console.log(`   Text Length: ${embedData.text_length}`);
            console.log(`   Sample Values: [${embedData.embedding.slice(0, 3).map(v => v.toFixed(4)).join(', ')}...]`);
            console.log(`   Value Range: ${Math.min(...embedData.embedding).toFixed(4)} to ${Math.max(...embedData.embedding).toFixed(4)}\n`);
        } else {
            console.log(`❌ Embedding generation failed: ${embedResponse.status}\n`);
            return false;
        }
        
        // Test 3: Multiple requests to test performance
        console.log('3. Testing multiple requests for performance...');
        const testTexts = [
            'I love spending time with my family.',
            'Work was challenging but rewarding today.',
            'The sunset was absolutely beautiful.',
            'I feel grateful for all the good things in my life.',
            'Reading a good book always makes me happy.'
        ];
        
        const times = [];
        for (let i = 0; i < testTexts.length; i++) {
            const start = Date.now();
            const response = await fetch(`${baseUrl}/embed`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ text: testTexts[i] })
            });
            const end = Date.now();
            
            if (response.ok) {
                const duration = end - start;
                times.push(duration);
                console.log(`   Request ${i + 1}: ${duration}ms ✅`);
            } else {
                console.log(`   Request ${i + 1}: Failed ❌`);
            }
        }
        
        if (times.length > 0) {
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            console.log(`\n📊 Performance Summary:`);
            console.log(`   Average: ${avgTime.toFixed(1)}ms`);
            console.log(`   Min: ${Math.min(...times)}ms`);
            console.log(`   Max: ${Math.max(...times)}ms\n`);
        }
        
        console.log('🎉 All proxy integration tests passed!');
        console.log('✅ The embedding service is now accessible through the Vite proxy');
        console.log('✅ CORS issues have been resolved');
        console.log('✅ Ready for frontend integration\n');
        
        return true;
        
    } catch (error) {
        console.log(`❌ Test failed with error: ${error.message}\n`);
        return false;
    }
}

// Run the test
testProxyIntegration().then(success => {
    if (success) {
        console.log('🚀 Next steps:');
        console.log('1. Test the embedding service from the React app');
        console.log('2. Verify memory persistence with embeddings');
        console.log('3. Test semantic similarity in memory retrieval');
    } else {
        console.log('🔧 Please check the embedding service and Vite proxy configuration');
    }
});
