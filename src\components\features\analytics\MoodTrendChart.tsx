/**
 * Mood Trend Chart Component
 * Line chart showing mood trends over time with Recharts
 */

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, CartesianGrid } from 'recharts';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { MoodTrendData, AnalyticsTimeRange } from '@/types';
import { format, parseISO } from 'date-fns';
import { useIsMobile } from '@/hooks/use-mobile';

interface MoodTrendChartProps {
  data?: MoodTrendData[];
  timeRange: AnalyticsTimeRange;
  detailed?: boolean;
  isLoading?: boolean;
}

const chartConfig = {
  averageMood: {
    label: 'Average Mood',
    color: '#F59E0B', // Amber color for consistency
  },
  entryCount: {
    label: 'Entries',
    color: '#FB923C', // Orange color
  },
};

/**
 * Calculate trend direction from data
 */
const calculateTrend = (data: MoodTrendData[]): 'improving' | 'declining' | 'stable' => {
  if (!data || data.length < 2) return 'stable';

  const validData = data.filter(d => d.averageMood > 0);
  if (validData.length < 2) return 'stable';

  const firstHalf = validData.slice(0, Math.ceil(validData.length / 2));
  const secondHalf = validData.slice(Math.floor(validData.length / 2));

  const firstAvg = firstHalf.reduce((sum, d) => sum + d.averageMood, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((sum, d) => sum + d.averageMood, 0) / secondHalf.length;

  const difference = secondAvg - firstAvg;
  if (difference > 0.3) return 'improving';
  if (difference < -0.3) return 'declining';
  return 'stable';
};

/**
 * Format date for chart display based on time range
 */
const formatDateForChart = (dateStr: string, timeRange: AnalyticsTimeRange): string => {
  const date = parseISO(dateStr);
  
  switch (timeRange) {
    case '7d':
      return format(date, 'EEE'); // Mon, Tue, etc.
    case '30d':
      return format(date, 'MMM d'); // Jan 1, Jan 2, etc.
    case '90d':
      return format(date, 'MMM d'); // Jan 1, Feb 1, etc.
    case '1y':
      return format(date, 'MMM yyyy'); // Jan 2024, Feb 2024, etc.
    default:
      return format(date, 'MMM d');
  }
};

/**
 * Custom tooltip for mood trend chart
 */
const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload?.length) return null;

  const data = payload[0].payload;
  const date = parseISO(label);

  return (
    <div className="bg-white p-3 border border-amber-200 rounded-lg shadow-lg">
      <p className="font-medium text-gray-900 mb-2">
        {format(date, 'EEEE, MMMM d, yyyy')}
      </p>
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-amber-500"></div>
          <span className="text-sm">
            Average Mood: <span className="font-medium">{data.averageMood.toFixed(1)}/10</span>
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-orange-500"></div>
          <span className="text-sm">
            Entries: <span className="font-medium">{data.entryCount}</span>
          </span>
        </div>
        {data.dominantEmotion && (
          <div className="text-sm text-gray-600 mt-1">
            Dominant emotion: <span className="font-medium capitalize">{data.dominantEmotion}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export const MoodTrendChart = ({ data, timeRange, detailed = false, isLoading = false }: MoodTrendChartProps) => {
  const isMobile = useIsMobile();
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-amber-600" />
            Mood Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <div className="animate-pulse text-gray-500">Loading mood trend...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-amber-600" />
            Mood Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <TrendingUp className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No mood data available for the selected time range</p>
            <p className="text-sm mt-2">Start journaling to see your mood trends!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const trend = calculateTrend(data);
  const validData = data.filter(d => d.averageMood > 0);

  // Calculate some statistics for display
  const totalEntries = data.reduce((sum, d) => sum + d.entryCount, 0);
  const averageMood = validData.length > 0 
    ? validData.reduce((sum, d) => sum + d.averageMood, 0) / validData.length 
    : 0;

  return (
    <Card className={detailed ? 'col-span-full' : ''}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {trend === 'improving' && <TrendingUp className="w-5 h-5 text-green-600" />}
            {trend === 'declining' && <TrendingDown className="w-5 h-5 text-red-600" />}
            {trend === 'stable' && <Minus className="w-5 h-5 text-gray-600" />}
            Mood Trend
          </div>
          {detailed && (
            <div className="text-sm text-gray-600 space-y-1">
              <div>Average: {averageMood.toFixed(1)}/10</div>
              <div>Total Entries: {totalEntries}</div>
            </div>
          )}
        </CardTitle>
        <div className="text-sm text-gray-600">
          {trend === 'improving' && (
            <span className="text-green-600 font-medium">📈 Your mood is improving!</span>
          )}
          {trend === 'declining' && (
            <span className="text-red-600 font-medium">📉 Your mood has been declining</span>
          )}
          {trend === 'stable' && (
            <span className="text-gray-600 font-medium">📊 Your mood is stable</span>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className={`
            ${detailed ? "h-[500px]" : isMobile ? "h-[250px]" : "h-[300px]"}
            w-full
            [&_.recharts-responsive-container]:!aspect-auto
          `}
        >
          <LineChart
            data={data}
            margin={{
              top: 5,
              right: isMobile ? 10 : 30,
              left: isMobile ? 10 : 20,
              bottom: 5
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
            <XAxis
              dataKey="date"
              tickFormatter={(value) => formatDateForChart(value, timeRange)}
              stroke="#6b7280"
              fontSize={isMobile ? 10 : 12}
              interval={isMobile ? 'preserveStartEnd' : 'preserveStart'}
            />
            <YAxis
              domain={[1, 10]}
              stroke="#6b7280"
              fontSize={isMobile ? 10 : 12}
              label={isMobile ? undefined : { value: 'Mood Score', angle: -90, position: 'insideLeft' }}
            />
            <ChartTooltip content={<CustomTooltip />} />
            <Line
              type="monotone"
              dataKey="averageMood"
              stroke={chartConfig.averageMood.color}
              strokeWidth={3}
              dot={{ 
                fill: chartConfig.averageMood.color, 
                strokeWidth: 2, 
                r: 5,
                stroke: '#fff'
              }}
              activeDot={{ 
                r: 7, 
                stroke: chartConfig.averageMood.color,
                strokeWidth: 2,
                fill: '#fff'
              }}
              connectNulls={false}
            />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};
