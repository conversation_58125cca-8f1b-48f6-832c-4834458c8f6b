/**
 * API Types
 * TypeScript interfaces for API responses and service layer
 */

/**
 * Standard API response wrapper
 */
export interface ApiResponse<T = any> {
  /** Success status */
  success: boolean;
  /** Response data */
  data?: T;
  /** Error information */
  error?: ApiError;
  /** Response metadata */
  meta?: ApiMeta;
}

/**
 * API error interface
 */
export interface ApiError {
  /** Error code */
  code: string;
  /** Human-readable error message */
  message: string;
  /** Detailed error information */
  details?: string;
  /** Field-specific validation errors */
  fieldErrors?: Record<string, string[]>;
  /** Whether the error is retryable */
  retryable?: boolean;
}

/**
 * API response metadata
 */
export interface ApiMeta {
  /** Request timestamp */
  timestamp: string;
  /** Request ID for tracking */
  requestId?: string;
  /** API version */
  version?: string;
  /** Pagination information */
  pagination?: PaginationMeta;
  /** Rate limiting information */
  rateLimit?: RateLimitMeta;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  /** Current page number */
  page: number;
  /** Items per page */
  limit: number;
  /** Total number of items */
  total: number;
  /** Total number of pages */
  totalPages: number;
  /** Whether there are more pages */
  hasNext: boolean;
  /** Whether there are previous pages */
  hasPrevious: boolean;
}

/**
 * Rate limiting metadata
 */
export interface RateLimitMeta {
  /** Requests remaining in current window */
  remaining: number;
  /** Total requests allowed in window */
  limit: number;
  /** Window reset timestamp */
  resetAt: string;
  /** Retry after seconds (if rate limited) */
  retryAfter?: number;
}

/**
 * API request configuration
 */
export interface ApiRequestConfig {
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Number of retry attempts */
  retries?: number;
  /** Retry delay in milliseconds */
  retryDelay?: number;
  /** Whether to include authentication */
  authenticated?: boolean;
  /** Custom headers */
  headers?: Record<string, string>;
  /** Request cache configuration */
  cache?: CacheConfig;
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  /** Cache key */
  key: string;
  /** Cache TTL in seconds */
  ttl: number;
  /** Whether to use stale data while revalidating */
  staleWhileRevalidate?: boolean;
}

/**
 * Service method options
 */
export interface ServiceOptions extends ApiRequestConfig {
  /** Whether to show loading indicators */
  showLoading?: boolean;
  /** Whether to show error notifications */
  showErrors?: boolean;
  /** Success callback */
  onSuccess?: (data: any) => void;
  /** Error callback */
  onError?: (error: ApiError) => void;
}

/**
 * Database query options
 */
export interface QueryOptions {
  /** Fields to select */
  select?: string[];
  /** Filter conditions */
  where?: Record<string, any>;
  /** Sort configuration */
  orderBy?: {
    field: string;
    direction: 'asc' | 'desc';
  }[];
  /** Pagination */
  limit?: number;
  offset?: number;
  /** Include related data */
  include?: string[];
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult<T = any> {
  /** Successfully processed items */
  success: T[];
  /** Failed items with errors */
  failed: {
    item: T;
    error: ApiError;
  }[];
  /** Total items processed */
  total: number;
  /** Number of successful operations */
  successCount: number;
  /** Number of failed operations */
  failedCount: number;
}

/**
 * File upload configuration
 */
export interface FileUploadConfig {
  /** Maximum file size in bytes */
  maxSize: number;
  /** Allowed file types */
  allowedTypes: string[];
  /** Upload destination */
  destination: string;
  /** Whether to generate thumbnails */
  generateThumbnails?: boolean;
  /** Compression settings */
  compression?: {
    quality: number;
    maxWidth?: number;
    maxHeight?: number;
  };
}

/**
 * File upload result
 */
export interface FileUploadResult {
  /** Uploaded file URL */
  url: string;
  /** File name */
  filename: string;
  /** File size in bytes */
  size: number;
  /** File MIME type */
  mimeType: string;
  /** Thumbnail URL (if generated) */
  thumbnailUrl?: string;
  /** Upload metadata */
  metadata?: Record<string, any>;
}

/**
 * Health check response
 */
export interface HealthCheckResponse {
  /** Service status */
  status: 'healthy' | 'degraded' | 'unhealthy';
  /** Service version */
  version: string;
  /** Uptime in seconds */
  uptime: number;
  /** Dependency statuses */
  dependencies: {
    name: string;
    status: 'healthy' | 'unhealthy';
    responseTime?: number;
  }[];
  /** Additional metrics */
  metrics?: Record<string, number>;
}
