/**
 * Journal Service
 * Centralized service for journal entry operations with enhanced error handling
 */

import { supabase } from '@/integrations/supabase/client';
import {
  JournalEntry,
  CreateJournalEntryPayload,
  UpdateJournalEntryPayload,
  FormattedJournalEntry,
  JournalEntryFilters,
  JournalStats,
  ApiResponse,
  ApiError,
  supabaseToJournalEntry,
  journalEntryToSupabaseInsert,
  journalEntryToSupabaseUpdate,
} from '@/types';
import { handleApiError } from '@/utils/errorHandler';
import { utcTimestampToLocalDateString } from '@/utils/dateUtils';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * Maps Supabase database errors to our standardized error types
 */
const mapDatabaseError = (error: PostgrestError | Error): ApiError => {
  const message = error.message.toLowerCase();

  if (message.includes('permission') || message.includes('unauthorized')) {
    return {
      code: 'PERMISSION_DENIED',
      message: 'You do not have permission to perform this action.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('not found') || message.includes('no rows')) {
    return {
      code: 'NOT_FOUND',
      message: 'Journal entry not found.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('unique') || message.includes('duplicate')) {
    return {
      code: 'DUPLICATE_ERROR',
      message: 'A journal entry with this information already exists.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('validation') || message.includes('invalid')) {
    return {
      code: 'VALIDATION_ERROR',
      message: 'Invalid journal entry data. Please check your input.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('network') || message.includes('timeout')) {
    return {
      code: 'NETWORK_ERROR',
      message: 'Network error. Please check your connection and try again.',
      details: error.message,
      retryable: true,
    };
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: 'An unexpected error occurred while processing your journal entry.',
    details: error.message,
    retryable: true,
  };
};

/**
 * Create a new journal entry
 */
export const createJournalEntry = async (
  userId: string,
  payload: CreateJournalEntryPayload
): Promise<ApiResponse<JournalEntry>> => {
  try {
    const insertData = journalEntryToSupabaseInsert({
      user_id: userId,
      ...payload,
    });

    const { data, error } = await supabase
      .from('journal_entries')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    const journalEntry = supabaseToJournalEntry(data);

    return {
      success: true,
      data: journalEntry,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Get journal entries for a user with enhanced pagination support
 */
export const getJournalEntries = async (
  userId: string,
  filters?: JournalEntryFilters
): Promise<ApiResponse<JournalEntry[]>> => {
  try {
    // Build base query
    let query = supabase.from('journal_entries').select('*').eq('user_id', userId);
    let countQuery = supabase.from('journal_entries').select('*', { count: 'exact', head: true }).eq('user_id', userId);

    // Apply filters to both queries
    if (filters) {
      if (filters.emotion) {
        query = query.eq('emotion', filters.emotion);
        countQuery = countQuery.eq('emotion', filters.emotion);
      }

      if (filters.mood_range) {
        query = query
          .gte('mood_score', filters.mood_range.min)
          .lte('mood_score', filters.mood_range.max);
        countQuery = countQuery
          .gte('mood_score', filters.mood_range.min)
          .lte('mood_score', filters.mood_range.max);
      }

      if (filters.date_range) {
        query = query
          .gte('created_at', filters.date_range.start)
          .lte('created_at', filters.date_range.end);
        countQuery = countQuery
          .gte('created_at', filters.date_range.start)
          .lte('created_at', filters.date_range.end);
      }

      if (filters.search_term) {
        const searchFilter = `title.ilike.%${filters.search_term}%,content.ilike.%${filters.search_term}%`;
        query = query.or(searchFilter);
        countQuery = countQuery.or(searchFilter);
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset !== undefined) {
        const limit = filters.limit || 10;
        query = query.range(filters.offset, filters.offset + limit - 1);
      }
    }

    // Default ordering
    query = query.order('created_at', { ascending: false });

    // Execute both queries
    const [dataResult, countResult] = await Promise.all([
      query,
      countQuery
    ]);

    if (dataResult.error) {
      return {
        success: false,
        error: mapDatabaseError(dataResult.error),
      };
    }

    if (countResult.error) {
      console.warn('Failed to get total count:', countResult.error);
    }

    const journalEntries = dataResult.data ? dataResult.data.map(supabaseToJournalEntry) : [];
    const totalCount = countResult.count || undefined;

    // Calculate pagination metadata
    const limit = filters?.limit || 10;
    const offset = filters?.offset || 0;
    const hasMore = journalEntries.length === limit;

    return {
      success: true,
      data: journalEntries,
      meta: {
        timestamp: new Date().toISOString(),
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: totalCount || 0,
          totalPages: totalCount ? Math.ceil(totalCount / limit) : 0,
          hasNext: hasMore,
          hasPrevious: offset > 0,
        },
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Update a journal entry
 */
export const updateJournalEntry = async (
  entryId: string,
  userId: string,
  payload: UpdateJournalEntryPayload
): Promise<ApiResponse<JournalEntry>> => {
  try {
    console.log('🔄 updateJournalEntry service called with:', {
      entryId,
      userId,
      payload
    });

    const updateData = journalEntryToSupabaseUpdate(payload);

    console.log('📤 Sending to Supabase:', {
      entryId,
      userId,
      updateData
    });

    const { data, error } = await supabase
      .from('journal_entries')
      .update(updateData)
      .eq('id', entryId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      console.error('❌ Supabase update error:', error);
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    console.log('✅ Supabase update successful:', data);
    const journalEntry = supabaseToJournalEntry(data);

    return {
      success: true,
      data: journalEntry,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Delete a journal entry
 */
export const deleteJournalEntry = async (
  entryId: string,
  userId: string
): Promise<ApiResponse<void>> => {
  try {
    console.log('🗑️ Database delete operation:', { entryId, userId });

    const { data, error, count } = await supabase
      .from('journal_entries')
      .delete()
      .eq('id', entryId)
      .eq('user_id', userId)
      .select(); // Add select to get the deleted record info

    console.log('🗑️ Database delete result:', { data, error, count });

    if (error) {
      console.error('❌ Database delete error:', error);
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    // Check if any rows were actually deleted
    if (data && data.length === 0) {
      console.warn('⚠️ No rows were deleted - entry may not exist or user mismatch');
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Journal entry not found or you do not have permission to delete it',
          retryable: false,
        },
      };
    }

    console.log('✅ Database delete successful');
    return {
      success: true,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('❌ Database delete exception:', error);
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Convert journal entry to formatted display entry
 */
export const formatJournalEntryForDisplay = (entry: JournalEntry): FormattedJournalEntry => {
  const formatted: FormattedJournalEntry = {
    id: entry.id,
    date: utcTimestampToLocalDateString(entry.created_at),
    title: entry.title,
    content: entry.content,
    emotion: entry.emotion,
    mood_score: entry.mood_score,
    created_at: entry.created_at,
  };

  // Add optional fields only if they exist
  if (entry.ai_reflection) formatted.ai_reflection = entry.ai_reflection;
  if (entry.ai_summary) formatted.ai_summary = entry.ai_summary;
  if (entry.ai_emotion) formatted.ai_emotion = entry.ai_emotion;
  if (entry.ai_encouragement) formatted.ai_encouragement = entry.ai_encouragement;
  if (entry.ai_reflection_question) formatted.ai_reflection_question = entry.ai_reflection_question;

  return formatted;
};

/**
 * Get journal statistics for a user
 */
export const getJournalStats = async (
  userId: string
): Promise<ApiResponse<JournalStats>> => {
  try {
    // Get all journal entries for the user
    const { data: entries, error } = await supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    if (!entries || entries.length === 0) {
      return {
        success: true,
        data: {
          total_entries: 0,
          average_mood: 0,
          most_common_emotion: 'neutral',
          entries_this_week: 0,
          entries_this_month: 0,
          mood_trend: 'stable',
        },
        meta: {
          timestamp: new Date().toISOString(),
        },
      };
    }

    // Calculate statistics
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Basic stats
    const totalEntries = entries.length;
    const moodScores = entries
      .map(entry => entry.mood_score)
      .filter(score => score !== null && score !== undefined) as number[];

    const averageMood = moodScores.length > 0
      ? moodScores.reduce((sum, score) => sum + score, 0) / moodScores.length
      : 0;

    // Count emotions
    const emotionCounts: Record<string, number> = {};
    entries.forEach(entry => {
      if (entry.emotion) {
        emotionCounts[entry.emotion] = (emotionCounts[entry.emotion] || 0) + 1;
      }
    });

    const mostCommonEmotion = Object.entries(emotionCounts).reduce(
      (max, [emotion, count]) => count > max.count ? { emotion, count } : max,
      { emotion: 'neutral', count: 0 }
    ).emotion as any;

    // Time-based counts
    const entriesThisWeek = entries.filter(entry =>
      new Date(entry.created_at) >= oneWeekAgo
    ).length;

    const entriesThisMonth = entries.filter(entry =>
      new Date(entry.created_at) >= oneMonthAgo
    ).length;

    // Calculate mood trend (simple implementation)
    const recentEntries = entries.slice(0, Math.min(10, entries.length));
    const olderEntries = entries.slice(10, Math.min(20, entries.length));

    let moodTrend: 'improving' | 'declining' | 'stable' = 'stable';

    if (recentEntries.length > 0 && olderEntries.length > 0) {
      const recentAvg = recentEntries
        .filter(e => e.mood_score)
        .reduce((sum, e) => sum + (e.mood_score || 0), 0) / recentEntries.length;

      const olderAvg = olderEntries
        .filter(e => e.mood_score)
        .reduce((sum, e) => sum + (e.mood_score || 0), 0) / olderEntries.length;

      const difference = recentAvg - olderAvg;
      if (difference > 0.5) moodTrend = 'improving';
      else if (difference < -0.5) moodTrend = 'declining';
    }

    const stats: JournalStats = {
      total_entries: totalEntries,
      average_mood: Math.round(averageMood * 100) / 100,
      most_common_emotion: mostCommonEmotion,
      entries_this_week: entriesThisWeek,
      entries_this_month: entriesThisMonth,
      mood_trend: moodTrend,
    };

    return {
      success: true,
      data: stats,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};
