# Memory Extraction System

The Memory Extraction System is designed to extract important long-term facts about users from either journal entries or conversations between the user and the AI assistant named <PERSON>.

## Overview

The system identifies and extracts information that <PERSON> should remember to provide more personalized, emotionally aware conversations in future interactions. It focuses on persistent facts, ongoing emotions, significant events, personal goals, preferences, and identity markers that would be relevant across multiple conversations.

## Architecture

### Core Components

1. **Memory Types** (`src/types/ai.ts`)
   - `UserMemory`: Core memory object structure
   - `MemoryCategory`: Classification categories
   - `MemoryExtractionInput`: Input for extraction
   - `MemoryExtractionResponse`: Extraction response

2. **Memory Extraction Service** (`src/services/memoryExtractionService.ts`)
   - `extractMemories()`: Core extraction function
   - `extractMemoriesFromJournalEntry()`: Journal-specific extraction
   - `extractMemoriesFromConversation()`: Conversation-specific extraction

3. **Memory Context Hook** (`src/hooks/useMemoryContext.ts`)
   - State management for user memories
   - Extraction orchestration
   - Memory CRUD operations
   - Statistics tracking

4. **UI Components**
   - `MemoryExtraction`: Test component for extraction
   - `MemoryTest`: Test page for demonstration

## Memory Categories

The system classifies extracted information into six categories:

- **fact**: Objective information (pets, family, job, location, possessions)
- **emotion**: Current emotional states or patterns (stress, anxiety, excitement)
- **event**: Significant upcoming or recent events (presentations, trips, milestones)
- **goal**: Things the user wants to achieve (learning languages, fitness, career)
- **preference**: Likes, dislikes, habits, or personal choices (hobbies, food, activities)
- **identity**: Core aspects of who they are (profession, relationships, values, beliefs)

## Output Format

The system returns a JSON array of memory objects:

```json
[
  {
    "key": "string",        // Concise 1-3 word label
    "value": "string",      // Specific memory content
    "category": "string"    // One of the six categories
  }
]
```

## Examples

### Journal Entry Example

**Input:**
```
"I've been feeling overwhelmed at work lately. Enzo hasn't been eating much either — I hope he's okay. I really want to get back into Mandarin practice this week."
```

**Output:**
```json
[
  { "key": "work_stress", "value": "feeling overwhelmed at work lately", "category": "emotion" },
  { "key": "pet", "value": "Enzo the cat", "category": "fact" },
  { "key": "language_goal", "value": "wants to resume Mandarin practice", "category": "goal" }
]
```

### Conversation Example

**Input:**
```
User: "I've been super stressed lately."
Amber: "I'm here for you. Do you know why you've been feeling that way?"
User: "Probably because of the upcoming presentation and the pressure to impress the execs."
```

**Output:**
```json
[
  { "key": "work_stress", "value": "stressed about upcoming executive presentation", "category": "emotion" },
  { "key": "work_event", "value": "important presentation to executives coming up", "category": "event" }
]
```

## Integration with AI Conversations

The memory system integrates with the local LLM conversation service to provide context-aware responses:

1. **Memory Context Injection**: User memories are automatically injected into AI conversation prompts
2. **Automatic Extraction**: Memories can be extracted from conversations and journal entries
3. **Persistent Storage**: Memories are maintained across sessions (when integrated with database)

## Usage

### Basic Memory Extraction

```typescript
import { useMemoryContext } from '@/hooks/useMemoryContext';

const { extractFromJournalEntry, memories } = useMemoryContext();

// Extract from journal entry
const response = await extractFromJournalEntry({
  title: "My Day",
  content: "Had a great day with my dog Max...",
  emotion: "happy",
  moodScore: 8
});

// Access extracted memories
console.log(response.memories);
```

### Memory Management

```typescript
const {
  addMemory,
  removeMemory,
  updateMemory,
  getMemoriesByCategory,
  searchMemories,
  getMemoryContext
} = useMemoryContext();

// Add manual memory
addMemory({
  key: "favorite_color",
  value: "loves the color blue",
  category: "preference"
});

// Get memories by category
const facts = getMemoriesByCategory('fact');

// Search memories
const workRelated = searchMemories('work');

// Get formatted context for AI
const context = getMemoryContext();
```

## Testing

Visit `/memory-test` in the application to test the memory extraction system:

1. **Input Test Content**: Enter journal entries or conversations
2. **Extract Memories**: Click to extract memories using the local LLM
3. **View Results**: See extracted memories categorized and formatted
4. **Memory Management**: Add, remove, or update memories manually
5. **Context Preview**: See how memories are formatted for AI context

## Configuration

The system uses the local LLM service (Ollama) for memory extraction. Ensure:

1. Ollama is running locally on `http://localhost:11434`
2. The `llama3.1:8b` model is available
3. The local LLM service is properly configured

## Components

### UI Components

1. **MemoryExtraction** (`src/components/features/MemoryExtraction.tsx`)
   - Complete testing interface for memory extraction
   - Supports both journal entries and conversations
   - Real-time extraction and display

2. **MemoryExtractionButton** (`src/components/features/MemoryExtractionButton.tsx`)
   - Reusable button component for journal entries
   - Shows extraction status and results
   - Easy integration with existing forms

3. **MemoryAwareConversation** (`src/components/features/MemoryAwareConversation.tsx`)
   - Demonstrates memory-aware AI conversations
   - Shows how memory context enhances responses
   - Real-time conversation with memory injection

### Test Page

Visit `/memory-test` to access a comprehensive testing interface with four tabs:

1. **Extraction Test**: Raw memory extraction testing
2. **Integration Demo**: Shows button integration with journal entries
3. **Memory-Aware Chat**: Live conversation with memory context
4. **Examples**: Documentation and category explanations

## Integration Examples

### Adding Memory Extraction to Journal Entries

```typescript
import { MemoryExtractionButton } from '@/components/features/MemoryExtractionButton';

// In your journal entry component
<MemoryExtractionButton
  journalEntry={{
    title: entry.title,
    content: entry.content,
    emotion: entry.emotion,
    moodScore: entry.moodScore
  }}
  onMemoriesExtracted={(memories) => {
    console.log('New memories:', memories);
  }}
/>
```

### Memory-Aware AI Conversations

```typescript
import { useMemoryContext } from '@/hooks/useMemoryContext';
import { generateAIConversationResponse } from '@/services/aiConversationService';

const { getMemoryContext } = useMemoryContext();

// Generate AI response with memory context
const response = await generateAIConversationResponse(
  conversationInput,
  undefined, // config
  getMemoryContext() // memory context
);
```

## Future Enhancements

1. **Database Integration**: Persist memories to Supabase
2. **Memory Validation**: Validate and merge duplicate memories
3. **Memory Expiration**: Implement TTL for temporary memories
4. **Memory Importance Scoring**: Weight memories by importance
5. **Privacy Controls**: User control over memory storage and usage
6. **Memory Sharing**: Share memories across different AI interactions
7. **Automatic Extraction**: Extract memories automatically during conversations
8. **Memory Analytics**: Track memory usage and effectiveness
9. **Memory Search**: Advanced search and filtering capabilities
10. **Memory Export**: Export memories for backup or analysis
