# Semantic Memory Implementation

## Overview

This implementation adds semantic deduplication and memory recall functionality to the AmberGlow journal application. The system uses semantic embeddings to prevent duplicate memories and provide contextually relevant memories to <PERSON>'s AI chat responses.

## Features Implemented

### 1. Semantic Deduplication

**Location**: `src/services/memoryExtractionService.ts`

- **Function**: `checkSemanticDuplication()`
- **Threshold**: 0.92 cosine similarity (configurable)
- **Process**:
  1. Generate embedding for new memory
  2. Compare with existing memory embeddings
  3. Skip saving if similarity > threshold
  4. Log deduplication statistics

**Benefits**:
- Prevents duplicate memories from cluttering the database
- Maintains memory quality and relevance
- Reduces storage overhead

### 2. Semantic Memory Recall

**Location**: `src/services/memoryRecallService.ts`

- **Function**: `recallRelevantMemories()`
- **Configuration**:
  - Max memories: 8 (default)
  - Min similarity: 0.3
  - Min importance: 4
  - Similarity weight: 0.7
  - High relevance threshold: 0.8

**Process**:
1. Generate embedding for user's message
2. Calculate cosine similarity with stored memory embeddings
3. Apply importance and relevance filtering
4. Rank by combined score (similarity + importance)
5. Return top N most relevant memories

### 3. AI Chat Integration

**Location**: `src/services/aiConversationService.ts`

- **Integration Point**: `generateLocalLLMConversationResponse()`
- **Memory Context**: Automatically injected into AI prompts
- **Format**: Structured memory context with relevance indicators

**Process**:
1. User sends message to Amber
2. System recalls relevant memories based on message content
3. Memories are formatted and injected into AI context
4. Amber responds with personalized, memory-aware content

## Technical Implementation

### Database Schema

**Migration**: `supabase/migrations/20250725000001_add_memory_embeddings.sql`

```sql
-- Add embedding column to memory table
ALTER TABLE public.memory 
ADD COLUMN embedding JSONB DEFAULT NULL;

-- Create indexes for efficient similarity search
CREATE INDEX IF NOT EXISTS idx_memory_embedding ON public.memory USING gin (embedding);
CREATE INDEX IF NOT EXISTS idx_memory_similarity_search 
ON public.memory(user_id, importance DESC) 
WHERE embedding IS NOT NULL;
```

### Embedding Service Integration

**Service**: Uses existing embedding service via Vite proxy
- **Endpoint**: `/api/embedding/*`
- **Model**: `all-MiniLM-L6-v2` (384 dimensions)
- **Fallback**: Word-matching if embedding service unavailable

### Type Definitions

**Updated Interface**: `src/types/ai.ts`

```typescript
export interface UserMemory {
  key: string;
  value: string;
  category: MemoryCategory;
  importance?: number;
  embedding?: number[] | string | null; // Added for semantic search
}
```

## Configuration

### Semantic Deduplication

```typescript
const SEMANTIC_SIMILARITY_THRESHOLD = 0.92; // Threshold for duplicates
const SEMANTIC_DEDUPLICATION_ENABLED = true; // Feature flag
```

### Memory Recall

```typescript
const DEFAULT_RECALL_CONFIG = {
  maxMemories: 8,
  minSimilarity: 0.3,
  minImportance: 4,
  similarityWeight: 0.7,
  includeHighRelevance: true,
  highRelevanceThreshold: 0.8,
};
```

## Testing

### Test Suite

**File**: `test-semantic-memory.html`

**Tests Include**:
1. **Embedding Service**: Health check and embedding generation
2. **Semantic Similarity**: Cosine similarity calculations
3. **Deduplication**: Threshold testing with similar memories
4. **Memory Recall**: Relevance ranking for different prompts

**Usage**:
1. Ensure embedding service is running (`localhost:5005`)
2. Start React dev server (`npm run dev`)
3. Open `http://localhost:8080/test-semantic-memory.html`
4. Run individual tests or full test suite

### Expected Results

- **Similar memories** (e.g., "cat named Whiskers" vs "feline companion Whiskers"): similarity > 0.8
- **Different memories** (e.g., "pet cat" vs "work stress"): similarity < 0.5
- **Deduplication**: Memories with similarity > 0.92 should be flagged as duplicates
- **Recall**: Relevant memories should rank higher for related prompts

## Performance Considerations

### Optimization Features

1. **Caching**: 5-minute cache for extraction results
2. **Batch Processing**: Efficient embedding generation
3. **Fallback Strategy**: Word-matching when embeddings unavailable
4. **Selective Processing**: Only process memories with embeddings

### Memory Usage

- **Embedding Storage**: ~1.5KB per memory (384 float32 values)
- **Processing Time**: ~10-50ms per embedding generation
- **Database Impact**: Minimal with proper indexing

## Integration Points

### Automatic Memory Extraction

**Journal Entries**: `extractMemoriesFromJournalEntry()`
- Semantic deduplication applied before saving
- Statistics logged for monitoring

**Conversations**: `extractMemoriesFromConversation()`
- Same deduplication process
- Context-aware memory extraction

### AI Chat Enhancement

**Memory Context Injection**: `generateLocalLLMConversationResponse()`
- Automatic memory recall for each user message
- Formatted context with relevance indicators
- Non-blocking fallback if recall fails

## Monitoring and Debugging

### Logging

- **Deduplication**: `🧠 [DEDUP]` prefix for deduplication logs
- **Recall**: `🧠 [RECALL]` prefix for memory recall logs
- **Statistics**: Memory counts, similarity scores, processing times

### Debug Information

- Similarity scores for each memory comparison
- Recall statistics (total considered, embeddings available)
- Fallback usage when semantic features unavailable

## Future Enhancements

### Potential Improvements

1. **Dynamic Thresholds**: Adjust deduplication threshold based on memory category
2. **Temporal Weighting**: Consider memory age in relevance calculations
3. **User Preferences**: Allow users to configure recall sensitivity
4. **Batch Deduplication**: Periodic cleanup of existing duplicate memories
5. **Advanced Similarity**: Explore other similarity metrics beyond cosine

### Scalability Considerations

1. **Vector Database**: Consider pgvector for large-scale deployments
2. **Embedding Caching**: Cache embeddings for frequently accessed memories
3. **Async Processing**: Background memory processing for better UX
4. **Distributed Embeddings**: Multiple embedding service instances

## Troubleshooting

### Common Issues

1. **Embedding Service Unavailable**: System falls back to word-matching
2. **High Memory Usage**: Check embedding storage and cleanup old embeddings
3. **Poor Recall Quality**: Adjust similarity thresholds and importance weights
4. **Slow Performance**: Verify database indexes and embedding service health

### Verification Steps

1. Check embedding service health: `/api/embedding/health`
2. Verify database migration applied: Check `memory.embedding` column exists
3. Test semantic similarity: Use test suite to verify expected behavior
4. Monitor logs: Look for deduplication and recall statistics

## Conclusion

The semantic memory implementation provides a robust foundation for intelligent memory management in AmberGlow. The system automatically prevents duplicate memories while ensuring Amber has access to the most relevant user context for personalized conversations.

The implementation is designed to be:
- **Robust**: Graceful fallbacks when semantic features unavailable
- **Performant**: Efficient similarity calculations and database queries
- **Configurable**: Adjustable thresholds and behavior
- **Monitorable**: Comprehensive logging and statistics
- **Testable**: Complete test suite for verification
