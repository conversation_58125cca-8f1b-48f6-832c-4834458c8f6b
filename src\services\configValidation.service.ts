/**
 * Configuration Validation Service
 * Comprehensive validation with helpful error messages and recovery suggestions
 */

import {
  getEnvironmentConfig,
  getEnvironmentValidation,
  validateEnvironment,
} from '@/config/environment.config';
import { validateSensitiveConfig, checkCriticalServices } from '@/utils/secureConfig';
import { handleError } from '@/utils/errorHandler';

/**
 * Validation severity levels
 */
export type ValidationSeverity = 'error' | 'warning' | 'info';

/**
 * Validation issue interface
 */
export interface ValidationIssue {
  severity: ValidationSeverity;
  category: string;
  message: string;
  suggestion: string;
  variable?: string;
  code: string;
}

/**
 * Validation result interface
 */
export interface ConfigValidationResult {
  isValid: boolean;
  canProceed: boolean;
  issues: ValidationIssue[];
  summary: {
    errors: number;
    warnings: number;
    info: number;
  };
}

/**
 * Validate environment configuration with detailed feedback
 */
export const validateConfiguration = (): ConfigValidationResult => {
  const issues: ValidationIssue[] = [];

  try {
    // 1. Basic environment validation
    const envValidation = validateEnvironment();

    // Process environment validation errors
    for (const error of envValidation.errors) {
      issues.push({
        severity: 'error',
        category: 'Environment',
        message: error,
        suggestion: 'Check your .env file and ensure all required variables are set',
        code: 'ENV_VALIDATION_ERROR',
      });
    }

    // Process environment validation warnings
    for (const warning of envValidation.warnings) {
      issues.push({
        severity: 'warning',
        category: 'Environment',
        message: warning,
        suggestion: 'Consider updating your configuration for optimal performance',
        code: 'ENV_VALIDATION_WARNING',
      });
    }

    // 2. Sensitive configuration validation
    const sensitiveValidation = validateSensitiveConfig();

    // Process sensitive configuration errors
    for (const error of sensitiveValidation.errors) {
      issues.push({
        severity: 'error',
        category: 'Security',
        message: error,
        suggestion: 'Verify your API keys and URLs are correctly formatted',
        code: 'SECURITY_VALIDATION_ERROR',
      });
    }

    // Process sensitive configuration warnings
    for (const warning of sensitiveValidation.warnings) {
      issues.push({
        severity: 'warning',
        category: 'Security',
        message: warning,
        suggestion: 'Review your security settings for production readiness',
        code: 'SECURITY_VALIDATION_WARNING',
      });
    }

    // 3. Critical services check
    const servicesCheck = checkCriticalServices();

    if (!servicesCheck.supabase) {
      issues.push({
        severity: 'error',
        category: 'Services',
        message: 'Supabase configuration is incomplete or invalid',
        suggestion: 'Ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are properly set',
        code: 'SUPABASE_CONFIG_ERROR',
      });
    }

    if (!servicesCheck.ai) {
      const env = getEnvironmentConfig();
      if (env.features.aiEnabled) {
        issues.push({
          severity: 'warning',
          category: 'Services',
          message: 'AI features are enabled but Gemini API key is missing',
          suggestion:
            'Set VITE_GEMINI_API_KEY or disable AI features with VITE_ENABLE_AI_FEATURES=false',
          code: 'AI_CONFIG_WARNING',
        });
      }
    }

    // 4. Environment-specific validations
    const env = getEnvironmentConfig();

    if (env.isProduction) {
      // Production-specific checks
      if (env.features.debugMode) {
        issues.push({
          severity: 'warning',
          category: 'Production',
          message: 'Debug mode is enabled in production',
          suggestion: 'Set VITE_ENABLE_DEBUG_MODE=false for production deployment',
          code: 'PROD_DEBUG_WARNING',
        });
      }

      if (!env.security.httpsOnly) {
        issues.push({
          severity: 'error',
          category: 'Production',
          message: 'HTTPS-only mode is disabled in production',
          suggestion: 'Set VITE_ENABLE_HTTPS_ONLY=true for production security',
          code: 'PROD_HTTPS_ERROR',
        });
      }

      if (!env.features.analyticsEnabled) {
        issues.push({
          severity: 'info',
          category: 'Production',
          message: 'Analytics are disabled in production',
          suggestion: 'Consider enabling analytics with VITE_ENABLE_ANALYTICS=true',
          code: 'PROD_ANALYTICS_INFO',
        });
      }
    }

    if (env.isDevelopment) {
      // Development-specific checks
      if (!env.features.debugMode) {
        issues.push({
          severity: 'info',
          category: 'Development',
          message: 'Debug mode is disabled in development',
          suggestion:
            'Enable debug mode with VITE_ENABLE_DEBUG_MODE=true for better development experience',
          code: 'DEV_DEBUG_INFO',
        });
      }

      if (env.features.analyticsEnabled) {
        issues.push({
          severity: 'info',
          category: 'Development',
          message: 'Analytics are enabled in development',
          suggestion:
            'Consider disabling analytics with VITE_ENABLE_ANALYTICS=false during development',
          code: 'DEV_ANALYTICS_INFO',
        });
      }
    }

    // Calculate summary
    const summary = {
      errors: issues.filter(i => i.severity === 'error').length,
      warnings: issues.filter(i => i.severity === 'warning').length,
      info: issues.filter(i => i.severity === 'info').length,
    };

    const isValid = summary.errors === 0;
    const canProceed = summary.errors === 0; // Can proceed if no errors

    return {
      isValid,
      canProceed,
      issues,
      summary,
    };
  } catch (error) {
    // Handle validation errors gracefully
    issues.push({
      severity: 'error',
      category: 'System',
      message: 'Configuration validation failed unexpectedly',
      suggestion: 'Check your environment configuration and restart the application',
      code: 'VALIDATION_SYSTEM_ERROR',
    });

    return {
      isValid: false,
      canProceed: false,
      issues,
      summary: { errors: 1, warnings: 0, info: 0 },
    };
  }
};

/**
 * Display validation results with helpful formatting
 */
export const displayValidationResults = (result: ConfigValidationResult): void => {
  const { issues, summary } = result;

  if (result.isValid) {
    console.log('✅ Configuration validation passed');
    if (summary.warnings > 0 || summary.info > 0) {
      console.log(`📋 Found ${summary.warnings} warnings and ${summary.info} info messages`);
    }
  } else {
    console.error(`❌ Configuration validation failed with ${summary.errors} errors`);
  }

  // Group issues by category
  const issuesByCategory = issues.reduce(
    (acc, issue) => {
      if (!acc[issue.category]) {
        acc[issue.category] = [];
      }
      acc[issue.category].push(issue);
      return acc;
    },
    {} as Record<string, ValidationIssue[]>
  );

  // Display issues by category
  for (const [category, categoryIssues] of Object.entries(issuesByCategory)) {
    console.group(`📁 ${category}`);

    for (const issue of categoryIssues) {
      const icon = issue.severity === 'error' ? '❌' : issue.severity === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`${icon} ${issue.message}`);
      console.log(`   💡 ${issue.suggestion}`);
      if (issue.variable) {
        console.log(`   🔧 Variable: ${issue.variable}`);
      }
    }

    console.groupEnd();
  }

  // Provide next steps
  if (!result.canProceed) {
    console.error('\n🚫 Cannot proceed with current configuration');
    console.error('📝 Please fix the errors above and restart the application');
  } else if (summary.warnings > 0) {
    console.warn('\n⚠️ Application can run but consider addressing the warnings above');
  }
};

/**
 * Initialize configuration validation
 */
export const initializeConfigValidation = (): ConfigValidationResult => {
  console.log('🔍 Validating application configuration...');

  const result = validateConfiguration();
  displayValidationResults(result);

  // Log validation errors for monitoring
  if (!result.isValid) {
    const errorIssues = result.issues.filter(i => i.severity === 'error');
    for (const issue of errorIssues) {
      handleError(
        new Error(issue.message),
        {
          component: 'ConfigValidation',
          action: 'validate_configuration',
          metadata: { code: issue.code, category: issue.category },
        },
        { showToast: false, logToConsole: false } // Already logged above
      );
    }
  }

  return result;
};
