<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Embedding Service</title>
</head>
<body>
    <h1>Test Embedding Service</h1>
    <button onclick="testHealthCheck()">Test Health Check</button>
    <button onclick="testEmbedding()">Test Embedding</button>
    <div id="results"></div>

    <script>
        async function testHealthCheck() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing health check...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/embedding/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                results.innerHTML = `<p>Health Check Success: ${JSON.stringify(data)}</p>`;
            } catch (error) {
                results.innerHTML = `<p>Health Check Error: ${error.message}</p>`;
            }
        }

        async function testEmbedding() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing embedding generation...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/embedding/embed', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: 'This is a test memory for semantic embedding'
                    }),
                });
                
                const data = await response.json();
                results.innerHTML = `<p>Embedding Success: Generated ${data.embedding.length} dimensions</p>`;
            } catch (error) {
                results.innerHTML = `<p>Embedding Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
