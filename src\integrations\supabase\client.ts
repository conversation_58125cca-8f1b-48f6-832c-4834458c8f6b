/**
 * Supabase Client Configuration
 * Secure client setup using environment configuration
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { getEnvironmentConfig } from '@/config/environment.config';

// Get environment configuration
const env = getEnvironmentConfig();

// Validate Supabase configuration
if (!env.supabase.url || !env.supabase.anonKey) {
  throw new Error(
    'Supabase configuration is missing. Please check your environment variables:\n' +
      '- VITE_SUPABASE_URL: Your Supabase project URL\n' +
      '- VITE_SUPABASE_ANON_KEY: Your Supabase anonymous key'
  );
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(env.supabase.url, env.supabase.anonKey, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  },
  global: {
    headers: {
      'X-Client-Info': `${env.app.name}/${env.app.version}`,
    },
  },
});
