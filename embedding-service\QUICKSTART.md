# Quick Start Guide

Get the AmberGlow Embedding Service running in 5 minutes!

## Prerequisites

- Python 3.8 or higher
- 2GB+ RAM
- Internet connection (for initial model download)

## Installation

### Option 1: Automatic Setup (Windows)

1. **Double-click `start.bat`** - This will automatically:
   - Create a virtual environment
   - Install dependencies
   - Start the service

### Option 2: Manual Setup (All Platforms)

```bash
# 1. Navigate to embedding-service directory
cd embedding-service

# 2. Create virtual environment
python -m venv venv

# 3. Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 4. Install dependencies
pip install -r requirements.txt

# 5. Start the service
python start.py
```

### Option 3: Docker (Advanced)

```bash
# Build and run with Docker Compose
docker-compose up --build
```

## Verification

### 1. Check Service Health

Open your browser and go to: http://localhost:5005/health

You should see:
```json
{
  "status": "healthy",
  "service": "AmberGlow Embedding Service",
  "model": "all-MiniLM-L6-v2",
  "model_status": "loaded"
}
```

### 2. Test Embedding Generation

```bash
# Using curl
curl -X POST http://localhost:5005/embed \
  -H "Content-Type: application/json" \
  -d '{"text": "I had a wonderful day at the park."}'
```

### 3. Run Automated Tests

```bash
# In the embedding-service directory
python test_api.py
```

## Integration with AmberGlow

1. **Start the embedding service** (as shown above)
2. **Follow the integration guide** in `INTEGRATION.md`
3. **Update your frontend code** to use semantic embeddings

## Troubleshooting

### Service Won't Start

**Error: "Python not found"**
- Install Python 3.8+ from python.org
- Make sure Python is in your PATH

**Error: "Port 5005 already in use"**
```bash
# Change port
export FLASK_PORT=5006
python start.py
```

**Error: "Model download failed"**
- Check internet connection
- Try running: `python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('all-MiniLM-L6-v2')"`

### Frontend Integration Issues

**CORS Error in Browser**
- Ensure embedding service is running
- Check that your React app is on an allowed origin (localhost:5173, localhost:3000)

**Connection Refused**
- Verify service is running: http://localhost:5005/health
- Check firewall settings

### Performance Issues

**Slow First Request**
- Model loading takes 2-3 seconds on first start
- Subsequent requests are much faster (~10-50ms)

**High Memory Usage**
- Normal: ~500MB for the loaded model
- Ensure at least 2GB RAM available

## Next Steps

1. ✅ Service is running
2. 📖 Read `INTEGRATION.md` for frontend integration
3. 🧪 Run tests with `python test_api.py`
4. 🚀 Deploy to production (see README.md)

## Support

- 📋 Check logs in `embedding-service.log`
- 🐛 Run `python test_api.py` for diagnostics
- 📚 See full documentation in `README.md`
