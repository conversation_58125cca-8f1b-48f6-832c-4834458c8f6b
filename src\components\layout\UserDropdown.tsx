/**
 * User Dropdown Component
 * Reusable user dropdown component with consistent styling and functionality
 * Consolidates the user dropdown pattern from Navigation component
 */

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { NavigationButton } from '@/components/ui/navigation-button';
import { User, Settings, LogOut, BarChart3 } from 'lucide-react';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface UserProfile {
  id: string;
  email: string;
  full_name?: string | null;
  avatar_url?: string | null;
}

interface DropdownMenuItem {
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'destructive';
}

interface UserDropdownProps extends BaseComponentProps {
  /** User profile data */
  user: UserProfile;
  /** Additional dropdown menu items */
  menuItems?: DropdownMenuItem[];
  /** Sign out handler */
  onSignOut: () => void;
  /** Settings click handler */
  onSettings?: () => void;
  /** Analytics click handler */
  onAnalytics?: () => void;
  /** Avatar size */
  avatarSize?: 'sm' | 'md' | 'lg';
  /** Whether to show user info in dropdown */
  showUserInfo?: boolean;
  /** Whether to show default menu items */
  showDefaultItems?: boolean;
}

/**
 * UserDropdown component with amber/orange branding
 */
export const UserDropdown: React.FC<UserDropdownProps> = ({
  user,
  menuItems = [],
  onSignOut,
  onSettings,
  onAnalytics,
  avatarSize = 'md',
  showUserInfo = true,
  showDefaultItems = true,
  className,
  testId,
  ...props
}) => {
  // Avatar size classes
  const avatarSizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10',
  };

  // Generate user initials
  const getUserInitials = (name?: string | null, email?: string) => {
    if (name) {
      return name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return 'U';
  };

  // Default menu items
  const defaultMenuItems: DropdownMenuItem[] = [
    ...(onAnalytics ? [{
      label: 'Analytics',
      icon: <BarChart3 className="mr-2 h-4 w-4" />,
      onClick: onAnalytics,
    }] : []),
    ...(onSettings ? [{
      label: 'Settings',
      icon: <Settings className="mr-2 h-4 w-4" />,
      onClick: onSettings,
    }] : []),
    {
      label: 'Sign out',
      icon: <LogOut className="mr-2 h-4 w-4" />,
      onClick: onSignOut,
      variant: 'destructive' as const,
    },
  ];

  // Combine menu items
  const allMenuItems = showDefaultItems 
    ? [...menuItems, ...defaultMenuItems]
    : menuItems;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className={cn(
            'flex items-center gap-2 rounded-full p-1',
            'hover:bg-amber-50 transition-colors duration-200',
            'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2',
            className
          )}
          data-testid={testId}
          {...props}
        >
          <Avatar className={avatarSizeClasses[avatarSize]}>
            <AvatarImage 
              src={user.avatar_url || undefined} 
              alt={user.full_name || user.email}
            />
            <AvatarFallback className="bg-amber-100 text-amber-700 text-xs font-medium">
              {getUserInitials(user.full_name, user.email)}
            </AvatarFallback>
          </Avatar>
        </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end" 
        className="w-56 glass-effect border-white/30 bg-white/95 backdrop-blur-md"
      >
        {/* User Info */}
        {showUserInfo && (
          <>
            <div className="flex items-center gap-3 p-3">
              <Avatar className="h-10 w-10">
                <AvatarImage 
                  src={user.avatar_url || undefined} 
                  alt={user.full_name || user.email}
                />
                <AvatarFallback className="bg-amber-100 text-amber-700 font-medium">
                  {getUserInitials(user.full_name, user.email)}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {user.full_name || 'User'}
                </p>
                <p className="w-[200px] truncate text-sm text-muted-foreground">
                  {user.email}
                </p>
              </div>
            </div>
            <DropdownMenuSeparator />
          </>
        )}

        {/* Menu Items */}
        {allMenuItems.map((item, index) => (
          <DropdownMenuItem
            key={index}
            onClick={item.onClick}
            className={cn(
              'cursor-pointer transition-colors duration-200',
              item.variant === 'destructive' 
                ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                : 'hover:bg-amber-50'
            )}
          >
            {item.icon}
            <span>{item.label}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

/**
 * Simple UserAvatar component for cases where dropdown is not needed
 */
interface UserAvatarProps extends BaseComponentProps {
  /** User profile data */
  user: UserProfile;
  /** Avatar size */
  size?: 'sm' | 'md' | 'lg';
  /** Click handler */
  onClick?: () => void;
  /** Whether the avatar is clickable */
  clickable?: boolean;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'md',
  onClick,
  clickable = false,
  className,
  testId,
  ...props
}) => {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10',
  };

  const getUserInitials = (name?: string | null, email?: string) => {
    if (name) {
      return name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return 'U';
  };

  const avatar = (
    <Avatar className={cn(sizeClasses[size], className)}>
      <AvatarImage 
        src={user.avatar_url || undefined} 
        alt={user.full_name || user.email}
      />
      <AvatarFallback className="bg-amber-100 text-amber-700 text-xs font-medium">
        {getUserInitials(user.full_name, user.email)}
      </AvatarFallback>
    </Avatar>
  );

  if (clickable || onClick) {
    return (
      <button
        onClick={onClick}
        className={cn(
          'rounded-full transition-colors duration-200',
          'hover:bg-amber-50 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2'
        )}
        data-testid={testId}
        {...props}
      >
        {avatar}
      </button>
    );
  }

  return (
    <div data-testid={testId} {...props}>
      {avatar}
    </div>
  );
};
