/**
 * Lazy Journal Entries Presenter Component
 * Lazy-loaded version of JournalEntriesPresenter for better performance
 */

import { createLazyComponent } from '@/utils/performance.utils';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';

// Lazy load the JournalEntriesPresenter component
export const LazyJournalEntriesPresenter = createLazyComponent(
  () => import('./JournalEntriesPresenter').then(module => ({ default: module.JournalEntriesPresenter })),
  () => (
    <div className="flex items-center justify-center p-8">
      <LoadingSpinner size="lg" message="Loading journal entries..." />
    </div>
  ),
  ({ error }) => (
    <div className="flex items-center justify-center p-8 text-red-600">
      <div className="text-center">
        <p className="text-lg font-semibold mb-2">Failed to load journal entries</p>
        <p className="text-sm">{error.message}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    </div>
  )
);
