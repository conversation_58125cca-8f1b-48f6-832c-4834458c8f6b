/**
 * Memory Importance Evaluation Service
 * Service for evaluating the importance of memories using local LLM
 */

import { UserMemory, ApiResponse, AIServiceConfig } from '@/types';
import { getAIConfig } from '@/config/ai.config';
import { callLocalLLM, LocalLLMError } from './localLLMService';
import { handleApiError } from '@/utils/errorHandler';

/**
 * Memory importance evaluation response
 */
export interface MemoryImportanceResponse {
  importance: number;
  reasoning?: string;
  processingTime: number;
  source: 'local-llm' | 'fallback';
}

/**
 * System prompt for memory importance evaluation
 */
const IMPORTANCE_EVALUATION_PROMPT = `Rate the following memory from 1 (not important) to 10 (highly important) based on how emotionally or contextually meaningful it is to the user.

Consider these factors:
- Emotional significance (personal relationships, major life events, trauma, achievements)
- Long-term relevance (ongoing situations, persistent preferences, core identity aspects)
- Contextual importance (information that helps understand the user's current state)
- Uniqueness (rare or special experiences vs. routine daily activities)

Scoring guidelines:
- 1-3: Routine, temporary, or trivial information (daily activities, minor preferences)
- 4-6: Moderately important information (work situations, casual relationships, general interests)
- 7-8: Significant information (close relationships, important goals, meaningful experiences)
- 9-10: Highly significant information (life-changing events, core identity, deep emotional connections)

Respond with only a number from 1 to 10. Do not include any explanation or additional text.

Memory to evaluate: "{memory_value}"`;



/**
 * Evaluate memory importance using Local LLM
 */
const evaluateImportanceWithLocalLLM = async (
  memory: UserMemory,
  config: AIServiceConfig
): Promise<MemoryImportanceResponse> => {
  const startTime = Date.now();

  try {
    const prompt = IMPORTANCE_EVALUATION_PROMPT.replace('{memory_value}', memory.value);

    const messages = [
      {
        role: 'system' as const,
        content: 'You are an AI assistant that evaluates the importance of personal memories. Respond only with a number from 1 to 10. Do not include any explanations or additional text.',
      },
      {
        role: 'user' as const,
        content: prompt,
      },
    ];

    // callLocalLLM returns a string directly, not an object with content property
    console.log('🧠 [DEBUG] Using Local LLM for importance evaluation');
    const response = await callLocalLLM(messages, config);
    console.log('🧠 [DEBUG] Raw LLM response:', response);
    
    let text = response.trim();
    let importance: number;
    
    // Try to extract a number from the response
    // First, check if it's a simple number
    const numberMatch = text.match(/^(\d+)$/) || text.match(/(\d+)/);
    if (numberMatch) {
      importance = parseInt(numberMatch[1]);
      if (importance >= 1 && importance <= 10) {
        return {
          importance,
          processingTime: Date.now() - startTime,
          source: 'local-llm',
        };
      }
    }
    
    // If not a simple number, try to handle JSON response
    console.log('🧠 [DEBUG] Not a simple number, trying to parse JSON');
    
    // Remove markdown code blocks if present
    text = text
      .replace(/```json\s*/g, '')
      .replace(/```\s*/g, '')
      .trim();
    
    try {
      // Try to parse as JSON
      const jsonResponse = JSON.parse(text);
      console.log('🧠 [DEBUG] Parsed JSON response:', jsonResponse);
      
      // Extract importance from various possible JSON structures
      if (typeof jsonResponse === 'number') {
        importance = jsonResponse;
      } else if (jsonResponse.importance && typeof jsonResponse.importance === 'number') {
        importance = jsonResponse.importance;
      } else if (jsonResponse.score && typeof jsonResponse.score === 'number') {
        importance = jsonResponse.score;
      } else if (jsonResponse.value && typeof jsonResponse.value === 'number') {
        importance = jsonResponse.value;
      } else if (jsonResponse.rating && typeof jsonResponse.rating === 'number') {
        importance = jsonResponse.rating;
      }
      
      if (importance >= 1 && importance <= 10) {
        return {
          importance,
          processingTime: Date.now() - startTime,
          source: 'local-llm',
        };
      }
    } catch (jsonError) {
      console.log('🧠 [DEBUG] Failed to parse JSON:', jsonError);
      // Continue to fallback
    }
    
    // If we still don't have a valid importance score, use fallback
    console.log('🧠 [DEBUG] Could not extract valid importance score, using fallback');
    throw new Error('Could not extract valid importance score from LLM response');
    
  } catch (error) {
    console.error('🧠 [DEBUG] Error in LLM importance evaluation:', error);
    // Use fallback importance calculation
    const fallbackResult = calculateFallbackImportance(memory);
    console.log('🧠 [DEBUG] Using fallback importance:', fallbackResult.importance);
    return fallbackResult;
  }
};

/**
 * Fallback importance calculation based on heuristics
 */
const calculateFallbackImportance = (memory: UserMemory): MemoryImportanceResponse => {
  const startTime = Date.now();

  let importance = 5; // Default

  // Category-based scoring
  switch (memory.category) {
    case 'identity':
      importance = 8; // Core identity is very important
      break;
    case 'emotion':
      importance = 7; // Emotional states are quite important
      break;
    case 'goal':
      importance = 7; // Goals are important for context
      break;
    case 'event':
      importance = 6; // Events can be moderately important
      break;
    case 'preference':
      importance = 4; // Preferences are less critical
      break;
    case 'fact':
      importance = 5; // Facts are baseline important
      break;
  }

  // Adjust based on content length (longer = more detailed = potentially more important)
  const valueLength = memory.value.length;
  if (valueLength > 200) {
    importance = Math.min(10, importance + 1);
  } else if (valueLength < 30) {
    importance = Math.max(1, importance - 1);
  }

  // Adjust based on emotional keywords
  const emotionalKeywords = [
    'love', 'hate', 'fear', 'anxiety', 'depression', 'joy', 'happiness',
    'trauma', 'loss', 'death', 'birth', 'marriage', 'divorce',
    'achievement', 'failure', 'success', 'proud', 'ashamed',
    'family', 'friend', 'relationship', 'partner', 'spouse'
  ];

  const hasEmotionalContent = emotionalKeywords.some(keyword =>
    memory.value.toLowerCase().includes(keyword)
  );

  if (hasEmotionalContent) {
    importance = Math.min(10, importance + 1);
  }

  return {
    importance,
    processingTime: Date.now() - startTime,
    source: 'fallback',
  };
};

/**
 * Main function to evaluate memory importance
 */
export const evaluateMemoryImportance = async (
  memory: UserMemory,
  config?: AIServiceConfig
): Promise<ApiResponse<MemoryImportanceResponse>> => {
  try {
    console.log('🧠 [DEBUG] Evaluating memory importance:', { key: memory.key, value: memory.value });

    const aiConfig = config || getAIConfig();
    let result: MemoryImportanceResponse;

    // Try Local LLM first
    try {
      console.log('🧠 [DEBUG] Using Local LLM for importance evaluation');
      result = await evaluateImportanceWithLocalLLM(memory, aiConfig);
    } catch (error) {
      console.warn('🧠 [DEBUG] Local LLM evaluation failed, using fallback:', error);
      result = calculateFallbackImportance(memory);
    }

    console.log('🧠 [DEBUG] Memory importance evaluation result:', result);

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error('🧠 [DEBUG] Memory importance evaluation failed:', error);
    
    // Always provide a fallback
    const fallbackResult = calculateFallbackImportance(memory);
    
    return {
      success: true,
      data: fallbackResult,
      error: { message: 'Used fallback importance calculation' },
    };
  }
};

/**
 * Batch evaluate importance for multiple memories
 */
export const evaluateMemoriesImportance = async (
  memories: UserMemory[],
  config?: AIServiceConfig
): Promise<ApiResponse<MemoryImportanceResponse[]>> => {
  try {
    console.log('🧠 [DEBUG] Batch evaluating importance for', memories.length, 'memories');

    const results: MemoryImportanceResponse[] = [];
    
    // Process memories sequentially to avoid rate limiting
    for (const memory of memories) {
      const result = await evaluateMemoryImportance(memory, config);
      if (result.success && result.data) {
        results.push(result.data);
      } else {
        // Use fallback for failed evaluations
        results.push(calculateFallbackImportance(memory));
      }
      
      // Small delay to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error('🧠 [DEBUG] Batch importance evaluation failed:', error);
    return {
      success: false,
      error: { message: 'Failed to evaluate memories importance' },
    };
  }
};
