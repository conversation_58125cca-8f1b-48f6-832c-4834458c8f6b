-- Create user_memories table for storing user memory data
CREATE TABLE IF NOT EXISTS user_memories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    memory_key TEXT NOT NULL,
    memory_value TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('fact', 'emotion', 'event', 'goal', 'preference', 'identity')),
    source TEXT DEFAULT 'manual' CHECK (source IN ('manual', 'journal_entry', 'conversation', 'ai_extraction')),
    confidence_score DECIMAL(3,2) DEFAULT 1.0 CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    extraction_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique memory keys per user
    UNIQUE(user_id, memory_key)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_memories_user_id ON user_memories(user_id);
CREATE INDEX IF NOT EXISTS idx_user_memories_category ON user_memories(category);
CREATE INDEX IF NOT EXISTS idx_user_memories_source ON user_memories(source);
CREATE INDEX IF NOT EXISTS idx_user_memories_created_at ON user_memories(created_at);

-- Create full-text search index for memory content
CREATE INDEX IF NOT EXISTS idx_user_memories_search ON user_memories USING gin(
    to_tsvector('english', memory_key || ' ' || memory_value)
);

-- Enable Row Level Security (RLS)
ALTER TABLE user_memories ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own memories
CREATE POLICY "Users can view their own memories" ON user_memories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own memories" ON user_memories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own memories" ON user_memories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own memories" ON user_memories
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_memories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_update_user_memories_updated_at
    BEFORE UPDATE ON user_memories
    FOR EACH ROW
    EXECUTE FUNCTION update_user_memories_updated_at();

-- Create memory_extraction_stats table for tracking extraction statistics
CREATE TABLE IF NOT EXISTS memory_extraction_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    extraction_date DATE DEFAULT CURRENT_DATE,
    total_extractions INTEGER DEFAULT 0,
    successful_extractions INTEGER DEFAULT 0,
    memories_extracted INTEGER DEFAULT 0,
    average_processing_time_ms INTEGER DEFAULT 0,
    source_type TEXT NOT NULL CHECK (source_type IN ('journal_entry', 'conversation')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique stats per user per date per source
    UNIQUE(user_id, extraction_date, source_type)
);

-- Create indexes for extraction stats
CREATE INDEX IF NOT EXISTS idx_memory_extraction_stats_user_id ON memory_extraction_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_memory_extraction_stats_date ON memory_extraction_stats(extraction_date);

-- Enable RLS for extraction stats
ALTER TABLE memory_extraction_stats ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for extraction stats
CREATE POLICY "Users can view their own extraction stats" ON memory_extraction_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own extraction stats" ON memory_extraction_stats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own extraction stats" ON memory_extraction_stats
    FOR UPDATE USING (auth.uid() = user_id);

-- Create trigger for extraction stats updated_at
CREATE TRIGGER trigger_update_memory_extraction_stats_updated_at
    BEFORE UPDATE ON memory_extraction_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_user_memories_updated_at();

-- Create function to upsert extraction stats
CREATE OR REPLACE FUNCTION upsert_memory_extraction_stats(
    p_user_id UUID,
    p_source_type TEXT,
    p_total_extractions INTEGER DEFAULT 1,
    p_successful_extractions INTEGER DEFAULT 0,
    p_memories_extracted INTEGER DEFAULT 0,
    p_processing_time_ms INTEGER DEFAULT 0
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO memory_extraction_stats (
        user_id,
        source_type,
        total_extractions,
        successful_extractions,
        memories_extracted,
        average_processing_time_ms
    )
    VALUES (
        p_user_id,
        p_source_type,
        p_total_extractions,
        p_successful_extractions,
        p_memories_extracted,
        p_processing_time_ms
    )
    ON CONFLICT (user_id, extraction_date, source_type)
    DO UPDATE SET
        total_extractions = memory_extraction_stats.total_extractions + p_total_extractions,
        successful_extractions = memory_extraction_stats.successful_extractions + p_successful_extractions,
        memories_extracted = memory_extraction_stats.memories_extracted + p_memories_extracted,
        average_processing_time_ms = CASE 
            WHEN memory_extraction_stats.total_extractions = 0 THEN p_processing_time_ms
            ELSE (memory_extraction_stats.average_processing_time_ms * memory_extraction_stats.total_extractions + p_processing_time_ms) / (memory_extraction_stats.total_extractions + 1)
        END,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_memories TO authenticated;
GRANT ALL ON memory_extraction_stats TO authenticated;
GRANT EXECUTE ON FUNCTION upsert_memory_extraction_stats TO authenticated;
