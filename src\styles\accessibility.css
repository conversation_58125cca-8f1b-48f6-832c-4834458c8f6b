/**
 * Accessibility Styles
 * WCAG 2.1 AA compliant styles for improved accessibility
 */

/* Screen reader only content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.sr-only:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: inherit !important;
  margin: inherit !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Focus indicators for keyboard navigation */
.keyboard-navigation *:focus {
  outline: 2px solid #f59e0b !important;
  outline-offset: 2px !important;
  border-radius: 4px;
}

.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation textarea:focus,
.keyboard-navigation select:focus {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3) !important;
}

/* Skip links */
.skip-links {
  position: absolute;
  top: -40px;
  left: 6px;
  z-index: 1000;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  z-index: 1001;
  transition: top 0.2s ease;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --contrast-multiplier: 1.5;
    --text-contrast: #000;
    --bg-contrast: #fff;
    --border-contrast: #000;
  }

  .high-contrast {
    --foreground: var(--text-contrast);
    --background: var(--bg-contrast);
    --border: var(--border-contrast);
  }

  .high-contrast button,
  .high-contrast .card,
  .high-contrast .input {
    border: 2px solid var(--border-contrast) !important;
  }

  .high-contrast .text-muted-foreground {
    color: var(--text-contrast) !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .reduce-motion * {
    animation: none !important;
    transition: none !important;
  }
}

/* Large text support */
.large-text {
  font-size: 120% !important;
}

.large-text h1 {
  font-size: 3rem !important;
}

.large-text h2 {
  font-size: 2.5rem !important;
}

.large-text h3 {
  font-size: 2rem !important;
}

.large-text p,
.large-text span,
.large-text div {
  font-size: 1.2rem !important;
  line-height: 1.6 !important;
}

.large-text button {
  font-size: 1.1rem !important;
  padding: 12px 24px !important;
}

/* Color contrast improvements */
.text-muted-foreground {
  color: #4b5563 !important; /* Improved contrast */
}

/* Focus management for modals and dialogs */
.modal-open {
  overflow: hidden;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1040;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1050;
  max-height: 90vh;
  overflow-y: auto;
}

/* Improved button accessibility */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

button[aria-pressed="true"] {
  background-color: #f59e0b;
  color: white;
}

/* Form accessibility improvements */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.form-input:invalid {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-help {
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Loading states */
.loading {
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Improved link accessibility */
a:not(.button) {
  color: #2563eb;
  text-decoration: underline;
}

a:not(.button):hover {
  color: #1d4ed8;
}

a:not(.button):focus {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

/* Table accessibility */
table {
  border-collapse: collapse;
  width: 100%;
}

th {
  background-color: #f9fafb;
  font-weight: 600;
  text-align: left;
  padding: 12px;
  border: 1px solid #e5e7eb;
}

td {
  padding: 12px;
  border: 1px solid #e5e7eb;
}

/* List accessibility */
ul[role="list"],
ol[role="list"] {
  list-style: none;
  padding: 0;
}

/* Card accessibility improvements */
.card:focus-within {
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.3);
}

/* Toast/notification accessibility */
.toast {
  position: fixed;
  z-index: 1060;
  max-width: 400px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.toast[role="alert"] {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
}

.toast[role="status"] {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .large-text h1 {
    font-size: 2.5rem !important;
  }

  .large-text h2 {
    font-size: 2rem !important;
  }

  .large-text h3 {
    font-size: 1.5rem !important;
  }
}

/* Print accessibility */
@media print {
  .skip-links,
  .modal-backdrop,
  .toast {
    display: none !important;
  }

  a::after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
    color: #666;
  }

  a[href^="#"]::after,
  a[href^="javascript:"]::after {
    content: "";
  }
}
