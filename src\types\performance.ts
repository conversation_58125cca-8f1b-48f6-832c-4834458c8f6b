/**
 * Performance and Caching Types
 * Type definitions for performance optimization and caching strategies
 */

/**
 * Cache configuration options
 */
export interface PerformanceCacheConfig {
  /** Cache key prefix */
  keyPrefix: string;
  /** Time to live in milliseconds */
  ttl: number;
  /** Maximum number of cached items */
  maxSize?: number;
  /** Whether to persist cache to localStorage */
  persist?: boolean;
  /** Cache invalidation strategy */
  invalidationStrategy?: 'time' | 'manual' | 'dependency';
}

/**
 * Query cache configuration
 */
export interface QueryCacheConfig extends PerformanceCacheConfig {
  /** Stale time in milliseconds */
  staleTime: number;
  /** Cache time in milliseconds */
  cacheTime: number;
  /** Whether to refetch on window focus */
  refetchOnWindowFocus: boolean;
  /** Whether to refetch on reconnect */
  refetchOnReconnect: boolean;
  /** Background refetch interval */
  refetchInterval?: number;
  /** Retry configuration */
  retry: {
    attempts: number;
    delay: (attemptIndex: number) => number;
  };
}

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  /** Component render time */
  renderTime: number;
  /** API response time */
  apiResponseTime: number;
  /** Cache hit rate */
  cacheHitRate: number;
  /** Memory usage */
  memoryUsage: number;
  /** Bundle size */
  bundleSize?: number;
  /** Time to first contentful paint */
  firstContentfulPaint?: number;
  /** Largest contentful paint */
  largestContentfulPaint?: number;
  /** Cumulative layout shift */
  cumulativeLayoutShift?: number;
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceMonitoringConfig {
  /** Whether monitoring is enabled */
  enabled: boolean;
  /** Sampling rate (0-1) */
  samplingRate: number;
  /** Metrics to track */
  metricsToTrack: (keyof PerformanceMetrics)[];
  /** Reporting interval in milliseconds */
  reportingInterval: number;
  /** Maximum number of metrics to store */
  maxMetricsHistory: number;
}

/**
 * Cache entry interface
 */
export interface CacheEntry<T = any> {
  /** Cached data */
  data: T;
  /** Timestamp when cached */
  timestamp: number;
  /** Time to live */
  ttl: number;
  /** Access count */
  accessCount: number;
  /** Last accessed timestamp */
  lastAccessed: number;
  /** Cache key */
  key: string;
  /** Data size in bytes (estimated) */
  size?: number;
}

/**
 * Cache statistics
 */
export interface CacheStats {
  /** Total cache hits */
  hits: number;
  /** Total cache misses */
  misses: number;
  /** Cache hit rate */
  hitRate: number;
  /** Total cached items */
  totalItems: number;
  /** Total cache size in bytes */
  totalSize: number;
  /** Average access time */
  averageAccessTime: number;
  /** Most accessed keys */
  topKeys: Array<{ key: string; accessCount: number }>;
}

/**
 * Query key factory interface
 */
export interface QueryKeyFactory {
  /** Base key for the entity */
  base: string;
  /** Generate key for listing entities */
  list: (filters?: Record<string, any>) => string[];
  /** Generate key for single entity */
  detail: (id: string) => string[];
  /** Generate key for related entities */
  related: (id: string, relation: string) => string[];
  /** Generate key for search results */
  search: (query: string, filters?: Record<string, any>) => string[];
}

/**
 * Optimistic update configuration
 */
export interface OptimisticUpdateConfig<T> {
  /** Function to generate optimistic data */
  generateOptimisticData: (input: any) => T;
  /** Function to rollback on error */
  rollback: (previousData: T | undefined) => T | undefined;
  /** Whether to show loading state during optimistic update */
  showLoading?: boolean;
}

/**
 * Background sync configuration
 */
export interface BackgroundSyncConfig {
  /** Whether background sync is enabled */
  enabled: boolean;
  /** Sync interval in milliseconds */
  interval: number;
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Retry delay function */
  retryDelay: (attempt: number) => number;
  /** Conditions for triggering sync */
  triggers: Array<'online' | 'focus' | 'interval' | 'manual'>;
}

/**
 * Prefetch strategy configuration
 */
export interface PrefetchConfig {
  /** Whether prefetching is enabled */
  enabled: boolean;
  /** Prefetch trigger conditions */
  triggers: Array<'hover' | 'intersection' | 'idle' | 'manual'>;
  /** Delay before prefetching (ms) */
  delay: number;
  /** Maximum number of items to prefetch */
  maxItems: number;
  /** Prefetch priority */
  priority: 'low' | 'normal' | 'high';
}

/**
 * Performance optimization strategies
 */
export type OptimizationStrategy =
  | 'memoization'
  | 'virtualization'
  | 'lazy-loading'
  | 'code-splitting'
  | 'prefetching'
  | 'caching'
  | 'debouncing'
  | 'throttling';

/**
 * Component performance configuration
 */
export interface ComponentPerformanceConfig {
  /** Optimization strategies to apply */
  strategies: OptimizationStrategy[];
  /** Memoization dependencies */
  memoDependencies?: string[];
  /** Virtualization configuration */
  virtualization?: {
    itemHeight: number;
    overscan: number;
    threshold: number;
  };
  /** Lazy loading configuration */
  lazyLoading?: {
    threshold: string;
    rootMargin: string;
  };
}

/**
 * Performance budget interface
 */
export interface PerformanceBudget {
  /** Maximum bundle size in KB */
  maxBundleSize: number;
  /** Maximum render time in ms */
  maxRenderTime: number;
  /** Maximum API response time in ms */
  maxApiResponseTime: number;
  /** Minimum cache hit rate */
  minCacheHitRate: number;
  /** Maximum memory usage in MB */
  maxMemoryUsage: number;
}

/**
 * Performance alert configuration
 */
export interface PerformanceAlert {
  /** Alert type */
  type: 'warning' | 'error' | 'info';
  /** Metric that triggered the alert */
  metric: keyof PerformanceMetrics;
  /** Threshold value */
  threshold: number;
  /** Alert message */
  message: string;
  /** Timestamp when alert was triggered */
  timestamp: number;
  /** Suggested actions */
  suggestions?: string[];
}
