-- Run this SQL in your Supabase dashboard SQL editor to add the new AI reflection columns
-- Go to: https://supabase.com/dashboard/project/rgcrjnljnqdnhmntouso/sql

-- First, check if columns already exist
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'journal_entries' 
  AND table_schema = 'public'
ORDER BY column_name;

-- Add new AI reflection columns if they don't exist
DO $$ 
BEGIN
    -- Check and add ai_reflection column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'journal_entries' 
          AND column_name = 'ai_reflection'
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_reflection TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_reflection IS 'AI-generated reflection based on the journal entry content and mood (legacy)';
    END IF;

    -- Check and add ai_summary column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'journal_entries' 
          AND column_name = 'ai_summary'
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_summary TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_summary IS 'AI-generated 1-2 sentence friendly summary';
    END IF;

    -- Check and add ai_emotion column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'journal_entries' 
          AND column_name = 'ai_emotion'
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_emotion TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_emotion IS 'AI-detected main emotion from the journal entry';
    END IF;

    -- Check and add ai_encouragement column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'journal_entries' 
          AND column_name = 'ai_encouragement'
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_encouragement TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_encouragement IS 'AI-generated supportive friend response';
    END IF;

    -- Check and add ai_reflection_question column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'journal_entries' 
          AND column_name = 'ai_reflection_question'
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.journal_entries ADD COLUMN ai_reflection_question TEXT;
        COMMENT ON COLUMN public.journal_entries.ai_reflection_question IS 'AI-generated gentle reflection question';
    END IF;

END $$;

-- Verify the columns were added
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'journal_entries' 
  AND table_schema = 'public'
  AND column_name LIKE 'ai_%'
ORDER BY column_name;
