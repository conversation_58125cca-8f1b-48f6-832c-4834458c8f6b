/**
 * SEO Service
 * Manages SEO meta tags, structured data, and search engine optimization
 */

import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * SEO meta tag configuration
 */
export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  author?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  siteName?: string;
  locale?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  robots?: string;
  canonical?: string;
}

/**
 * Structured data schema types
 */
export interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

/**
 * SEO service for managing meta tags and structured data
 */
class SEOService {
  private defaultConfig: SEOConfig;

  constructor() {
    const env = getEnvironmentConfig();
    
    this.defaultConfig = {
      title: 'AmberGlow - Personal Journaling & Reflection',
      description: 'A beautiful, secure journaling application with AI-powered reflection features to help you grow and understand yourself better.',
      keywords: ['journaling', 'reflection', 'personal growth', 'AI', 'mindfulness', 'self-improvement'],
      author: 'AmberGlow Team',
      siteName: 'AmberGlow',
      type: 'website',
      locale: 'en_US',
      twitterCard: 'summary_large_image',
      robots: env.isProduction ? 'index,follow' : 'noindex,nofollow',
    };
  }

  /**
   * Set meta tags for the current page
   */
  public setMetaTags(config: Partial<SEOConfig>): void {
    const finalConfig = { ...this.defaultConfig, ...config };

    // Set document title
    document.title = finalConfig.title;

    // Basic meta tags
    this.setMetaTag('description', finalConfig.description);
    this.setMetaTag('author', finalConfig.author || '');
    this.setMetaTag('robots', finalConfig.robots || '');
    
    if (finalConfig.keywords) {
      this.setMetaTag('keywords', finalConfig.keywords.join(', '));
    }

    // Canonical URL
    if (finalConfig.canonical) {
      this.setLinkTag('canonical', finalConfig.canonical);
    }

    // Open Graph tags
    this.setMetaProperty('og:title', finalConfig.title);
    this.setMetaProperty('og:description', finalConfig.description);
    this.setMetaProperty('og:type', finalConfig.type || 'website');
    this.setMetaProperty('og:site_name', finalConfig.siteName || '');
    this.setMetaProperty('og:locale', finalConfig.locale || '');
    
    if (finalConfig.url) {
      this.setMetaProperty('og:url', finalConfig.url);
    }
    
    if (finalConfig.image) {
      this.setMetaProperty('og:image', finalConfig.image);
      this.setMetaProperty('og:image:alt', finalConfig.title);
    }

    // Twitter Card tags
    this.setMetaName('twitter:card', finalConfig.twitterCard || 'summary');
    this.setMetaName('twitter:title', finalConfig.title);
    this.setMetaName('twitter:description', finalConfig.description);
    
    if (finalConfig.image) {
      this.setMetaName('twitter:image', finalConfig.image);
    }

    // Viewport and mobile optimization
    this.setMetaName('viewport', 'width=device-width, initial-scale=1.0');
    this.setMetaName('theme-color', '#f59e0b'); // AmberGlow brand color
    this.setMetaName('apple-mobile-web-app-capable', 'yes');
    this.setMetaName('apple-mobile-web-app-status-bar-style', 'default');
  }

  /**
   * Set structured data (JSON-LD)
   */
  public setStructuredData(data: StructuredData): void {
    // Remove existing structured data
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Add new structured data
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(data);
    document.head.appendChild(script);
  }

  /**
   * Set website structured data
   */
  public setWebsiteStructuredData(): void {
    const env = getEnvironmentConfig();
    const baseUrl = env.isProduction ? 'https://amberglow.app' : 'http://localhost:8080';

    const structuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebApplication',
      name: 'AmberGlow',
      description: this.defaultConfig.description,
      url: baseUrl,
      applicationCategory: 'LifestyleApplication',
      operatingSystem: 'Web Browser',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
      },
      author: {
        '@type': 'Organization',
        name: 'AmberGlow Team',
      },
      featureList: [
        'Personal Journaling',
        'AI-Powered Reflection',
        'Secure Data Storage',
        'Cross-Platform Access',
        'Privacy-First Design',
      ],
    };

    this.setStructuredData(structuredData);
  }

  /**
   * Set article structured data for journal entries
   */
  public setArticleStructuredData(title: string, content: string, datePublished: string): void {
    const structuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: title,
      articleBody: content.substring(0, 200) + '...', // Truncated for privacy
      datePublished,
      dateModified: datePublished,
      author: {
        '@type': 'Person',
        name: 'Journal Author', // Keep anonymous for privacy
      },
      publisher: {
        '@type': 'Organization',
        name: 'AmberGlow',
      },
    };

    this.setStructuredData(structuredData);
  }

  /**
   * Helper method to set meta tag by name
   */
  private setMetaTag(name: string, content: string): void {
    if (!content) return;
    
    let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.name = name;
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  /**
   * Helper method to set meta property (for Open Graph)
   */
  private setMetaProperty(property: string, content: string): void {
    if (!content) return;
    
    let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute('property', property);
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  /**
   * Helper method to set meta name (for Twitter Cards)
   */
  private setMetaName(name: string, content: string): void {
    if (!content) return;
    
    let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
    if (!meta) {
      meta = document.createElement('meta');
      meta.name = name;
      document.head.appendChild(meta);
    }
    meta.content = content;
  }

  /**
   * Helper method to set link tag
   */
  private setLinkTag(rel: string, href: string): void {
    if (!href) return;
    
    let link = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;
    if (!link) {
      link = document.createElement('link');
      link.rel = rel;
      document.head.appendChild(link);
    }
    link.href = href;
  }

  /**
   * Get current page URL
   */
  private getCurrentUrl(): string {
    return window.location.href;
  }

  /**
   * Initialize default SEO for the application
   */
  public initializeDefaultSEO(): void {
    this.setMetaTags({
      url: this.getCurrentUrl(),
    });
    this.setWebsiteStructuredData();
  }
}

// Export singleton instance
export const seoService = new SEOService();

// Export utility functions
export const setPageSEO = (config: Partial<SEOConfig>) => seoService.setMetaTags(config);
export const setPageStructuredData = (data: StructuredData) => seoService.setStructuredData(data);
export const initializeSEO = () => seoService.initializeDefaultSEO();
