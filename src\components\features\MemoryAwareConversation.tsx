/**
 * Memory-Aware Conversation Component
 * Example component showing how to integrate memory context with AI conversations
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Brain, MessageCircle, Loader2, User, Bot } from 'lucide-react';
import { useMemoryContext } from '@/hooks/useMemoryContext';
import { generateAIConversationResponse } from '@/services/aiConversationService';
import { AIConversationInput, ConversationMessage } from '@/types';

/**
 * Props for MemoryAwareConversation component
 */
interface MemoryAwareConversationProps {
  /** Journal entry context for the conversation */
  journalEntry: {
    title: string;
    content: string;
    emotion: string;
    moodScore: number;
  };
  /** Whether to show memory context */
  showMemoryContext?: boolean;
}

/**
 * Memory-aware conversation component
 */
export const MemoryAwareConversation: React.FC<MemoryAwareConversationProps> = ({
  journalEntry,
  showMemoryContext = true,
}) => {
  const { memories, getMemoryContext, isExtracting } = useMemoryContext();
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [userInput, setUserInput] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [memoryContextText, setMemoryContextText] = useState('');

  /**
   * Update memory context when memories change
   */
  useEffect(() => {
    const context = getMemoryContext();
    setMemoryContextText(context);
  }, [memories, getMemoryContext]);

  /**
   * Handle sending a message
   */
  const handleSendMessage = async () => {
    if (!userInput.trim() || isGenerating) return;

    const userMessage: ConversationMessage = {
      id: `user-${Date.now()}`,
      conversation_id: 'demo',
      sender_type: 'user',
      message_content: userInput.trim(),
      message_type: 'text',
      created_at: new Date().toISOString(),
    };

    setMessages(prev => [...prev, userMessage]);
    setUserInput('');
    setIsGenerating(true);

    try {
      // Prepare conversation input
      const conversationInput: AIConversationInput = {
        user_message: userInput.trim(),
        conversation_history: messages,
        journal_entry: {
          title: journalEntry.title,
          content: journalEntry.content,
          emotion: journalEntry.emotion,
          mood_score: journalEntry.moodScore,
        },
      };

      // Generate AI response with memory context
      const response = await generateAIConversationResponse(
        conversationInput,
        undefined, // Use default config
        memoryContextText // Pass memory context
      );

      if (response.success && response.data) {
        const aiMessage: ConversationMessage = {
          id: `ai-${Date.now()}`,
          conversation_id: 'demo',
          sender_type: 'ai',
          message_content: response.data.message,
          message_type: response.data.message_type,
          ai_metadata: response.data.metadata,
          created_at: new Date().toISOString(),
        };

        setMessages(prev => [...prev, aiMessage]);
      } else {
        console.error('AI conversation failed:', response.error);
        // Add fallback message
        const fallbackMessage: ConversationMessage = {
          id: `ai-fallback-${Date.now()}`,
          conversation_id: 'demo',
          sender_type: 'ai',
          message_content: "I'm here to listen and support you. Could you tell me more about how you're feeling?",
          message_type: 'follow_up',
          created_at: new Date().toISOString(),
        };
        setMessages(prev => [...prev, fallbackMessage]);
      }
    } catch (error) {
      console.error('Conversation error:', error);
      // Add error fallback message
      const errorMessage: ConversationMessage = {
        id: `ai-error-${Date.now()}`,
        conversation_id: 'demo',
        sender_type: 'ai',
        message_content: "I'm having trouble responding right now, but I'm here for you. Please try again.",
        message_type: 'follow_up',
        created_at: new Date().toISOString(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };

  /**
   * Handle key press in input
   */
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="space-y-4">
      {/* Memory Context Display */}
      {showMemoryContext && memories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              <Brain className="h-4 w-4 text-amber-600" />
              Memory Context ({memories.length} memories)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex flex-wrap gap-2">
                {memories.slice(0, 5).map((memory, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {memory.key}: {memory.value.substring(0, 30)}
                    {memory.value.length > 30 ? '...' : ''}
                  </Badge>
                ))}
                {memories.length > 5 && (
                  <Badge variant="outline" className="text-xs">
                    +{memories.length - 5} more
                  </Badge>
                )}
              </div>
              <p className="text-xs text-gray-500">
                💡 These memories help Amber provide more personalized responses
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conversation Display */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-amber-600" />
            Memory-Aware Conversation with Amber
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Messages */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {messages.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                <MessageCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>Start a conversation with Amber about your journal entry</p>
                <p className="text-sm">
                  {memories.length > 0 
                    ? "Amber will remember your previous conversations and experiences"
                    : "Add some memories first to see personalized responses"
                  }
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.sender_type === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex gap-2 max-w-[80%] ${
                    message.sender_type === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {message.sender_type === 'user' ? (
                      <User className="h-6 w-6 text-blue-600" />
                    ) : (
                      <Bot className="h-6 w-6 text-amber-600" />
                    )}
                  </div>
                  <div
                    className={`p-3 rounded-lg ${
                      message.sender_type === 'user'
                        ? 'bg-blue-100 text-blue-900'
                        : 'bg-amber-50 text-amber-900'
                    }`}
                  >
                    <p className="text-sm">{message.message_content}</p>
                    <p className="text-xs opacity-70 mt-1">
                      {new Date(message.created_at).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}

            {/* Generating indicator */}
            {isGenerating && (
              <div className="flex gap-3 justify-start">
                <div className="flex gap-2">
                  <Bot className="h-6 w-6 text-amber-600" />
                  <div className="bg-amber-50 text-amber-900 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Amber is thinking...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Input Area */}
          <div className="space-y-2">
            <Textarea
              placeholder="Share your thoughts with Amber..."
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyPress={handleKeyPress}
              rows={3}
              className="resize-none"
              disabled={isGenerating}
            />
            <div className="flex justify-between items-center">
              <p className="text-xs text-gray-500">
                Press Enter to send, Shift+Enter for new line
              </p>
              <Button
                onClick={handleSendMessage}
                disabled={!userInput.trim() || isGenerating}
                size="sm"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  'Send Message'
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
