/**
 * Conversation Hooks
 * Custom hooks for managing reflection conversations with React Query
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useMemoryContext } from './useMemoryContext';
import {
  ReflectionConversation,
  ConversationMessage,
  CreateConversationInput,
  CreateMessageInput,
  AIConversationInput,
  ConversationState,
  ConversationActions,
  JournalEntry,
} from '@/types';
import {
  createReflectionConversation,
  getConversationByJournalEntry,
  getConversationMessages,
  createConversationMessage,
  getConversationWithMessages,
  deleteConversation,
  getUserConversationCount,
  getOldConversations,
  bulkDeleteConversations,
  getConversationAnalytics,
} from '@/services/conversationService';
import { generateAIConversationResponse } from '@/services/aiConversationService';
import { extractMemoriesFromConversation } from '@/services/memoryExtractionService';
import { useState, useCallback, useMemo, useEffect } from 'react';
import { toast } from 'sonner';

/**
 * Query keys for conversation-related queries
 */
export const conversationQueryKeys = {
  all: ['conversations'] as const,
  byJournalEntry: (journalEntryId: string) => 
    [...conversationQueryKeys.all, 'journal-entry', journalEntryId] as const,
  conversation: (conversationId: string) => 
    [...conversationQueryKeys.all, 'conversation', conversationId] as const,
  messages: (conversationId: string) => 
    [...conversationQueryKeys.conversation(conversationId), 'messages'] as const,
  userCount: (userId: string) => 
    [...conversationQueryKeys.all, 'user-count', userId] as const,
};

/**
 * Hook to get conversation by journal entry
 */
export const useConversationByJournalEntry = (journalEntryId: string) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: conversationQueryKeys.byJournalEntry(journalEntryId),
    queryFn: async () => {
      if (!user) throw new Error('User not authenticated');
      const result = await getConversationByJournalEntry(journalEntryId, user.id);
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch conversation');
      }
      return result.data;
    },
    enabled: !!user && !!journalEntryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: true,
  });
};

/**
 * Hook to get conversation messages
 */
export const useConversationMessages = (
  conversationId: string | null,
  options: {
    limit?: number;
    offset?: number;
    enabled?: boolean;
  } = {}
) => {
  const { limit = 50, offset = 0, enabled = true } = options;

  return useQuery({
    queryKey: conversationQueryKeys.messages(conversationId || ''),
    queryFn: async () => {
      if (!conversationId) return [];
      const result = await getConversationMessages(conversationId, {
        limit,
        offset,
        orderBy: 'created_at',
        ascending: true,
      });
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch messages');
      }
      return result.data || [];
    },
    enabled: enabled && !!conversationId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchOnMount: true,
  });
};

/**
 * Hook to create a new conversation
 */
export const useCreateConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createReflectionConversation,
    onSuccess: (result, variables) => {
      if (result.success && result.data) {
        // Invalidate and update conversation queries
        queryClient.invalidateQueries({
          queryKey: conversationQueryKeys.byJournalEntry(variables.journal_entry_id),
        });
        
        // Set the new conversation in cache
        queryClient.setQueryData(
          conversationQueryKeys.byJournalEntry(variables.journal_entry_id),
          result.data
        );

        toast.success('Conversation started with Amber!');
      } else {
        toast.error(result.error?.message || 'Failed to start conversation');
      }
    },
    onError: (error) => {
      console.error('Error creating conversation:', error);
      toast.error('Failed to start conversation. Please try again.');
    },
  });
};

/**
 * Hook to send a message in a conversation with optimistic updates
 */
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createConversationMessage,
    onMutate: async (variables) => {
      // Cancel any outgoing refetches
      const messagesQueryKey = conversationQueryKeys.messages(variables.conversation_id);
      await queryClient.cancelQueries({ queryKey: messagesQueryKey });

      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData<ConversationMessage[]>(messagesQueryKey);

      // Optimistically update to the new value
      const optimisticMessage: ConversationMessage = {
        id: `temp-${Date.now()}`, // Temporary ID
        conversation_id: variables.conversation_id,
        sender_type: variables.sender_type,
        message_content: variables.message_content,
        message_type: variables.message_type || 'text',
        ai_metadata: variables.ai_metadata || null,
        created_at: new Date().toISOString(),
      };

      queryClient.setQueryData(messagesQueryKey, (old: ConversationMessage[] = []) => [
        ...old,
        optimisticMessage,
      ]);

      // Return a context object with the snapshotted value
      return { previousMessages, optimisticMessage };
    },
    onSuccess: (result, variables, context) => {
      if (result.success && result.data) {
        // Replace optimistic message with real message
        const messagesQueryKey = conversationQueryKeys.messages(variables.conversation_id);

        queryClient.setQueryData(messagesQueryKey, (oldMessages: ConversationMessage[] = []) => {
          return oldMessages.map(msg =>
            msg.id === context?.optimisticMessage.id ? result.data! : msg
          );
        });

        // Invalidate conversation queries to update timestamps
        queryClient.invalidateQueries({
          queryKey: conversationQueryKeys.conversation(variables.conversation_id),
        });
      } else {
        toast.error(result.error?.message || 'Failed to send message');
      }
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      const messagesQueryKey = conversationQueryKeys.messages(variables.conversation_id);
      if (context?.previousMessages) {
        queryClient.setQueryData(messagesQueryKey, context.previousMessages);
      }

      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
    },
  });
};

/**
 * Hook to generate AI response with enhanced error handling and memory context
 */
export const useGenerateAIResponse = () => {
  const queryClient = useQueryClient();
  const sendMessage = useSendMessage();
  const { getMemoryContext } = useMemoryContext();

  return useMutation({
    mutationFn: async (input: AIConversationInput & { conversationId: string }) => {
      const { conversationId, ...aiInput } = input;

      try {
        // Get memory context for personalized responses
        const memoryContext = getMemoryContext();

        // Generate AI response with memory context
        const aiResult = await generateAIConversationResponse(aiInput, undefined, memoryContext);

        if (!aiResult.success || !aiResult.data) {
          throw new Error(aiResult.error?.message || 'Failed to generate AI response');
        }

        // Send AI message (this will use optimistic updates)
        const messageResult = await sendMessage.mutateAsync({
          conversation_id: conversationId,
          sender_type: 'ai',
          message_content: aiResult.data.message,
          message_type: aiResult.data.message_type,
          ai_metadata: aiResult.data.metadata,
        });

        // Automatically extract memories from the conversation (fire and forget)
        setTimeout(async () => {
          try {
            console.log('🧠 Automatically extracting memories from conversation...');
            const conversationMessages = [
              ...aiInput.conversation_history,
              { sender_type: 'user' as const, message_content: aiInput.user_message },
              { sender_type: 'ai' as const, message_content: aiResult.data.message },
            ];

            const memoryResult = await extractMemoriesFromConversation({
              messages: conversationMessages,
              journalContext: aiInput.journal_entry,
            });

            if (memoryResult.success && memoryResult.memories.length > 0) {
              console.log(`🧠 Successfully extracted ${memoryResult.memories.length} memories from conversation`);
              // Show subtle notification for memory discovery
              toast.success(`💾 Learned ${memoryResult.memories.length} new things about you from this conversation`, {
                duration: 4000,
                position: 'bottom-right',
              });
            }
          } catch (error) {
            // Memory extraction failure should not affect conversation
            console.warn('🧠 Automatic memory extraction failed (non-critical):', error);
          }
        }, 500); // Small delay to ensure conversation UI updates first

        return {
          aiResponse: aiResult.data,
          messageResult,
        };
      } catch (error) {
        // Log detailed error for debugging
        console.error('AI response generation failed:', {
          error,
          conversationId,
          userMessage: aiInput.user_message,
          historyLength: aiInput.conversation_history.length,
        });
        throw error;
      }
    },
    retry: (failureCount, error) => {
      // Retry up to 2 times for certain errors
      if (failureCount < 2) {
        const errorMessage = error?.message?.toLowerCase() || '';
        // Retry for network errors or temporary API issues
        if (errorMessage.includes('network') ||
            errorMessage.includes('timeout') ||
            errorMessage.includes('rate limit')) {
          return true;
        }
      }
      return false;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
    onError: (error, variables) => {
      console.error('Error generating AI response:', error);

      // Provide specific error messages based on error type
      const errorMessage = error?.message?.toLowerCase() || '';
      if (errorMessage.includes('rate limit')) {
        toast.error('Amber is busy right now. Please wait a moment and try again.');
      } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
        toast.error('Connection issue. Please check your internet and try again.');
      } else {
        toast.error('Amber is having trouble responding. Please try again.');
      }
    },
  });
};

/**
 * Hook to delete a conversation
 */
export const useDeleteConversation = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (conversationId: string) => {
      if (!user) throw new Error('User not authenticated');
      return deleteConversation(conversationId, user.id);
    },
    onSuccess: (result, conversationId) => {
      if (result.success) {
        // Remove from all relevant caches
        queryClient.removeQueries({
          queryKey: conversationQueryKeys.conversation(conversationId),
        });
        
        // Invalidate journal entry conversation queries
        queryClient.invalidateQueries({
          queryKey: conversationQueryKeys.all,
        });

        toast.success('Conversation deleted');
      } else {
        toast.error(result.error?.message || 'Failed to delete conversation');
      }
    },
    onError: (error) => {
      console.error('Error deleting conversation:', error);
      toast.error('Failed to delete conversation. Please try again.');
    },
  });
};

/**
 * Main conversation hook that provides complete conversation state and actions
 */
export const useConversationManager = (
  journalEntryId: string,
  journalEntry?: JournalEntry,
  autoInitialize: boolean = false
) => {
  const { user } = useAuth();
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasAutoInitialized, setHasAutoInitialized] = useState(false);

  // Get conversation for this journal entry
  const conversationQuery = useConversationByJournalEntry(journalEntryId);
  const conversation = conversationQuery.data;
  
  // Get messages for the conversation
  const messagesQuery = useConversationMessages(conversation?.id || null, {
    enabled: !!conversation,
  });
  
  // Mutations
  const createConversation = useCreateConversation();
  const sendMessage = useSendMessage();
  const generateAIResponse = useGenerateAIResponse();
  const deleteConversationMutation = useDeleteConversation();

  // Auto-initialize conversation if requested and conditions are met
  useEffect(() => {
    if (
      autoInitialize &&
      !hasAutoInitialized &&
      !conversation &&
      !conversationQuery.isLoading &&
      user &&
      journalEntryId
    ) {
      setHasAutoInitialized(true);
      createConversation.mutateAsync({
        journal_entry_id: journalEntryId,
        user_id: user.id,
      }).catch((error) => {
        console.error('Auto-initialization failed:', error);
        setHasAutoInitialized(false); // Reset to allow retry
      });
    }
  }, [
    autoInitialize,
    hasAutoInitialized,
    conversation,
    conversationQuery.isLoading,
    user,
    journalEntryId,
    createConversation
  ]);

  // Memoized state
  const state: ConversationState = useMemo(() => ({
    conversation: conversation || null,
    messages: messagesQuery.data || [],
    isLoading: conversationQuery.isLoading || messagesQuery.isLoading,
    isGenerating,
    error: conversationQuery.error?.message || messagesQuery.error?.message || null,
    isInitialized: !conversationQuery.isLoading,
  }), [
    conversation,
    messagesQuery.data,
    conversationQuery.isLoading,
    messagesQuery.isLoading,
    isGenerating,
    conversationQuery.error,
    messagesQuery.error,
  ]);

  // Actions
  const actions: ConversationActions = useMemo(() => ({
    sendMessage: async (message: string) => {
      if (!conversation || !user) {
        toast.error('Please start a conversation first');
        return;
      }

      if (!message.trim()) {
        toast.error('Please enter a message');
        return;
      }

      try {
        // Send user message
        await sendMessage.mutateAsync({
          conversation_id: conversation.id,
          sender_type: 'user',
          message_content: message.trim(),
          message_type: 'text',
        });

        // Generate AI response
        setIsGenerating(true);
        await generateAIResponse.mutateAsync({
          conversationId: conversation.id,
          user_message: message.trim(),
          conversation_history: messagesQuery.data || [],
          journal_entry: {
            title: journalEntry?.title || '',
            content: journalEntry?.content || '',
            emotion: journalEntry?.emotion || 'neutral',
            mood_score: journalEntry?.mood_score || 5,
          },
          initial_reflection: {
            summary: journalEntry?.ai_summary || '',
            emotion: journalEntry?.ai_emotion || '',
            encouragement: journalEntry?.ai_encouragement || '',
            reflection_question: journalEntry?.ai_reflection_question || '',
          },
        });
      } catch (error) {
        console.error('Error in sendMessage action:', error);
        // Error handling is done in the mutation hooks
      } finally {
        setIsGenerating(false);
      }
    },

    startConversation: async (journalEntryId: string) => {
      if (!user) return;
      
      await createConversation.mutateAsync({
        journal_entry_id: journalEntryId,
        user_id: user.id,
      });
    },

    loadConversation: async (conversationId: string) => {
      // This is handled automatically by React Query
      conversationQuery.refetch();
      messagesQuery.refetch();
    },

    clearConversation: () => {
      // Reset local state
      setIsGenerating(false);
    },

    retryLastResponse: async () => {
      if (!conversation || !messagesQuery.data) return;
      
      const lastUserMessage = [...messagesQuery.data]
        .reverse()
        .find(msg => msg.sender_type === 'user');
      
      if (lastUserMessage) {
        setIsGenerating(true);
        try {
          await generateAIResponse.mutateAsync({
            conversationId: conversation.id,
            user_message: lastUserMessage.message_content,
            conversation_history: messagesQuery.data,
            journal_entry: {
              title: '',
              content: '',
              emotion: 'neutral',
              mood_score: 5,
            },
          });
        } finally {
          setIsGenerating(false);
        }
      }
    },
  }), [
    conversation,
    user,
    messagesQuery.data,
    sendMessage,
    generateAIResponse,
    createConversation,
    conversationQuery,
    messagesQuery,
  ]);

  return {
    state,
    actions,
    mutations: {
      createConversation,
      sendMessage,
      generateAIResponse,
      deleteConversation: deleteConversationMutation,
    },
  };
};

/**
 * Hook to get user's conversation count
 */
export const useUserConversationCount = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: conversationQueryKeys.userCount(user?.id || ''),
    queryFn: async () => {
      if (!user) throw new Error('User not authenticated');
      const result = await getUserConversationCount(user.id);
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch conversation count');
      }
      return result.data || 0;
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to get old conversations for cleanup
 */
export const useOldConversations = (daysOld: number = 90) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: [...conversationQueryKeys.all, 'old', daysOld, user?.id],
    queryFn: async () => {
      if (!user) throw new Error('User not authenticated');
      const result = await getOldConversations(user.id, daysOld);
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch old conversations');
      }
      return result.data || [];
    },
    enabled: !!user,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to bulk delete conversations
 */
export const useBulkDeleteConversations = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (conversationIds: string[]) => {
      if (!user) throw new Error('User not authenticated');
      return bulkDeleteConversations(conversationIds, user.id);
    },
    onSuccess: (result, conversationIds) => {
      if (result.success) {
        // Remove deleted conversations from cache
        conversationIds.forEach(id => {
          queryClient.removeQueries({
            queryKey: conversationQueryKeys.conversation(id),
          });
        });

        // Invalidate all conversation-related queries
        queryClient.invalidateQueries({
          queryKey: conversationQueryKeys.all,
        });

        toast.success(`Deleted ${result.data?.deletedCount || 0} conversation(s)`);
      } else {
        toast.error(result.error?.message || 'Failed to delete conversations');
      }
    },
    onError: (error) => {
      console.error('Error bulk deleting conversations:', error);
      toast.error('Failed to delete conversations. Please try again.');
    },
  });
};

/**
 * Hook to get conversation analytics
 */
export const useConversationAnalytics = (timeRange?: {
  startDate?: string;
  endDate?: string;
}) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: [...conversationQueryKeys.all, 'analytics', user?.id, timeRange],
    queryFn: async () => {
      if (!user) throw new Error('User not authenticated');
      const result = await getConversationAnalytics(user.id, timeRange);
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch analytics');
      }
      return result.data;
    },
    enabled: !!user,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};
