/**
 * Settings Service
 * Centralized service for settings page operations
 */

import { supabase } from '@/integrations/supabase/client';
import {
  UserSettingsFormData,
  UserSettingsUpdatePayload,
  UserSettingsUpdateResponse,
  BulkDeleteResponse,
  BulkDeleteResult,
  ApiResponse,
  ApiError,
} from '@/types';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * Map database errors to API errors
 */
const mapDatabaseError = (error: PostgrestError | Error): ApiError => {
  if ('code' in error && error.code) {
    switch (error.code) {
      case '23505':
        return {
          code: 'DUPLICATE_ENTRY',
          message: 'A record with this information already exists.',
          retryable: false,
        };
      case '23503':
        return {
          code: 'FOREIGN_KEY_VIOLATION',
          message: 'Referenced record does not exist.',
          retryable: false,
        };
      case 'PGRST116':
        return {
          code: 'NOT_FOUND',
          message: 'Record not found.',
          retryable: false,
        };
      default:
        return {
          code: 'DATABASE_ERROR',
          message: error.message || 'Database operation failed.',
          retryable: true,
        };
    }
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'An unexpected error occurred.',
    retryable: true,
  };
};

/**
 * Get user settings/profile data
 */
export const getUserSettings = async (userId: string): Promise<ApiResponse<UserSettingsFormData>> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name')
      .eq('id', userId)
      .single();

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    if (!data) {
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'User profile not found.',
          retryable: false,
        },
      };
    }

    const userSettings: UserSettingsFormData = {
      full_name: data.full_name || '',
      email: data.email || '',
    };

    return {
      success: true,
      data: userSettings,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Update user settings/profile
 */
export const updateUserSettings = async (
  userId: string,
  updateData: UserSettingsUpdatePayload
): Promise<UserSettingsUpdateResponse> => {
  try {
    // Validate input
    if (!updateData.full_name?.trim()) {
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Full name is required.',
          retryable: false,
        },
      };
    }

    const { data, error } = await supabase
      .from('profiles')
      .update({
        full_name: updateData.full_name.trim(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select('id, email, full_name')
      .single();

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    if (!data) {
      return {
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: 'Failed to update user profile.',
          retryable: true,
        },
      };
    }

    const updatedProfile: UserSettingsFormData = {
      full_name: data.full_name || '',
      email: data.email || '',
    };

    return {
      success: true,
      data: {
        profile: updatedProfile,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Delete all journal entries for a user (bulk delete)
 */
export const deleteAllJournalEntries = async (userId: string): Promise<BulkDeleteResponse> => {
  try {
    // First, get count of entries to be deleted
    const { count, error: countError } = await supabase
      .from('journal_entries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      return {
        success: false,
        error: mapDatabaseError(countError),
      };
    }

    const entryCount = count || 0;

    // If no entries to delete, return success
    if (entryCount === 0) {
      return {
        success: true,
        data: {
          deletedCount: 0,
          success: true,
        },
        meta: {
          timestamp: new Date().toISOString(),
        },
      };
    }

    // Delete all entries for the user
    const { error: deleteError } = await supabase
      .from('journal_entries')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      return {
        success: false,
        error: mapDatabaseError(deleteError),
      };
    }

    const result: BulkDeleteResult = {
      deletedCount: entryCount,
      success: true,
    };

    return {
      success: true,
      data: result,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Validate user settings form data
 */
export const validateUserSettings = (data: UserSettingsUpdatePayload): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  // Validate full name
  if (!data.full_name?.trim()) {
    errors.full_name = 'Full name is required.';
  } else if (data.full_name.trim().length < 2) {
    errors.full_name = 'Full name must be at least 2 characters long.';
  } else if (data.full_name.trim().length > 100) {
    errors.full_name = 'Full name must be less than 100 characters.';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
