/**
 * Journal Operations Hook
 * Centralized hook for journal entry CRUD operations, validation, and data transformation
 * Consolidates duplicated logic from JournalEntry, EditJournalEntryModal, and other components
 */

import { useState } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { generateAIReflection } from '@/services/aiReflectionService';
import { utcTimestampToLocalDateString } from '@/utils/dateUtils';
import {
  FormattedJournalEntry,
  EmotionType,
  MoodScore,
  AIReflection as AIReflectionType,
} from '@/types';

interface JournalEntryData {
  title: string;
  content: string;
  emotion: EmotionType | '';
  moodScore: MoodScore;
}

interface CreateJournalEntryParams extends JournalEntryData {
  aiReflection?: AIReflectionType;
}

interface UpdateJournalEntryParams extends JournalEntryData {
  id: string;
  aiReflection?: AIReflectionType;
}

/**
 * Hook for managing journal entry operations
 */
export const useJournalOperations = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Validates journal entry data
   */
  const validateJournalEntry = (data: JournalEntryData): boolean => {
    if (!data.title.trim()) {
      toast.error('Please enter a title for your entry');
      return false;
    }

    if (!data.content.trim()) {
      toast.error('Please share your thoughts and feelings');
      return false;
    }

    if (!data.emotion) {
      toast.error('Please select how you\'re feeling');
      return false;
    }

    return true;
  };

  /**
   * Creates a new journal entry
   */
  const createJournalEntry = async (params: CreateJournalEntryParams): Promise<FormattedJournalEntry | null> => {
    if (!user) {
      toast.error('You must be logged in to save entries');
      return null;
    }

    if (!validateJournalEntry(params)) {
      return null;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase
        .from('journal_entries')
        .insert({
          user_id: user.id,
          title: params.title.trim(),
          content: params.content.trim(),
          emotion: params.emotion,
          mood_score: params.moodScore,
          ai_reflection: params.aiReflection?.reflection || null,
          ai_summary: params.aiReflection?.summary || null,
          ai_emotion: params.aiReflection?.emotion || null,
          ai_encouragement: params.aiReflection?.encouragement || null,
          ai_reflection_question: params.aiReflection?.reflection_question || null,
        })
        .select()
        .single();

      if (error) throw error;

      const entry: FormattedJournalEntry = {
        id: data.id,
        date: utcTimestampToLocalDateString(data.created_at),
        title: data.title,
        content: data.content,
        emotion: data.emotion,
        mood_score: data.mood_score,
        ai_reflection: data.ai_reflection,
        ai_summary: data.ai_summary,
        ai_emotion: data.ai_emotion,
        ai_encouragement: data.ai_encouragement,
        ai_reflection_question: data.ai_reflection_question,
        created_at: data.created_at,
      };

      toast.success('Journal entry saved!');
      return entry;
    } catch (error: any) {
      console.error('Error saving entry:', error);
      console.error('Entry data being saved:', {
        user_id: user?.id,
        title: params.title.trim(),
        content: params.content.trim(),
        emotion: params.emotion,
        mood_score: params.moodScore,
        ai_reflection: params.aiReflection?.reflection || null,
        ai_summary: params.aiReflection?.summary || null,
        ai_emotion: params.aiReflection?.emotion || null,
        ai_encouragement: params.aiReflection?.encouragement || null,
        ai_reflection_question: params.aiReflection?.reflection_question || null,
      });

      // Provide more specific error messages
      if (error.message?.includes('permission')) {
        toast.error('Permission denied. Please check your account access.');
      } else if (error.message?.includes('network')) {
        toast.error('Network error. Please check your connection and try again.');
      } else if (error.message?.includes('validation')) {
        toast.error('Invalid data. Please check your entry and try again.');
      } else {
        toast.error('Failed to save entry. Please try again.');
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Updates an existing journal entry
   */
  const updateJournalEntry = async (params: UpdateJournalEntryParams): Promise<FormattedJournalEntry | null> => {
    if (!user) {
      toast.error('You must be logged in to update entries');
      return null;
    }

    if (!validateJournalEntry(params)) {
      return null;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase
        .from('journal_entries')
        .update({
          title: params.title.trim(),
          content: params.content.trim(),
          emotion: params.emotion,
          mood_score: params.moodScore,
          ai_reflection: params.aiReflection?.reflection || null,
          ai_summary: params.aiReflection?.summary || null,
          ai_emotion: params.aiReflection?.emotion || null,
          ai_encouragement: params.aiReflection?.encouragement || null,
          ai_reflection_question: params.aiReflection?.reflection_question || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', params.id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;

      const entry: FormattedJournalEntry = {
        id: data.id,
        date: utcTimestampToLocalDateString(data.created_at),
        title: data.title,
        content: data.content,
        emotion: data.emotion,
        mood_score: data.mood_score,
        ai_reflection: data.ai_reflection,
        ai_summary: data.ai_summary,
        ai_emotion: data.ai_emotion,
        ai_encouragement: data.ai_encouragement,
        ai_reflection_question: data.ai_reflection_question,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };

      toast.success('Journal entry updated!');
      return entry;
    } catch (error: any) {
      console.error('Error updating entry:', error);

      if (error.message?.includes('permission')) {
        toast.error('Permission denied. Please check your account access.');
      } else if (error.message?.includes('network')) {
        toast.error('Network error. Please check your connection and try again.');
      } else {
        toast.error('Failed to update entry. Please try again.');
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Deletes a journal entry
   */
  const deleteJournalEntry = async (entryId: string): Promise<boolean> => {
    if (!user) {
      toast.error('You must be logged in to delete entries');
      return false;
    }

    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('journal_entries')
        .delete()
        .eq('id', entryId)
        .eq('user_id', user.id);

      if (error) throw error;

      toast.success('Journal entry deleted');
      return true;
    } catch (error: any) {
      console.error('Error deleting entry:', error);
      toast.error('Failed to delete entry. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Generates AI reflection for journal entry data
   */
  const generateReflection = async (data: JournalEntryData): Promise<AIReflectionType | null> => {
    if (!validateJournalEntry(data)) {
      return null;
    }

    try {
      const reflectionResult = await generateAIReflection({
        title: data.title.trim(),
        content: data.content.trim(),
        emotion: data.emotion,
        moodScore: data.moodScore,
      });

      if (reflectionResult.success && reflectionResult.data) {
        return {
          summary: reflectionResult.data.summary,
          emotion: reflectionResult.data.emotion,
          encouragement: reflectionResult.data.encouragement,
          reflection_question: reflectionResult.data.reflection_question,
          reflection: reflectionResult.data.reflection,
        };
      } else {
        toast.error('Failed to generate reflection. Please try again.');
        return null;
      }
    } catch (error) {
      console.error('Error generating reflection:', error);
      toast.error('Failed to generate reflection. Please try again.');
      return null;
    }
  };

  return {
    isLoading,
    validateJournalEntry,
    createJournalEntry,
    updateJournalEntry,
    deleteJournalEntry,
    generateReflection,
  };
};
