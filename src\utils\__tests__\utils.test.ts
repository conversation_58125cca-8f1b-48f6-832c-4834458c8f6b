/**
 * Utils Test Suite
 * Tests for utility functions
 */

import { describe, it, expect } from 'vitest';
import { cn } from '../utils';

describe('cn utility function', () => {
  it('should merge class names correctly', () => {
    const result = cn('base-class', 'additional-class');
    expect(result).toContain('base-class');
    expect(result).toContain('additional-class');
  });

  it('should handle conditional classes', () => {
    const shouldInclude = true;
    const shouldExclude = false;
    const result = cn('base-class', shouldInclude && 'conditional-class', shouldExclude && 'hidden-class');
    expect(result).toContain('base-class');
    expect(result).toContain('conditional-class');
    expect(result).not.toContain('hidden-class');
  });

  it('should handle undefined and null values', () => {
    const result = cn('base-class', undefined, null, 'valid-class');
    expect(result).toContain('base-class');
    expect(result).toContain('valid-class');
  });

  it('should merge Tailwind classes correctly', () => {
    const result = cn('p-4', 'p-2'); // Should resolve conflicts
    expect(result).toBeDefined();
    expect(typeof result).toBe('string');
  });
});
