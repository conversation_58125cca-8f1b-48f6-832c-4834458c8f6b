/**
 * UI Component Types
 * TypeScript interfaces for UI components and interactions
 */

import { ReactNode } from 'react';

/**
 * Common component props
 */
export interface BaseComponentProps {
  /** Additional CSS classes */
  className?: string;
  /** Child components */
  children?: ReactNode;
  /** Test ID for testing */
  testId?: string;
}

/**
 * Loading state interface
 */
export interface LoadingState {
  /** Whether the operation is loading */
  isLoading: boolean;
  /** Loading message to display */
  message?: string;
  /** Progress percentage (0-100) */
  progress?: number;
}

/**
 * Error state interface
 */
export interface ErrorState {
  /** Whether there is an error */
  hasError: boolean;
  /** Error message to display */
  message?: string;
  /** Error code for debugging */
  code?: string;
  /** Whether the error is recoverable */
  recoverable?: boolean;
}

/**
 * Navigation view types
 */
export type NavigationView = 'home' | 'write' | 'entries' | 'profile' | 'settings' | 'analytics';

/**
 * Navigation component props
 * Note: Navigation now uses React Router directly, so no props needed
 */
export interface NavigationProps extends BaseComponentProps {
  // No props needed - Navigation component uses React Router directly
}

/**
 * Button variant types
 */
export type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';

/**
 * Button size types
 */
export type ButtonSize = 'default' | 'sm' | 'lg' | 'icon';

/**
 * Form field validation state
 */
export interface FieldValidation {
  /** Whether the field is valid */
  isValid: boolean;
  /** Validation error message */
  error?: string;
  /** Whether the field has been touched */
  touched: boolean;
}

/**
 * Form state interface
 */
export interface FormState<T = Record<string, any>> {
  /** Form field values */
  values: T;
  /** Field validation states */
  validation: Record<keyof T, FieldValidation>;
  /** Whether the form is submitting */
  isSubmitting: boolean;
  /** Whether the form has been submitted */
  isSubmitted: boolean;
  /** Form-level error message */
  error?: string;
}

/**
 * Modal component props
 */
export interface ModalProps extends BaseComponentProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Close handler */
  onClose: () => void;
  /** Modal title */
  title?: string;
  /** Modal size */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  /** Whether clicking outside closes modal */
  closeOnOutsideClick?: boolean;
}

/**
 * Toast notification types
 */
export type ToastType = 'success' | 'error' | 'warning' | 'info';

/**
 * Toast notification interface
 */
export interface ToastNotification {
  /** Unique toast ID */
  id: string;
  /** Toast type */
  type: ToastType;
  /** Toast title */
  title?: string;
  /** Toast message */
  message: string;
  /** Auto-dismiss duration (ms) */
  duration?: number;
  /** Whether toast can be dismissed */
  dismissible?: boolean;
  /** Action button configuration */
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Pagination interface
 */
export interface PaginationState {
  /** Current page number (1-based) */
  currentPage: number;
  /** Total number of pages */
  totalPages: number;
  /** Number of items per page */
  pageSize: number;
  /** Total number of items */
  totalItems: number;
  /** Whether there is a next page */
  hasNext: boolean;
  /** Whether there is a previous page */
  hasPrevious: boolean;
}

/**
 * Search and filter interface
 */
export interface SearchFilters {
  /** Search query string */
  query?: string;
  /** Sort field */
  sortBy?: string;
  /** Sort direction */
  sortOrder?: 'asc' | 'desc';
  /** Additional filters */
  filters?: Record<string, any>;
}

/**
 * Theme configuration
 */
export interface ThemeConfig {
  /** Theme mode */
  mode: 'light' | 'dark' | 'system';
  /** Primary color scheme */
  primaryColor: string;
  /** Font family */
  fontFamily: string;
  /** Font size scale */
  fontSize: 'sm' | 'md' | 'lg';
  /** Animation preferences */
  animations: boolean;
}

/**
 * Responsive breakpoint types
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Component animation states
 */
export type AnimationState = 'idle' | 'entering' | 'entered' | 'exiting' | 'exited';
