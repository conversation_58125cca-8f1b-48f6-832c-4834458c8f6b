#!/usr/bin/env python3
"""
Simple AmberGlow Embedding Service with working CORS
"""

import logging
import sys
from flask import Flask, request, jsonify
from flask_cors import CORS
from sentence_transformers import SentenceTransformer
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Enable CORS - using the same configuration that worked in test-cors.py
CORS(app)

# Global model instance
model = None

def load_model():
    """Load the sentence transformer model."""
    global model
    try:
        logger.info("Loading sentence transformer model: all-MiniLM-L6-v2")
        model = SentenceTransformer('all-MiniLM-L6-v2')
        logger.info(f"Model loaded successfully. Embedding dimension: {model.get_sentence_embedding_dimension()}")
        return True
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        "status": "healthy",
        "service": "AmberGlow Embedding Service",
        "version": "1.0.0",
        "model": "all-MiniLM-L6-v2",
        "model_status": "loaded" if model else "not_loaded",
        "embedding_dimension": 384
    })

@app.route('/embed', methods=['POST'])
def generate_embedding():
    """Generate semantic embedding for text."""
    try:
        # Validate request
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400
        
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Missing 'text' field in request"}), 400
        
        text = data['text']
        if not isinstance(text, str) or not text.strip():
            return jsonify({"error": "Text must be a non-empty string"}), 400
        
        # Check if model is loaded
        if model is None:
            return jsonify({"error": "Model not loaded"}), 500
        
        # Generate embedding
        logger.info(f"Generating embedding for text: {text[:50]}...")
        embedding = model.encode(text, normalize_embeddings=True)
        
        # Convert to list for JSON serialization
        embedding_list = embedding.tolist()
        
        logger.info(f"Generated embedding with {len(embedding_list)} dimensions")
        
        return jsonify({
            "embedding": embedding_list,
            "dimension": len(embedding_list),
            "text_length": len(text)
        })
        
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    logger.info("Starting Simple AmberGlow Embedding Service...")
    
    # Load model
    if not load_model():
        logger.error("Failed to load model. Exiting.")
        sys.exit(1)
    
    logger.info("Starting Flask app on 0.0.0.0:5005")
    app.run(host='0.0.0.0', port=5005, debug=True)
