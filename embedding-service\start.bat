@echo off
REM AmberGlow Embedding Service Startup Script for Windows
REM This batch file helps Windows users start the embedding service easily

echo ========================================
echo AmberGlow Embedding Service Startup
echo ========================================

REM Check if we're in the right directory
if not exist "app.py" (
    echo Error: app.py not found. Make sure you're in the embedding-service directory.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found. Creating one...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment.
        echo Make sure Python 3.8+ is installed and in your PATH.
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo Error: Failed to activate virtual environment.
    pause
    exit /b 1
)

REM Check if requirements are installed
echo Checking dependencies...
python -c "import flask, flask_cors, sentence_transformers" 2>nul
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies.
        pause
        exit /b 1
    )
)

REM Start the service
echo.
echo Starting AmberGlow Embedding Service...
echo Service will be available at: http://localhost:5005
echo Press Ctrl+C to stop the service
echo.
python start.py

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo Service stopped with an error.
    pause
)
