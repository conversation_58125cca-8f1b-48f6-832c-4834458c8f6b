/**
 * <PERSON>ton Refactoring Tests
 * Tests to verify the refactored button styling with icons and proper layout
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { JournalEntryForm } from '@/components/forms/JournalEntryForm';
import { EditJournalEntryModal } from '@/components/features/EditJournalEntryModal';
import { JournalEntry } from '@/components/features/JournalEntry';
import { FormModal } from '@/components/ui/form-modal';
import { AmberButtonPair } from '@/components/ui/amber-button';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock the AI reflection service
vi.mock('@/services/aiReflectionService', () => ({
  generateAIReflection: vi.fn(),
}));

// Mock the auth context
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user', email: '<EMAIL>' },
  }),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Button Refactoring', () => {
  const mockFormData = {
    title: 'Test Entry',
    content: 'Test content',
    emotion: 'joyful' as const,
    moodScore: 7 as const,
  };

  const mockEntry = {
    id: 'test-id',
    title: 'Test Entry',
    content: 'Test content',
    emotion: 'joyful' as const,
    mood_score: 7 as const,
    date: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    user_id: 'test-user',
    ai_summary: null,
    ai_emotion: null,
    ai_encouragement: null,
    ai_reflection_question: null,
    ai_reflection: null,
  };

  describe('Save/Update button with floppy disk icon', () => {
    it('AmberButtonPair form variant includes Save icon', () => {
      render(
        <AmberButtonPair
          cancelText="Cancel"
          actionText="Save Entry"
          onCancel={vi.fn()}
          onAction={vi.fn()}
          variant="form"
        />
      );

      const saveButton = screen.getByRole('button', { name: /save entry/i });
      expect(saveButton).toBeInTheDocument();
      
      // Check for Save icon (lucide-react Save icon)
      const saveIcon = saveButton.querySelector('svg');
      expect(saveIcon).toBeInTheDocument();
    });

    it('JournalEntryForm Save button has floppy disk icon', () => {
      render(
        <JournalEntryForm
          data={mockFormData}
          onChange={vi.fn()}
          onSubmit={vi.fn()}
          onCancel={vi.fn()}
          submitText="Save Entry"
        />
      );

      const saveButton = screen.getByRole('button', { name: /save entry/i });
      expect(saveButton).toBeInTheDocument();
      
      // Check for Save icon
      const saveIcon = saveButton.querySelector('svg');
      expect(saveIcon).toBeInTheDocument();
    });

    it('FormModal Update button has floppy disk icon', () => {
      render(
        <TestWrapper>
          <FormModal
            isOpen={true}
            onClose={vi.fn()}
            onSubmit={vi.fn()}
            title="Test Modal"
            submitText="Update Entry"
            cancelText="Cancel"
          >
            <div>Test content</div>
          </FormModal>
        </TestWrapper>
      );

      const updateButton = screen.getByRole('button', { name: /update entry/i });
      expect(updateButton).toBeInTheDocument();
      
      // Check for Save icon
      const saveIcon = updateButton.querySelector('svg');
      expect(saveIcon).toBeInTheDocument();
    });
  });

  describe('Button layout and spacing', () => {
    it('Cancel and Save buttons are in single row with 50% width each', () => {
      render(
        <AmberButtonPair
          cancelText="Cancel"
          actionText="Save Entry"
          onCancel={vi.fn()}
          onAction={vi.fn()}
          variant="form"
        />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      const saveButton = screen.getByRole('button', { name: /save entry/i });

      // Both buttons should have flex-1 class for 50% width
      expect(cancelButton).toHaveClass('flex-1');
      expect(saveButton).toHaveClass('flex-1');

      // Check proper padding and border radius
      expect(cancelButton).toHaveClass('px-4', 'py-2', 'rounded-md');
      expect(saveButton).toHaveClass('px-4', 'py-2', 'rounded-md');
    });

    it('Buttons have proper styling and colors', () => {
      render(
        <AmberButtonPair
          cancelText="Cancel"
          actionText="Save Entry"
          onCancel={vi.fn()}
          onAction={vi.fn()}
          variant="form"
        />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      const saveButton = screen.getByRole('button', { name: /save entry/i });

      // Cancel button styling
      expect(cancelButton).toHaveClass('border-gray-300', 'bg-white', 'text-gray-700');
      
      // Save button styling (amber/orange background)
      expect(saveButton).toHaveClass('bg-amber-500', 'text-white');
    });
  });

  describe('AI Reflection buttons with orange background', () => {
    it('Generate Reflection button has orange background', () => {
      render(
        <TestWrapper>
          <JournalEntry />
        </TestWrapper>
      );

      // Look for the Generate Reflection button
      const generateButton = screen.getByRole('button', { name: /generate reflection/i });
      expect(generateButton).toBeInTheDocument();
      
      // Should have amber/orange background (primary variant)
      expect(generateButton).toHaveClass('bg-amber-500');
    });

    it('Regenerate Reflection button has orange background', () => {
      render(
        <TestWrapper>
          <EditJournalEntryModal
            isOpen={true}
            onClose={vi.fn()}
            onSave={vi.fn()}
            entry={mockEntry}
          />
        </TestWrapper>
      );

      // Look for the Regenerate Reflection button
      const regenerateButton = screen.getByRole('button', { name: /regenerate reflection/i });
      expect(regenerateButton).toBeInTheDocument();
      
      // Should have amber/orange background (primary variant)
      expect(regenerateButton).toHaveClass('bg-amber-500');
    });
  });

  describe('Loading states', () => {
    it('Save button shows loading state correctly', () => {
      render(
        <AmberButtonPair
          cancelText="Cancel"
          actionText="Save Entry"
          onCancel={vi.fn()}
          onAction={vi.fn()}
          variant="form"
          isLoading={true}
        />
      );

      const saveButton = screen.getByRole('button', { name: /saving/i });
      expect(saveButton).toBeInTheDocument();
      expect(saveButton).toBeDisabled();
      
      // Check for loading spinner
      const spinner = saveButton.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });
  });
});
