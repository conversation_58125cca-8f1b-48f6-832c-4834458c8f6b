/**
 * Entry Content Molecule
 * Component for displaying journal entry content with mood rating
 */

import { MoodRating } from '@/components/atoms/MoodRating';
import { cn } from '@/utils/utils';
import { MoodScore, BaseComponentProps } from '@/types';

interface EntryContentProps extends BaseComponentProps {
  /** Entry content text */
  content: string;
  /** Mood score */
  moodScore: MoodScore;
  /** Content size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Maximum lines to show before truncating */
  maxLines?: number;
  /** Whether content is expandable */
  expandable?: boolean;
}

const contentSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
};

const lineHeightClasses = {
  sm: 'leading-relaxed',
  md: 'leading-relaxed',
  lg: 'leading-loose',
};

export const EntryContent = ({
  content,
  moodScore,
  size = 'md',
  maxLines,
  expandable = false,
  className,
  testId,
}: EntryContentProps) => {
  const truncateStyle = maxLines
    ? {
        display: '-webkit-box',
        WebkitLineClamp: maxLines,
        WebkitBoxOrient: 'vertical' as const,
        overflow: 'hidden',
      }
    : {};

  return (
    <div className={cn('space-y-4', className)} data-testid={testId}>
      {/* Content Text */}
      <div
        className={cn(
          'text-muted-foreground font-lora',
          contentSizeClasses[size],
          lineHeightClasses[size]
        )}
        style={truncateStyle}
      >
        {content}
      </div>

      {/* Mood Rating */}
      <MoodRating score={moodScore} size={size === 'lg' ? 'md' : 'sm'} showScore={true} />

      {/* Expand Button for truncated content */}
      {expandable && maxLines && (
        <button
          className="text-amber-600 hover:text-amber-700 text-sm font-medium transition-colors"
          onClick={() => {
            // TODO: Implement expand functionality
            console.log('Expand content');
          }}
        >
          Read more
        </button>
      )}
    </div>
  );
};
