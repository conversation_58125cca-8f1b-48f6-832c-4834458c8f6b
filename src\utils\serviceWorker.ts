/**
 * Service Worker Registration Utility
 * Handles service worker registration and lifecycle management
 */

import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * Service worker registration result
 */
export interface ServiceWorkerRegistrationResult {
  success: boolean;
  registration?: ServiceWorkerRegistration;
  error?: Error;
  message: string;
}

/**
 * Register service worker for caching and offline support
 */
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistrationResult> => {
  // Only register in production and if supported
  if (!('serviceWorker' in navigator)) {
    return {
      success: false,
      message: 'Service Worker not supported in this browser',
    };
  }

  const env = getEnvironmentConfig();
  
  // Skip registration in development unless explicitly enabled
  if (env.isDevelopment && !env.features.serviceWorkerEnabled) {
    return {
      success: false,
      message: 'Service Worker disabled in development',
    };
  }

  try {
    console.log('[SW] Registering service worker...');
    
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    console.log('[SW] Service worker registered successfully:', registration);

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New service worker is available
            console.log('[SW] New service worker available');
            
            // Notify user about update
            if (window.confirm('A new version is available. Reload to update?')) {
              newWorker.postMessage({ type: 'SKIP_WAITING' });
              window.location.reload();
            }
          }
        });
      }
    });

    // Handle controller change
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('[SW] Service worker controller changed');
      window.location.reload();
    });

    return {
      success: true,
      registration,
      message: 'Service Worker registered successfully',
    };
  } catch (error) {
    console.error('[SW] Service worker registration failed:', error);
    
    return {
      success: false,
      error: error as Error,
      message: `Service Worker registration failed: ${(error as Error).message}`,
    };
  }
};

/**
 * Unregister service worker
 */
export const unregisterServiceWorker = async (): Promise<boolean> => {
  if (!('serviceWorker' in navigator)) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('[SW] Service worker unregistered:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('[SW] Service worker unregistration failed:', error);
    return false;
  }
};

/**
 * Clear all caches
 */
export const clearServiceWorkerCaches = async (): Promise<boolean> => {
  if (!('caches' in window)) {
    return false;
  }

  try {
    const cacheNames = await caches.keys();
    await Promise.all(
      cacheNames.map(cacheName => caches.delete(cacheName))
    );
    
    console.log('[SW] All caches cleared');
    return true;
  } catch (error) {
    console.error('[SW] Failed to clear caches:', error);
    return false;
  }
};

/**
 * Get cache storage usage
 */
export const getCacheStorageUsage = async (): Promise<{
  usage: number;
  quota: number;
  percentage: number;
} | null> => {
  if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
    return null;
  }

  try {
    const estimate = await navigator.storage.estimate();
    const usage = estimate.usage || 0;
    const quota = estimate.quota || 0;
    const percentage = quota > 0 ? (usage / quota) * 100 : 0;

    return {
      usage,
      quota,
      percentage,
    };
  } catch (error) {
    console.error('[SW] Failed to get storage estimate:', error);
    return null;
  }
};

/**
 * Check if app is running in standalone mode (PWA)
 */
export const isStandalone = (): boolean => {
  return (
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true
  );
};

/**
 * Check if service worker is active
 */
export const isServiceWorkerActive = (): boolean => {
  return !!(
    'serviceWorker' in navigator &&
    navigator.serviceWorker.controller
  );
};
