#!/usr/bin/env python3
"""
AmberGlow Semantic Embedding Service

A Flask microservice that provides semantic embeddings for the AmberGlow journal application.
Uses sentence-transformers with the all-MiniLM-L6-v2 model for generating semantic embeddings.

Author: AmberGlow Development Team
Version: 1.0.0
"""

import logging
import os
import sys
from typing import Dict, List, Any, Optional
import traceback

from flask import Flask, request, jsonify, make_response
from flask_cors import CORS
from sentence_transformers import SentenceTransformer
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('embedding-service.log')
    ]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Manual CORS implementation since Flask-CORS seems to have issues
@app.after_request
def after_request(response):
    """Add CORS headers to all responses"""
    logger.info("CORS DEBUG: after_request called")
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
    response.headers['Access-Control-Max-Age'] = '86400'
    logger.info(f"CORS DEBUG: Added headers: {dict(response.headers)}")
    return response

@app.before_request
def handle_preflight():
    """Handle preflight OPTIONS requests"""
    if request.method == "OPTIONS":
        response = make_response()
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response.headers['Access-Control-Max-Age'] = '86400'
        return response

# Global model instance - loaded once at startup
model: Optional[SentenceTransformer] = None

# Model configuration
MODEL_NAME = 'all-MiniLM-L6-v2'
EMBEDDING_DIMENSION = 384  # Dimension of all-MiniLM-L6-v2 embeddings

def load_model() -> SentenceTransformer:
    """
    Load the sentence transformer model.
    
    Returns:
        SentenceTransformer: The loaded model instance
        
    Raises:
        Exception: If model loading fails
    """
    try:
        logger.info(f"Loading sentence transformer model: {MODEL_NAME}")
        model = SentenceTransformer(MODEL_NAME)
        logger.info(f"Model loaded successfully. Embedding dimension: {EMBEDDING_DIMENSION}")
        return model
    except Exception as e:
        logger.error(f"Failed to load model {MODEL_NAME}: {str(e)}")
        logger.error(traceback.format_exc())
        raise

def validate_request_data(data: Dict[str, Any]) -> tuple[bool, Optional[str]]:
    """
    Validate the request data for the /embed endpoint.
    
    Args:
        data: The JSON request data
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not isinstance(data, dict):
        return False, "Request body must be a JSON object"
    
    if 'text' not in data:
        return False, "Missing required 'text' field in request body"
    
    if not isinstance(data['text'], str):
        return False, "'text' field must be a string"
    
    if not data['text'].strip():
        return False, "'text' field cannot be empty"
    
    if len(data['text']) > 10000:  # Reasonable limit
        return False, "'text' field is too long (max 10,000 characters)"
    
    return True, None

@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint to verify service is running.
    
    Returns:
        JSON response with service status
    """
    try:
        global model
        model_status = "loaded" if model is not None else "not_loaded"
        
        return jsonify({
            "status": "healthy",
            "service": "AmberGlow Embedding Service",
            "version": "1.0.0",
            "model": MODEL_NAME,
            "model_status": model_status,
            "embedding_dimension": EMBEDDING_DIMENSION
        }), 200
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "error": str(e)
        }), 500

@app.route('/embed', methods=['POST'])
def generate_embedding():
    """
    Generate semantic embedding for the provided text.
    
    Expected JSON payload:
    {
        "text": "The journal entry or memory text to embed"
    }
    
    Returns:
        JSON response with embedding array or error message
    """
    try:
        # Validate request content type
        if not request.is_json:
            logger.warning("Request received with non-JSON content type")
            return jsonify({
                "error": "Request must have Content-Type: application/json"
            }), 400
        
        # Get and validate request data
        data = request.get_json()
        is_valid, error_message = validate_request_data(data)
        
        if not is_valid:
            logger.warning(f"Invalid request data: {error_message}")
            return jsonify({"error": error_message}), 400
        
        text = data['text'].strip()
        logger.info(f"Generating embedding for text (length: {len(text)} chars)")
        
        # Check if model is loaded
        global model
        if model is None:
            logger.error("Model not loaded")
            return jsonify({
                "error": "Embedding model not loaded. Please check service logs."
            }), 500
        
        # Generate embedding
        try:
            # Use normalize_embeddings=True to ensure consistent vector magnitudes
            embedding = model.encode(
                text, 
                normalize_embeddings=True,
                show_progress_bar=False
            )
            
            # Convert numpy array to Python list for JSON serialization
            embedding_list = embedding.tolist()
            
            logger.info(f"Successfully generated embedding with dimension: {len(embedding_list)}")
            
            return jsonify({
                "embedding": embedding_list
            }), 200
            
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                "error": f"Failed to generate embedding: {str(e)}"
            }), 500
            
    except Exception as e:
        logger.error(f"Unexpected error in /embed endpoint: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({
            "error": "Internal server error"
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({
        "error": "Endpoint not found",
        "available_endpoints": ["/health", "/embed"]
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    """Handle 405 errors."""
    return jsonify({
        "error": "Method not allowed",
        "message": "Check the HTTP method and endpoint"
    }), 405

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({
        "error": "Internal server error"
    }), 500

def initialize_service():
    """
    Initialize the embedding service by loading the model.
    """
    global model
    try:
        logger.info("Initializing AmberGlow Embedding Service...")
        model = load_model()
        logger.info("Service initialization completed successfully")
        return True
    except Exception as e:
        logger.error(f"Service initialization failed: {str(e)}")
        return False

if __name__ == '__main__':
    # Initialize the service
    if not initialize_service():
        logger.error("Failed to initialize service. Exiting.")
        sys.exit(1)
    
    # Get configuration from environment variables
    host = os.getenv('FLASK_HOST', '0.0.0.0')  # Bind to all interfaces
    port = int(os.getenv('FLASK_PORT', 5005))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting AmberGlow Embedding Service on {host}:{port}")
    logger.info(f"Debug mode: {debug}")
    logger.info(f"Model: {MODEL_NAME}")
    logger.info(f"CORS enabled for React frontend")
    
    # Start the Flask application
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True  # Enable threading for better performance
    )
