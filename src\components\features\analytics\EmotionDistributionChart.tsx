/**
 * Emotion Distribution Chart Component
 * Pie and bar charts showing emotion distribution with Recharts
 */

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { 
  PieChart, 
  Pie, 
  Cell, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid,
  ResponsiveContainer 
} from 'recharts';
import { PieChart as PieChartIcon, BarChart3, Heart } from 'lucide-react';
import { EmotionDistributionData } from '@/types';
import { useIsMobile } from '@/hooks/use-mobile';

interface EmotionDistributionChartProps {
  data?: EmotionDistributionData[];
  detailed?: boolean;
  isLoading?: boolean;
}

/**
 * Emotion emoji mapping for better visualization
 */
const EMOTION_EMOJIS: Record<string, string> = {
  joyful: '😊',
  calm: '😌',
  neutral: '😐',
  sad: '😢',
  anxious: '😰',
  excited: '🤩',
  grateful: '🙏',
  frustrated: '😤',
  hopeful: '🌟',
  overwhelmed: '😵',
};

/**
 * Custom label for pie chart
 */
const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
  if (percent < 0.05) return null; // Don't show labels for slices < 5%
  
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

/**
 * Custom tooltip for emotion charts
 */
const CustomTooltip = ({ active, payload }: any) => {
  if (!active || !payload?.length) return null;

  const data = payload[0].payload;
  const emoji = EMOTION_EMOJIS[data.emotion] || '😐';

  return (
    <div className="bg-white p-3 border border-amber-200 rounded-lg shadow-lg">
      <div className="flex items-center gap-2 mb-2">
        <span className="text-lg">{emoji}</span>
        <span className="font-medium text-gray-900 capitalize">{data.emotion}</span>
      </div>
      <div className="space-y-1 text-sm">
        <div>Count: <span className="font-medium">{data.count}</span></div>
        <div>Percentage: <span className="font-medium">{data.percentage.toFixed(1)}%</span></div>
        <div>Avg Mood: <span className="font-medium">{data.averageMood.toFixed(1)}/10</span></div>
      </div>
    </div>
  );
};

export const EmotionDistributionChart = ({
  data,
  detailed = false,
  isLoading = false
}: EmotionDistributionChartProps) => {
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-amber-600" />
            Emotion Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <div className="animate-pulse text-gray-500">Loading emotion data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-amber-600" />
            Emotion Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <Heart className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No emotion data available</p>
            <p className="text-sm mt-2">Start journaling to track your emotions!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate total entries and most common emotion
  const totalEntries = data.reduce((sum, item) => sum + item.count, 0);
  const mostCommonEmotion = data[0]; // Data is already sorted by count

  const chartConfig = data.reduce((config, item) => {
    config[item.emotion] = {
      label: item.emotion,
      color: item.color,
    };
    return config;
  }, {} as any);

  return (
    <Card className={detailed ? 'col-span-full' : ''}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Heart className="w-5 h-5 text-amber-600" />
            Emotion Distribution
          </div>
          <div className="flex gap-2">
            <Button
              variant={chartType === 'pie' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('pie')}
              className="h-8"
            >
              <PieChartIcon className="w-4 h-4" />
            </Button>
            <Button
              variant={chartType === 'bar' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setChartType('bar')}
              className="h-8"
            >
              <BarChart3 className="w-4 h-4" />
            </Button>
          </div>
        </CardTitle>
        {mostCommonEmotion && (
          <div className="text-sm text-gray-600">
            Most common: <span className="font-medium capitalize">
              {EMOTION_EMOJIS[mostCommonEmotion.emotion]} {mostCommonEmotion.emotion}
            </span> ({mostCommonEmotion.percentage.toFixed(1)}%)
          </div>
        )}
      </CardHeader>
      <CardContent>
        {chartType === 'pie' ? (
          <ChartContainer
            config={chartConfig}
            className={`
              ${detailed ? "h-[500px]" : isMobile ? "h-[250px]" : "h-[300px]"}
              w-full
              [&_.recharts-responsive-container]:!aspect-auto
            `}
          >
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={isMobile ? undefined : renderCustomLabel}
                outerRadius={detailed ? 180 : isMobile ? 80 : 120}
                fill="#8884d8"
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip content={<CustomTooltip />} />
            </PieChart>
          </ChartContainer>
        ) : (
          <ChartContainer
            config={chartConfig}
            className={`
              ${detailed ? "h-[500px]" : isMobile ? "h-[250px]" : "h-[300px]"}
              w-full
              [&_.recharts-responsive-container]:!aspect-auto
            `}
          >
            <BarChart
              data={data}
              margin={{
                top: 5,
                right: isMobile ? 10 : 30,
                left: isMobile ? 10 : 20,
                bottom: 5
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis
                dataKey="emotion"
                stroke="#6b7280"
                fontSize={isMobile ? 10 : 12}
                tickFormatter={(value) => EMOTION_EMOJIS[value] || value}
                interval={isMobile ? 'preserveStartEnd' : 0}
              />
              <YAxis
                stroke="#6b7280"
                fontSize={isMobile ? 10 : 12}
                label={isMobile ? undefined : { value: 'Count', angle: -90, position: 'insideLeft' }}
              />
              <ChartTooltip content={<CustomTooltip />} />
              <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ChartContainer>
        )}

        {detailed && (
          <div className="mt-6">
            <h4 className="font-medium text-gray-900 mb-3">Emotion Details</h4>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
              {data.map((emotion) => (
                <div 
                  key={emotion.emotion}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: emotion.color }}
                    />
                    <span className="text-lg">{EMOTION_EMOJIS[emotion.emotion]}</span>
                    <span className="font-medium capitalize">{emotion.emotion}</span>
                  </div>
                  <div className="text-right text-sm">
                    <div className="font-medium">{emotion.count} entries</div>
                    <div className="text-gray-600">
                      {emotion.percentage.toFixed(1)}% • Avg: {emotion.averageMood.toFixed(1)}/10
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
