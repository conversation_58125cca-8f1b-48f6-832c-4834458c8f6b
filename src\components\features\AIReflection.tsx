import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, <PERSON>, Heart, MessageCircle, Lightbulb } from 'lucide-react';
import { AIReflection as AIReflectionType, JournalEntry } from '@/types';
import { ConversationThread } from './ConversationThread';
import { cn } from '@/utils/utils';

interface AIReflectionProps extends Partial<AIReflectionType> {
  isGenerating?: boolean;
  className?: string;
  /** Journal entry for conversation context */
  journalEntry?: JournalEntry;
  /** Whether to show conversation interface */
  showConversation?: boolean;
}

export const AIReflection = ({
  summary,
  emotion,
  encouragement,
  reflection_question,
  reflection,
  isGenerating = false,
  className = '',
  journalEntry,
  showConversation = false,
}: AIReflectionProps) => {
  // Get the text to display (no animation needed)
  const displayedEncouragement = encouragement || reflection || '';

  if (isGenerating) {
    return (
      <Card
        className={`glass-effect border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 ${className}`}
      >
        <CardContent className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
              <Brain className="w-4 h-4 text-amber-600 animate-pulse" />
            </div>
            <h3 className="text-lg font-semibold text-amber-800">Amber's Reflection</h3>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2 text-amber-600">
              <Sparkles className="w-4 h-4 animate-spin" />
              <span className="text-sm">Crafting a thoughtful response just for you...</span>
            </div>

            {/* Animated loading bars */}
            <div className="space-y-2">
              <div className="h-3 bg-amber-200 rounded-full animate-pulse"></div>
              <div className="h-3 bg-amber-200 rounded-full animate-pulse w-4/5"></div>
              <div className="h-3 bg-amber-200 rounded-full animate-pulse w-3/5"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Check if we have new format data or legacy data
  const hasNewFormat = summary || emotion || encouragement || reflection_question;
  const hasLegacyFormat = reflection && !hasNewFormat;

  if (!hasNewFormat && !hasLegacyFormat) {
    return null;
  }

  return (
    <Card
      className={cn(
        'card-elevated border-amber-200/40 bg-gradient-to-br from-amber-50 to-orange-50',
        'hover-glow transition-all duration-300 ease-out scale-in',
        className
      )}
    >
      <CardContent className="section-padding pt-8">
        <div className="flex items-center gap-4 mb-8">
          <div className="w-10 h-10 bg-amber-100 rounded-xl flex items-center justify-center shadow-sm">
            <Brain className="w-5 h-5 text-amber-600" />
          </div>
          <h3 className="text-xl heading-modern text-amber-800">Amber's Reflection</h3>
          <Sparkles className="w-5 h-5 text-amber-500 ml-auto animate-pulse" />
        </div>

        {hasNewFormat ? (
          <div className="content-spacing">
            {/* Summary Section */}
            {summary && (
              <div className="flex gap-4 p-4 rounded-xl bg-blue-50/50 border border-blue-200/30">
                <div className="w-8 h-8 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm">
                  <MessageCircle className="w-4 h-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-800 mb-2 heading-modern">Summary</h4>
                  <p className="text-blue-700 font-lora text-sm text-elegant">{summary}</p>
                </div>
              </div>
            )}

            {/* Detected Emotion */}
            {emotion && (
              <div className="flex gap-4 p-4 rounded-xl bg-pink-50/50 border border-pink-200/30">
                <div className="w-8 h-8 bg-pink-100 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Heart className="w-4 h-4 text-pink-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-pink-800 mb-3 heading-modern">I sense you're feeling</h4>
                  <span className="inline-block px-4 py-2 bg-pink-100 text-pink-800 rounded-xl text-sm font-medium capitalize shadow-sm">
                    {emotion}
                  </span>
                </div>
              </div>
            )}

            {/* Encouragement Section */}
            {(encouragement || reflection) && (
              <div className="flex gap-4 p-4 rounded-xl bg-green-50/50 border border-green-200/30">
                <div className="w-8 h-8 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Heart className="w-4 h-4 text-green-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-green-800 mb-3 heading-modern">From your friend</h4>
                  <div className="relative">
                    <p className="text-green-700 font-lora text-elegant">
                      {displayedEncouragement}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Reflection Question */}
            {reflection_question && (
              <div className="flex gap-4 p-4 rounded-xl bg-amber-50/50 border border-amber-200/30">
                <div className="w-8 h-8 bg-amber-100 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm">
                  <Lightbulb className="w-4 h-4 text-amber-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-amber-800 mb-3 heading-modern">Something to ponder</h4>
                  <p className="text-amber-700 font-lora text-sm text-elegant italic">
                    {reflection_question}
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          // Legacy format display
          <div className="relative">
            <p className="text-amber-700 font-lora leading-relaxed text-base">
              {displayedEncouragement}
            </p>

            {/* Subtle decorative element */}
            <div className="absolute -top-2 -left-2 w-6 h-6 bg-amber-100 rounded-full opacity-20"></div>
            <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-orange-100 rounded-full opacity-30"></div>
          </div>
        )}

        <div className="mt-6 pt-4 border-t border-amber-200">
          <p className="text-xs text-amber-500 italic">A thoughtful response just for you</p>
        </div>
      </CardContent>

      {/* Conversation Interface */}
      {showConversation && journalEntry && (
        <div className="border-t border-amber-200/30">
          <ConversationThread
            journalEntry={journalEntry}
            autoInitialize={true}
            className="border-0 bg-transparent"
          />
        </div>
      )}
    </Card>
  );
};
