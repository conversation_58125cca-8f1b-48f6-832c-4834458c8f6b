import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, <PERSON>, Heart, MessageCircle, Lightbulb } from 'lucide-react';
import { AIReflection as AIReflectionType, JournalEntry } from '@/types';
import { ConversationThread } from './ConversationThread';

interface AIReflectionProps extends Partial<AIReflectionType> {
  isGenerating?: boolean;
  className?: string;
  /** Journal entry for conversation context */
  journalEntry?: JournalEntry;
  /** Whether to show conversation interface */
  showConversation?: boolean;
}

export const AIReflection = ({
  summary,
  emotion,
  encouragement,
  reflection_question,
  reflection,
  isGenerating = false,
  className = '',
  journalEntry,
  showConversation = false,
}: AIReflectionProps) => {
  // Get the text to display (no animation needed)
  const displayedEncouragement = encouragement || reflection || '';

  if (isGenerating) {
    return (
      <Card
        className={`glass-effect border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 ${className}`}
      >
        <CardContent className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
              <Brain className="w-4 h-4 text-amber-600 animate-pulse" />
            </div>
            <h3 className="text-lg font-semibold text-amber-800">Amber's Reflection</h3>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2 text-amber-600">
              <Sparkles className="w-4 h-4 animate-spin" />
              <span className="text-sm">Crafting a thoughtful response just for you...</span>
            </div>

            {/* Animated loading bars */}
            <div className="space-y-2">
              <div className="h-3 bg-amber-200 rounded-full animate-pulse"></div>
              <div className="h-3 bg-amber-200 rounded-full animate-pulse w-4/5"></div>
              <div className="h-3 bg-amber-200 rounded-full animate-pulse w-3/5"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Check if we have new format data or legacy data
  const hasNewFormat = summary || emotion || encouragement || reflection_question;
  const hasLegacyFormat = reflection && !hasNewFormat;

  if (!hasNewFormat && !hasLegacyFormat) {
    return null;
  }

  return (
    <Card
      className={`glass-effect border-amber-200 bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-lg transition-all duration-300 ${className}`}
    >
      <CardContent className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
            <Brain className="w-4 h-4 text-amber-600" />
          </div>
          <h3 className="text-lg font-semibold text-amber-800">Amber's Reflection</h3>
          <Sparkles className="w-4 h-4 text-amber-500 ml-auto" />
        </div>

        {hasNewFormat ? (
          <div className="space-y-6">
            {/* Summary Section */}
            {summary && (
              <div className="flex gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <MessageCircle className="w-3 h-3 text-blue-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-blue-800 mb-1">Summary</h4>
                  <p className="text-blue-700 font-lora text-sm leading-relaxed">{summary}</p>
                </div>
              </div>
            )}

            {/* Detected Emotion */}
            {emotion && (
              <div className="flex gap-3">
                <div className="w-6 h-6 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Heart className="w-3 h-3 text-pink-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-pink-800 mb-1">I sense you're feeling</h4>
                  <span className="inline-block px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm font-medium capitalize">
                    {emotion}
                  </span>
                </div>
              </div>
            )}

            {/* Encouragement Section with Typewriter Effect */}
            {(encouragement || reflection) && (
              <div className="flex gap-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Heart className="w-3 h-3 text-green-600" />
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-green-800 mb-2">From your friend</h4>
                  <div className="relative">
                    <p className="text-green-700 font-lora leading-relaxed">
                      {displayedEncouragement}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Reflection Question */}
            {reflection_question && (
              <div className="flex gap-3">
                <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Lightbulb className="w-3 h-3 text-amber-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-amber-800 mb-1">Something to ponder</h4>
                  <p className="text-amber-700 font-lora text-sm leading-relaxed italic">
                    {reflection_question}
                  </p>
                </div>
              </div>
            )}
          </div>
        ) : (
          // Legacy format display
          <div className="relative">
            <p className="text-amber-700 font-lora leading-relaxed text-base">
              {displayedEncouragement}
            </p>

            {/* Subtle decorative element */}
            <div className="absolute -top-2 -left-2 w-6 h-6 bg-amber-100 rounded-full opacity-20"></div>
            <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-orange-100 rounded-full opacity-30"></div>
          </div>
        )}

        <div className="mt-6 pt-4 border-t border-amber-200">
          <p className="text-xs text-amber-500 italic">A thoughtful response just for you</p>
        </div>
      </CardContent>

      {/* Conversation Interface */}
      {showConversation && journalEntry && (
        <div className="border-t border-amber-200/30">
          <ConversationThread
            journalEntry={journalEntry}
            autoInitialize={true}
            className="border-0 bg-transparent"
          />
        </div>
      )}
    </Card>
  );
};
