/**
 * AI Response Validation Utilities
 * Comprehensive validation for AI responses with fallback mechanisms
 */

import { GeminiAIResponse, AIReflectionInput, AIError } from '@/types';

/**
 * Validation rules for AI response fields
 */
interface ValidationRule {
  field: keyof GeminiAIResponse;
  minLength: number;
  maxLength: number;
  required: boolean;
  pattern?: RegExp;
  customValidator?: (value: string) => boolean;
}

/**
 * Validation configuration for AI responses
 */
const VALIDATION_RULES: ValidationRule[] = [
  {
    field: 'summary',
    minLength: 10,
    maxLength: 500,
    required: true,
    customValidator: (value: string) => {
      // Summary should be meaningful and not just repeated words
      const words = value.toLowerCase().split(/\s+/);
      const uniqueWords = new Set(words);
      return uniqueWords.size >= Math.min(words.length * 0.7, 5); // At least 70% unique words or 5 unique words
    },
  },
  {
    field: 'emotion',
    minLength: 2,
    maxLength: 50,
    required: true,
    pattern: /^[a-zA-Z\s-]+$/, // Only letters, spaces, and hyphens
    customValidator: (value: string) => {
      // Emotion should be a single word or short phrase
      const words = value.trim().split(/\s+/);
      return words.length <= 3;
    },
  },
  {
    field: 'encouragement',
    minLength: 20,
    maxLength: 1000,
    required: true,
    customValidator: (value: string) => {
      // Encouragement should be supportive and not contain negative words
      const negativeWords = ['terrible', 'awful', 'horrible', 'stupid', 'worthless', 'failure'];
      const lowerValue = value.toLowerCase();
      return !negativeWords.some(word => lowerValue.includes(word));
    },
  },
  {
    field: 'reflection_question',
    minLength: 10,
    maxLength: 300,
    required: true,
    pattern: /\?$/, // Should end with a question mark
    customValidator: (value: string) => {
      // Should be a proper question
      const questionWords = [
        'what',
        'how',
        'why',
        'when',
        'where',
        'which',
        'who',
        'can',
        'could',
        'would',
        'should',
      ];
      const lowerValue = value.toLowerCase();
      return questionWords.some(word => lowerValue.includes(word));
    },
  },
];

/**
 * Validation result interface
 */
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  sanitizedResponse?: GeminiAIResponse;
}

/**
 * Validates a single field according to its rules
 */
const validateField = (
  value: string,
  rule: ValidationRule
): { isValid: boolean; errors: string[]; warnings: string[] } => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if field is required
  if (rule.required && (!value || value.trim().length === 0)) {
    errors.push(`${rule.field} is required`);
    return { isValid: false, errors, warnings };
  }

  // Skip further validation if field is empty and not required
  if (!value || value.trim().length === 0) {
    return { isValid: true, errors, warnings };
  }

  const trimmedValue = value.trim();

  // Check length constraints
  if (trimmedValue.length < rule.minLength) {
    errors.push(`${rule.field} is too short (minimum ${rule.minLength} characters)`);
  }

  if (trimmedValue.length > rule.maxLength) {
    warnings.push(
      `${rule.field} is too long (maximum ${rule.maxLength} characters), will be truncated`
    );
  }

  // Check pattern if specified
  if (rule.pattern && !rule.pattern.test(trimmedValue)) {
    errors.push(`${rule.field} does not match required pattern`);
  }

  // Run custom validator if specified
  if (rule.customValidator && !rule.customValidator(trimmedValue)) {
    errors.push(`${rule.field} failed custom validation`);
  }

  return { isValid: errors.length === 0, errors, warnings };
};

/**
 * Sanitizes AI response by cleaning and truncating fields
 */
const sanitizeResponse = (response: any): GeminiAIResponse => {
  const sanitized: GeminiAIResponse = {
    summary: '',
    emotion: '',
    encouragement: '',
    reflection_question: '',
  };

  // Sanitize each field
  for (const rule of VALIDATION_RULES) {
    const value = response[rule.field];
    if (typeof value === 'string') {
      let sanitizedValue = value.trim();

      // Remove excessive whitespace
      sanitizedValue = sanitizedValue.replace(/\s+/g, ' ');

      // Remove potentially harmful content
      sanitizedValue = sanitizedValue.replace(/<[^>]*>/g, ''); // Remove HTML tags
      sanitizedValue = sanitizedValue.replace(/[^\w\s.,!?;:'"()-]/g, ''); // Remove special characters except common punctuation

      // Truncate if too long
      if (sanitizedValue.length > rule.maxLength) {
        sanitizedValue = sanitizedValue.substring(0, rule.maxLength - 3) + '...';
      }

      sanitized[rule.field] = sanitizedValue;
    }
  }

  // Ensure reflection_question ends with a question mark
  if (sanitized.reflection_question && !sanitized.reflection_question.endsWith('?')) {
    sanitized.reflection_question += '?';
  }

  return sanitized;
};

/**
 * Validates AI response structure and content
 */
export const validateAIResponse = (response: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if response is an object
  if (!response || typeof response !== 'object') {
    return {
      isValid: false,
      errors: ['Response is not a valid object'],
      warnings: [],
    };
  }

  // Validate each field
  for (const rule of VALIDATION_RULES) {
    const value = response[rule.field];
    const fieldValidation = validateField(value, rule);

    errors.push(...fieldValidation.errors);
    warnings.push(...fieldValidation.warnings);
  }

  // If validation failed, return early
  if (errors.length > 0) {
    return {
      isValid: false,
      errors,
      warnings,
    };
  }

  // Sanitize the response
  const sanitizedResponse = sanitizeResponse(response);

  return {
    isValid: true,
    errors: [],
    warnings,
    sanitizedResponse,
  };
};

/**
 * Creates a fallback response when validation fails
 */
export const createFallbackResponse = (
  input: AIReflectionInput,
  validationErrors: string[]
): GeminiAIResponse => {
  console.warn('Creating fallback response due to validation errors:', validationErrors);

  // Simple emotion mapping
  const emotionMap: Record<string, string> = {
    joyful: 'joy',
    happy: 'happiness',
    sad: 'sadness',
    angry: 'anger',
    anxious: 'anxiety',
    calm: 'peace',
    excited: 'excitement',
    grateful: 'gratitude',
    frustrated: 'frustration',
    hopeful: 'hope',
    overwhelmed: 'overwhelm',
  };

  const detectedEmotion = emotionMap[input.emotion.toLowerCase()] || 'reflection';

  return {
    summary: `You've shared some thoughtful reflections about ${input.emotion} feelings in your journal entry.`,
    emotion: detectedEmotion,
    encouragement: `Thank you for taking the time to write about your experiences. Journaling is a wonderful way to process your thoughts and feelings, and I appreciate you sharing this moment with me.`,
    reflection_question: `What's one thing from this experience that you'd like to remember or explore further?`,
  };
};

/**
 * Validates and processes AI response with comprehensive error handling
 */
export const processAIResponse = (
  response: any,
  input: AIReflectionInput
): { isValid: boolean; data: GeminiAIResponse; errors: string[]; warnings: string[] } => {
  const validation = validateAIResponse(response);

  if (validation.isValid && validation.sanitizedResponse) {
    return {
      isValid: true,
      data: validation.sanitizedResponse,
      errors: [],
      warnings: validation.warnings,
    };
  }

  // Create fallback response
  const fallbackResponse = createFallbackResponse(input, validation.errors);

  return {
    isValid: false,
    data: fallbackResponse,
    errors: validation.errors,
    warnings: validation.warnings,
  };
};

/**
 * Content quality scoring for AI responses
 */
export const scoreResponseQuality = (response: GeminiAIResponse): number => {
  let score = 0;
  const maxScore = 100;

  // Summary quality (25 points)
  if (response.summary.length >= 20 && response.summary.length <= 200) score += 15;
  if (response.summary.includes(' ') && response.summary.split(' ').length >= 5) score += 10;

  // Emotion appropriateness (15 points)
  if (response.emotion.length >= 3 && response.emotion.length <= 20) score += 10;
  if (!/\d/.test(response.emotion)) score += 5; // No numbers in emotion

  // Encouragement quality (35 points)
  if (response.encouragement.length >= 50 && response.encouragement.length <= 500) score += 20;
  if (response.encouragement.includes('you') || response.encouragement.includes('your'))
    score += 10; // Personal touch
  if (!/negative|bad|wrong|terrible/.test(response.encouragement.toLowerCase())) score += 5; // Positive tone

  // Question quality (25 points)
  if (response.reflection_question.endsWith('?')) score += 10;
  if (response.reflection_question.length >= 15 && response.reflection_question.length <= 150)
    score += 10;
  if (/what|how|why|when|where/.test(response.reflection_question.toLowerCase())) score += 5; // Proper question words

  return Math.min(score, maxScore);
};
