/**
 * Mood Rating Atom
 * Reusable component for displaying mood ratings with stars
 */

import { Star } from 'lucide-react';
import { cn } from '@/utils/utils';
import { MoodScore, BaseComponentProps } from '@/types';

interface MoodRatingProps extends BaseComponentProps {
  /** Mood score (1-10) */
  score: MoodScore;
  /** Maximum number of stars to display */
  maxStars?: number;
  /** Size of the stars */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show the numeric score */
  showScore?: boolean;
  /** Whether this is interactive (for input) */
  interactive?: boolean;
  /** Callback for interactive mode */
  onScoreChange?: (score: MoodScore) => void;
}

const sizeClasses = {
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-5 h-5',
};

export const MoodRating = ({
  score,
  maxStars = 5,
  size = 'md',
  showScore = true,
  interactive = false,
  onScoreChange,
  className,
  testId,
}: MoodRatingProps) => {
  // Convert 1-10 score to star rating (1-5)
  const starRating = Math.floor(score / 2);

  const handleStarClick = (starIndex: number) => {
    if (interactive && onScoreChange) {
      // Convert star index back to 1-10 scale
      const newScore = (starIndex + 1) * 2;
      onScoreChange(newScore as MoodScore);
    }
  };

  return (
    <div className={cn('flex items-center gap-1', className)} data-testid={testId}>
      <div className="flex items-center gap-1">
        {[...Array(maxStars)].map((_, i) => (
          <Star
            key={i}
            className={cn(
              sizeClasses[size],
              i < starRating ? 'fill-amber-400 text-amber-400' : 'text-gray-300',
              interactive && 'cursor-pointer hover:text-amber-300 transition-colors'
            )}
            onClick={() => handleStarClick(i)}
          />
        ))}
      </div>
      {showScore && <span className="text-sm text-muted-foreground ml-2">Mood: {score}/10</span>}
    </div>
  );
};
