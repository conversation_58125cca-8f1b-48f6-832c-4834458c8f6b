/**
 * Memory Context Hook
 * Hook for managing user memories and memory extraction
 * Enhanced with relevance-based memory retrieval
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { UserMemory, MemoryExtractionResponse } from '@/types';
import {
  extractMemoriesFromJournalEntry,
  extractMemoriesFromConversation,
} from '@/services/memoryExtractionService';
import {
  getUserMemories,
  addUserMemory,
  updateUserMemory,
  removeUserMemory,
  clearAllUserMemories,
  deduplicateUserMemories,
  getMemoryExtractionStats,
  MemoryRetrievalOptions,
} from '@/services/memoryPersistenceService';
import { getRelevantMemories, getContextRelevantMemories, RelevantMemory, RelevanceRetrievalOptions } from '@/services/memoryRelevanceService';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Memory context state
 */
interface MemoryContextState {
  /** Current user memories */
  memories: UserMemory[];
  /** Whether memory extraction is in progress */
  isExtracting: boolean;
  /** Last extraction error */
  extractionError: string | null;
  /** Memory extraction statistics */
  stats: {
    totalExtractions: number;
    successfulExtractions: number;
    totalMemoriesExtracted: number;
    averageProcessingTime: number;
  };
}

/**
 * Memory context actions
 */
interface MemoryContextActions {
  /** Extract memories from a journal entry */
  extractFromJournalEntry: (journalEntry: {
    title: string;
    content: string;
    emotion: string;
    moodScore: number;
  }) => Promise<MemoryExtractionResponse>;
  
  /** Extract memories from a conversation */
  extractFromConversation: (conversation: {
    messages: Array<{
      sender_type: 'user' | 'ai';
      message_content: string;
    }>;
    journalContext?: {
      title: string;
      content: string;
      emotion: string;
      moodScore: number;
    };
  }) => Promise<MemoryExtractionResponse>;
  
  /** Add a memory manually */
  addMemory: (memory: UserMemory) => Promise<void>;

  /** Remove a memory */
  removeMemory: (key: string) => Promise<void>;

  /** Update an existing memory */
  updateMemory: (key: string, updates: Partial<UserMemory>) => Promise<void>;

  /** Get memories by category */
  getMemoriesByCategory: (category: UserMemory['category']) => UserMemory[];

  /** Search memories by key or value */
  searchMemories: (query: string) => UserMemory[];

  /** Clear all memories */
  clearMemories: () => Promise<void>;

  /** Remove duplicate memories */
  deduplicateMemories: () => Promise<{ removedCount: number; mergedCount: number } | null>;

  /** Get all memories including low importance ones (for backward compatibility) */
  getAllMemories: () => Promise<void>;

  /** Get top important memories (default behavior) */
  getImportantMemories: (options?: MemoryRetrievalOptions) => Promise<void>;

  /** Get memories based on relevance to a query */
  getRelevantMemoriesForQuery: (query: string, options?: RelevanceRetrievalOptions) => Promise<RelevantMemory[]>;
  
  /** Get memories relevant to a specific context (journal entry, conversation) */
  getRelevantMemoriesForContext: (context: string, options?: Omit<RelevanceRetrievalOptions, 'minRelevance'>) => Promise<RelevantMemory[]>;

  /** Get formatted memory context for AI prompts */
  getMemoryContext: () => string;
}

/**
 * Memory context hook return type
 */
type UseMemoryContextReturn = MemoryContextState & MemoryContextActions & {
  /** Relevant memories based on the last query */
  relevantMemories: RelevantMemory[];
};

/**
 * Hook for managing user memories and memory extraction
 */
export const useMemoryContext = (): UseMemoryContextReturn => {
  const { user } = useAuth();
  const [state, setState] = useState<MemoryContextState>({
    memories: [],
    isExtracting: false,
    extractionError: null,
    stats: {
      totalExtractions: 0,
      successfulExtractions: 0,
      totalMemoriesExtracted: 0,
      averageProcessingTime: 0,
    },
  });
  
  // Store relevant memories separately
  const [relevantMemories, setRelevantMemories] = useState<RelevantMemory[]>([]);

  // Track processing times for statistics
  const processingTimes = useRef<number[]>([]);
  
  // Calculate average processing time
  const calculateAverageProcessingTime = useCallback((): number => {
    if (processingTimes.current.length === 0) return 0;
    const sum = processingTimes.current.reduce((acc, time) => acc + time, 0);
    return sum / processingTimes.current.length;
  }, []);

  /**
   * Load memories from Supabase when user is authenticated
   */
  useEffect(() => {
    const loadMemories = async () => {
      if (!user) return;

      try {
        // Load all memories without limit
        const memoriesResult = await getUserMemories({ orderByImportance: true, includeAllMemories: true });

        if (memoriesResult.success && memoriesResult.data) {
          setState(prev => ({
            ...prev,
            memories: memoriesResult.data,
          }));
        }
        
        // Then try to load stats
        try {
          const statsResult = await getMemoryExtractionStats();
          if (statsResult.success && statsResult.data) {
            setState(prev => ({
              ...prev,
              stats: statsResult.data || {
                totalExtractions: 0,
                successfulExtractions: 0,
                totalMemoriesExtracted: 0,
                averageProcessingTime: 0,
              },
            }));
          }
        } catch (statsError) {
          console.warn('Failed to load memory extraction stats:', statsError);
          // Continue without stats - non-critical error
        }
      } catch (error) {
        console.error('Error loading memories from Supabase:', error);
      }
    };

    loadMemories();
  }, [user]);

  /**
   * Update statistics after extraction
   */
  const updateStats = useCallback((
    success: boolean,
    memoriesCount: number,
    processingTime?: number
  ) => {
    setState(prev => {
      const newStats = { ...prev.stats };
      newStats.totalExtractions += 1;

      if (success) {
        newStats.successfulExtractions += 1;
        newStats.totalMemoriesExtracted += memoriesCount;
      }

      if (processingTime) {
        processingTimes.current.push(processingTime);
        // Keep only last 100 processing times for average calculation
        if (processingTimes.current.length > 100) {
          processingTimes.current = processingTimes.current.slice(-100);
        }
        newStats.averageProcessingTime =
          processingTimes.current.reduce((sum, time) => sum + time, 0) / processingTimes.current.length;
      }

      return {
        ...prev,
        stats: newStats,
      };
    });
  }, []);

  /**
   * Merge new memories with existing ones, avoiding duplicates
   */
  const mergeMemories = useCallback((newMemories: UserMemory[]) => {
    setState(prev => {
      const existingKeys = new Set(prev.memories.map(m => m.key));
      const uniqueNewMemories = newMemories.filter(m => !existingKeys.has(m.key));
      
      if (uniqueNewMemories.length === 0) {
        console.log(' No new unique memories to add');
        return prev;
      }

      console.log(' Adding new memories:', uniqueNewMemories.map(m => m.key));
      
      return {
        ...prev,
        memories: [...prev.memories, ...uniqueNewMemories],
      };
    });

    return newMemories.length;
  }, []);

  /**
   * Extract memories from a journal entry
   */
  const extractFromJournalEntry = useCallback(async (
    journalEntry: {
      title: string;
      content: string;
      emotion: string;
      moodScore: number;
    }
  ): Promise<MemoryExtractionResponse> => {
    setState(prev => ({
      ...prev,
      isExtracting: true,
      extractionError: null,
    }));

    try {
      const startTime = Date.now();
      const result = await extractMemoriesFromJournalEntry(journalEntry);
      const processingTime = Date.now() - startTime;
      processingTimes.current.push(processingTime);

      // Update stats
      const newStats = {
        totalExtractions: state.stats.totalExtractions + 1,
        successfulExtractions: state.stats.successfulExtractions + (result.success ? 1 : 0),
        totalMemoriesExtracted: state.stats.totalMemoriesExtracted + (result.memories?.length || 0),
        averageProcessingTime: calculateAverageProcessingTime(),
      };

      setState(prev => ({
        ...prev,
        isExtracting: false,
        stats: newStats,
      }));

      if (result.success && result.memories && result.memories.length > 0) {
        // Update state with extraction results
        setState(prev => ({
          ...prev,
          isExtracting: false,
          extractionError: null,
          memories: [...prev.memories, ...(result.memories || [])],
        }));
      }

      return result;
    } catch (error) {
      console.error('Error extracting memories from journal entry:', error);
      setState(prev => ({
        ...prev,
        isExtracting: false,
        extractionError: error instanceof Error ? error.message : 'Unknown error',
      }));
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        memories: [],
      };
    }
  }, [state.stats]);

  /**
   * Extract memories from a conversation
   */
  const extractFromConversation = useCallback(async (
    conversation: {
      messages: Array<{
        sender_type: 'user' | 'ai';
        message_content: string;
      }>;
      journalContext?: {
        title: string;
        content: string;
        emotion: string;
        moodScore: number;
      };
    }
  ): Promise<MemoryExtractionResponse> => {
    setState(prev => ({
      ...prev,
      isExtracting: true,
      extractionError: null,
    }));

    try {
      const startTime = Date.now();
      const result = await extractMemoriesFromConversation(conversation);
      const processingTime = Date.now() - startTime;
      processingTimes.current.push(processingTime);

      // Update stats
      const newStats = {
        totalExtractions: state.stats.totalExtractions + 1,
        successfulExtractions: state.stats.successfulExtractions + (result.success ? 1 : 0),
        totalMemoriesExtracted: state.stats.totalMemoriesExtracted + (result.memories?.length || 0),
        averageProcessingTime: calculateAverageProcessingTime(),
      };

      setState(prev => ({
        ...prev,
        isExtracting: false,
        stats: newStats,
      }));

      if (result.success && result.memories && result.memories.length > 0) {
        // Update state with extraction results
        setState(prev => ({
          ...prev,
          isExtracting: false,
          extractionError: null,
          memories: [...prev.memories, ...(result.memories || [])],
        }));
      }

      return result;
    } catch (error) {
      console.error('Error extracting memories from conversation:', error);
      setState(prev => ({
        ...prev,
        isExtracting: false,
        extractionError: error instanceof Error ? error.message : 'Unknown error',
      }));
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        memories: [],
      };
    }
  }, [state.stats]);

  /**
   * Remove a memory
   */
  const removeMemory = useCallback(async (key: string): Promise<void> => {
    try {
      const result = await removeUserMemory(key);
      if (result.success) {
        setState(prev => ({
          ...prev,
          memories: prev.memories.filter(m => m.key !== key),
        }));
      }
    } catch (error) {
      console.error('Error removing memory:', error);
      // Fallback to local state update
      setState(prev => ({
        ...prev,
        memories: prev.memories.filter(m => m.key !== key),
      }));
    }
  }, []);

  /**
   * Add a memory manually
   */
  const addMemory = useCallback(async (memory: UserMemory): Promise<void> => {
    try {
      const result = await addUserMemory(memory);
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          memories: [...prev.memories, result.data],
        }));
      }
    } catch (error) {
      console.error('Error adding memory:', error);
    }
  }, []);

  /**
   * Update an existing memory
   */
  const updateMemory = useCallback(async (key: string, updates: Partial<UserMemory>): Promise<void> => {
    try {
      const result = await updateUserMemory(key, updates);
      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          memories: prev.memories.map(m => (m.key === key ? result.data : m)),
        }));
      }
    } catch (error) {
      console.error('Error updating memory:', error);
    }
  }, []);

  /**
   * Get memories by category
   */
  const getMemoriesByCategory = useCallback((category: UserMemory['category']): UserMemory[] => {
    return state.memories.filter(memory => memory.category === category);
  }, [state.memories]);

  /**
   * Search memories by key or value
   */
  const searchMemories = useCallback((query: string): UserMemory[] => {
    const normalizedQuery = query.toLowerCase().trim();
    return state.memories.filter(memory => {
      return memory.key.toLowerCase().includes(normalizedQuery) || 
             memory.value.toLowerCase().includes(normalizedQuery);
    });
  }, [state.memories]);

  /**
   * Clear all memories
   */
  const clearMemories = useCallback(async () => {
    try {
      const result = await clearAllUserMemories();
      if (result.success) {
        setState(prev => ({ ...prev, memories: [] }));
      }
    } catch (error) {
      console.error('Error clearing memories from Supabase:', error);
      // Fallback to local state update
      setState(prev => ({ ...prev, memories: [] }));
    }
  }, []);

  /**
   * Remove duplicate memories
   */
  const deduplicateMemories = useCallback(async () => {
    try {
      console.log(' Starting memory deduplication...');
      const result = await deduplicateUserMemories();

      if (result.success && result.data) {
        console.log(' Deduplication completed:', result.data);

        // Reload memories to reflect changes - include all memories
        const memoriesResult = await getUserMemories({ orderByImportance: true, includeAllMemories: true });
        if (memoriesResult.success && memoriesResult.data) {
          setState(prev => ({
            ...prev,
            memories: memoriesResult.data,
          }));
        }

        return result.data;
      } else {
        console.error('Failed to deduplicate memories:', result.error);
        return null;
      }
    } catch (error) {
      console.error('Error deduplicating memories:', error);
      return null;
    }
  }, []);

  /**
   * Get all memories including low importance ones
   */
  const getAllMemories = useCallback(async () => {
    try {
      const result = await getUserMemories({
        includeAllMemories: true,
        orderByImportance: false,
      });

      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          memories: result.data || [],
        }));
      }
    } catch (error) {
      console.error('Error fetching all memories:', error);
    }
  }, []);

  /**
   * Get top important memories (default behavior)
   */
  const getImportantMemories = useCallback(async (options?: MemoryRetrievalOptions) => {
    try {
      const result = await getUserMemories({
        includeAllMemories: true,
        orderByImportance: true,
        ...options,
      });

      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          memories: result.data || [],
        }));
      }
    } catch (error) {
      console.error('Error fetching important memories:', error);
    }
  }, []);

  // Load memories on mount
  useEffect(() => {
    if (user) {
      getImportantMemories();
    }
  }, [user, getImportantMemories]);

  /**
   * Get relevant memories for a specific query
   */
  const getRelevantMemoriesForQuery = useCallback(async (
    query: string,
    options?: RelevanceRetrievalOptions
  ): Promise<RelevantMemory[]> => {
    try {
      const result = await getRelevantMemories(query, options);
      if (result.success && result.data) {
        setRelevantMemories(result.data);
        return result.data;
      }
      return [];
    } catch (error) {
      console.error('Error getting relevant memories:', error);
      return [];
    }
  }, []);

  /**
   * Get relevant memories for a specific context
   */
  const getRelevantMemoriesForContext = useCallback(async (
    context: string,
    options?: Omit<RelevanceRetrievalOptions, 'minRelevance'>
  ): Promise<RelevantMemory[]> => {
    try {
      const result = await getContextRelevantMemories(context, options);
      if (result.success && result.data) {
        setRelevantMemories(result.data);
        return result.data;
      }
      return [];
    } catch (error) {
      console.error('Error getting context-relevant memories:', error);
      return [];
    }
  }, []);

  /**
 * Type guard to check if a memory is a RelevantMemory
 */
const isRelevantMemory = (memory: UserMemory | RelevantMemory): memory is RelevantMemory => {
  return 'relevance' in memory && typeof memory.relevance === 'number';
};

/**
 * Get formatted memory context for AI prompts
 */
const getMemoryContext = useCallback(() => {
  // Use relevant memories if available, otherwise fall back to importance-based memories
  const memoriesToUse = relevantMemories.length > 0 
    ? relevantMemories.slice(0, 10) 
    : state.memories.slice(0, 10); // Limit to top 10 memories
  
  if (memoriesToUse.length === 0) return '';

  // Categorize memories
  const categorizedMemories = memoriesToUse.reduce<Record<string, Array<UserMemory | RelevantMemory>>>((acc, memory) => {
    if (!acc[memory.category]) {
      acc[memory.category] = [];
    }
    acc[memory.category].push(memory);
    return acc;
  }, {});

  let context = 'User Context (Important things to remember about this user):\n\n';

  Object.entries(categorizedMemories).forEach(([category, memories]) => {
    if (memories && memories.length > 0) {
      context += `${category.toUpperCase()}:\n`;
      memories.forEach((memory) => {
        // Include relevance scores if using relevant memories
        if (isRelevantMemory(memory) && relevantMemories.length > 0) {
          context += `- ${memory.key}: ${memory.value} [relevance: ${(memory.relevance * 100).toFixed(0)}%]\n`;
        } else {
          context += `- ${memory.key}: ${memory.value}\n`;
        }
      });
      context += '\n';
    }
  });

  return context.trim();
}, [state.memories, relevantMemories]);

  return {
    ...state,
    extractFromJournalEntry,
    extractFromConversation,
    addMemory,
    removeMemory,
    updateMemory,
    getMemoriesByCategory,
    searchMemories,
    clearMemories,
    deduplicateMemories,
    getAllMemories,
    getImportantMemories,
    getRelevantMemoriesForQuery,
    getRelevantMemoriesForContext,
    getMemoryContext,
    relevantMemories,
  };
};
