import { Button } from '@/components/ui/button';
import { ArrowDown, Sparkles, Heart, BookOpen } from 'lucide-react';

interface HeroProps {
  onGetStarted: () => void;
}

export const Hero = ({ onGetStarted }: HeroProps) => {
  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Modern gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-amber-50 via-orange-50 to-warm-50">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-amber-300 to-orange-300 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-orange-300 to-amber-300 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-5" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,167,38,0.3) 1px, transparent 0)`,
          backgroundSize: '20px 20px'
        }}></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center max-w-5xl mx-auto px-6">
        <div className="animate-fade-in space-y-8">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/40 backdrop-blur-sm border border-amber-200/50 rounded-full text-amber-700 text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            Your mindful journaling companion
          </div>

          {/* Main heading */}
          <div className="space-y-4">
            <h1 className="text-6xl md:text-8xl font-bold text-gradient leading-none tracking-tight">
              Amberglow
            </h1>
            <p className="text-2xl md:text-3xl text-gray-600 font-light max-w-3xl mx-auto leading-relaxed">
              Transform your thoughts into insights with AI-powered reflection and mindful journaling
            </p>
          </div>

          {/* CTA Button */}
          <div className="flex justify-center pt-4">
            <Button
              size="lg"
              onClick={onGetStarted}
              className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white px-12 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
            >
              <BookOpen className="w-5 h-5 mr-2" />
              Start Journaling
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="pt-8 flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              Private & Secure
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
              AI-Powered Insights
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
              Mindful Growth
            </div>
          </div>
        </div>


      </div>

      {/* Floating decorative elements */}
      <div className="absolute top-20 left-10 w-3 h-3 bg-amber-400 rounded-full animate-pulse opacity-60"></div>
      <div className="absolute top-32 right-16 w-2 h-2 bg-orange-400 rounded-full animate-pulse delay-500 opacity-60"></div>
      <div className="absolute bottom-32 left-20 w-4 h-4 bg-warm-300 rounded-full animate-pulse delay-1000 opacity-60"></div>
      <div className="absolute bottom-20 right-12 w-3 h-3 bg-amber-300 rounded-full animate-pulse delay-1500 opacity-60"></div>
    </section>
  );
};
