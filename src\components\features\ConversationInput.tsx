/**
 * Conversation Input Component
 * Input field for sending messages in conversations
 */

import React, { useState, useRef, useCallback } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface ConversationInputProps {
  /** Function called when user sends a message */
  onSendMessage: (message: string) => Promise<void>;
  /** Whether the input should be disabled */
  disabled?: boolean;
  /** Whether a message is currently being sent */
  isLoading?: boolean;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Maximum length for messages */
  maxLength?: number;
  /** Additional CSS classes */
  className?: string;
  /** Test ID for testing */
  testId?: string;
}

export const ConversationInput: React.FC<ConversationInputProps> = ({
  onSendMessage,
  disabled = false,
  isLoading = false,
  placeholder = "Share your thoughts with <PERSON>...",
  maxLength = 1000,
  className = '',
  testId,
}) => {
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  }, []);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setMessage(value);
      adjustTextareaHeight();
    }
  };

  // Handle send message
  const handleSendMessage = async () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage || isSending || disabled) return;

    setIsSending(true);
    try {
      await onSendMessage(trimmedMessage);
      setMessage('');
      
      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const isDisabled = disabled || isSending || isLoading;
  const canSend = message.trim().length > 0 && !isDisabled;

  return (
    <div className={`border-t border-amber-200/30 bg-white/50 backdrop-blur-sm p-4 ${className}`} data-testid={testId}>
      <div className="flex items-end gap-3">
        {/* Message input */}
        <div className="flex-1 relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            disabled={isDisabled}
            className={`
              min-h-[44px] max-h-[120px] resize-none
              glass-effect border-amber-200 bg-white/70
              focus:border-amber-400 focus:ring-amber-400/20
              placeholder:text-amber-500/70
              ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            style={{ height: 'auto' }}
          />
          
          {/* Character count */}
          <div className="absolute bottom-2 right-2 text-xs text-amber-500/70">
            {message.length}/{maxLength}
          </div>
        </div>

        {/* Send button */}
        <Button
          onClick={handleSendMessage}
          disabled={!canSend}
          size="sm"
          className={`
            h-11 px-4 bg-gradient-to-r from-amber-500 to-orange-500 
            hover:from-amber-600 hover:to-orange-600
            text-white border-0 shadow-md
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200
          `}
        >
          {isSending || isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* Helper text */}
      <div className="mt-2 text-xs text-amber-600/70">
        Press Enter to send, Shift+Enter for new line
      </div>
    </div>
  );
};

/**
 * Conversation starter component for when no conversation exists
 */
interface ConversationStarterProps {
  /** Function called when user wants to start a conversation */
  onStartConversation: () => Promise<void>;
  /** Whether the starter should be disabled */
  disabled?: boolean;
  /** Whether a conversation is being created */
  isLoading?: boolean;
  /** Additional CSS classes */
  className?: string;
}

export const ConversationStarter: React.FC<ConversationStarterProps> = ({
  onStartConversation,
  disabled = false,
  isLoading = false,
  className = '',
}) => {
  const [isStarting, setIsStarting] = useState(false);

  const handleStartConversation = async () => {
    if (isStarting || disabled) return;

    setIsStarting(true);
    try {
      await onStartConversation();
    } catch (error) {
      console.error('Error starting conversation:', error);
    } finally {
      setIsStarting(false);
    }
  };

  const isDisabled = disabled || isStarting || isLoading;

  return (
    <div className={`border-t border-amber-200/30 bg-white/50 backdrop-blur-sm p-6 text-center ${className}`}>
      <div className="max-w-md mx-auto">
        <h3 className="text-lg font-semibold text-amber-800 mb-2">
          Ready to reflect with Amber?
        </h3>
        <p className="text-amber-600 text-sm mb-4">
          Start a conversation about your journal entry. Amber is here to listen, understand, and help you explore your thoughts and feelings.
        </p>
        
        <Button
          onClick={handleStartConversation}
          disabled={isDisabled}
          className={`
            bg-gradient-to-r from-amber-500 to-orange-500 
            hover:from-amber-600 hover:to-orange-600
            text-white border-0 shadow-md px-6 py-2
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200
          `}
        >
          {isStarting || isLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              Starting conversation...
            </>
          ) : (
            <>
              <Send className="w-4 h-4 mr-2" />
              Start conversation with Amber
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default ConversationInput;
