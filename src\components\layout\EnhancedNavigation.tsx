/**
 * Enhanced Navigation Component
 * Improved navigation component using the new reusable components
 * Consolidates navigation patterns with amber/orange branding and glass-effect styling
 */

import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { NavigationButton, NavigationButtonGroup } from '@/components/ui/navigation-button';
import { UserDropdown } from './UserDropdown';
import { AmberButton } from '@/components/ui/amber-button';
import { Book, Plus, User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserProfile } from '@/hooks/useUserProfile';
import { useNavigation } from '@/hooks/useNavigation';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface EnhancedNavigationProps extends BaseComponentProps {
  /** Whether to show the navigation */
  show?: boolean;
  /** Navigation variant */
  variant?: 'default' | 'minimal';
  /** Whether to use glass effect */
  glassEffect?: boolean;
}

/**
 * EnhancedNavigation component with reusable components and consistent styling
 */
export const EnhancedNavigation: React.FC<EnhancedNavigationProps> = ({
  show = true,
  variant = 'default',
  glassEffect = true,
  className,
  testId,
  ...props
}) => {
  const { user, signOut } = useAuth();
  const { profile } = useUserProfile();
  const location = useLocation();
  const { navigateToView } = useNavigation();

  // Helper function to check if a route is active
  const isActiveRoute = (path: string): boolean => {
    return location.pathname === path;
  };

  const handleSignOut = async () => {
    try {
      console.log('🧭 Enhanced Navigation: Initiating sign-out');
      await signOut();
    } catch (error) {
      console.error('🧭 Enhanced Navigation: Error signing out:', error);
      // AuthContext will handle redirect even on error
    }
  };

  const handleSettings = () => {
    navigateToView('settings');
  };

  const handleAnalytics = () => {
    navigateToView('analytics');
  };

  if (!show) {
    return null;
  }

  return (
    <nav
      className={cn(
        'sticky top-0 z-50 border-b border-amber-200/30 shadow-sm',
        glassEffect && 'backdrop-blur-md bg-white/30',
        !glassEffect && 'bg-white',
        className
      )}
      data-testid={testId}
      {...props}
    >
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <h1 className="text-2xl font-bold text-gradient">Amberglow</h1>
          </Link>

          {/* Navigation Items */}
          <div className="flex items-center gap-2">
            {user ? (
              <>
                {/* Authenticated Navigation */}
                {variant === 'default' && (
                  <NavigationButtonGroup gap="sm">
                    <NavigationButton
                      to="/journal"
                      isActive={isActiveRoute('/journal')}
                      icon={<Book className="w-4 h-4" />}
                    >
                      Journal
                    </NavigationButton>
                    
                    <NavigationButton
                      to="/write"
                      isActive={isActiveRoute('/write')}
                      icon={<Plus className="w-4 h-4" />}
                    >
                      Write
                    </NavigationButton>
                  </NavigationButtonGroup>
                )}

                {/* User Dropdown */}
                <UserDropdown
                  user={{
                    id: user.id,
                    email: user.email || '',
                    full_name: profile?.full_name,
                    avatar_url: profile?.avatar_url,
                  }}
                  onSignOut={handleSignOut}
                  onSettings={handleSettings}
                  onAnalytics={handleAnalytics}
                  testId={testId ? `${testId}-user-dropdown` : undefined}
                />
              </>
            ) : (
              <>
                {/* Unauthenticated Navigation */}
                <AmberButton
                  variant="primary"
                  icon={<User className="w-4 h-4" />}
                  onClick={() => navigateToView('home')}
                >
                  Sign In
                </AmberButton>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

/**
 * MobileNavigation component for mobile-specific navigation
 */
interface MobileNavigationProps extends BaseComponentProps {
  /** Whether the mobile menu is open */
  isOpen: boolean;
  /** Function to close the mobile menu */
  onClose: () => void;
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  isOpen,
  onClose,
  className,
  testId,
  ...props
}) => {
  const { user, signOut } = useAuth();
  const { profile } = useUserProfile();
  const location = useLocation();
  const { navigateToView } = useNavigation();

  const isActiveRoute = (path: string): boolean => {
    return location.pathname === path;
  };

  const handleNavigation = (view: any) => {
    navigateToView(view);
    onClose();
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      onClose();
    } catch (error) {
      console.error('Mobile Navigation: Error signing out:', error);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div
      className={cn(
        'fixed inset-0 z-50 bg-black/50 backdrop-blur-sm',
        className
      )}
      onClick={onClose}
      data-testid={testId}
      {...props}
    >
      <div
        className="fixed right-0 top-0 h-full w-80 max-w-[90vw] glass-effect bg-white/95 backdrop-blur-md shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-amber-200/30">
            <h2 className="text-lg font-bold text-gradient">Menu</h2>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-amber-50 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Navigation Items */}
          <div className="flex-1 p-4">
            {user ? (
              <NavigationButtonGroup direction="vertical" gap="sm">
                <NavigationButton
                  to="/journal"
                  isActive={isActiveRoute('/journal')}
                  icon={<Book className="w-4 h-4" />}
                  onClick={onClose}
                >
                  Journal
                </NavigationButton>
                
                <NavigationButton
                  to="/write"
                  isActive={isActiveRoute('/write')}
                  icon={<Plus className="w-4 h-4" />}
                  onClick={onClose}
                >
                  Write
                </NavigationButton>
              </NavigationButtonGroup>
            ) : (
              <AmberButton
                variant="primary"
                fullWidth
                icon={<User className="w-4 h-4" />}
                onClick={() => handleNavigation('home')}
              >
                Sign In
              </AmberButton>
            )}
          </div>

          {/* User Info (if authenticated) */}
          {user && (
            <div className="border-t border-amber-200/30 p-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
                  <span className="text-amber-700 font-medium">
                    {profile?.full_name?.[0] || user.email?.[0] || 'U'}
                  </span>
                </div>
                <div>
                  <p className="font-medium">{profile?.full_name || 'User'}</p>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
              </div>
              
              <AmberButton
                variant="outline"
                fullWidth
                onClick={handleSignOut}
              >
                Sign Out
              </AmberButton>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
