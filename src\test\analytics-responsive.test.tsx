/**
 * Analytics Responsive Design Tests
 * Tests to verify the responsive chart components work correctly
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MoodTrendChart } from '@/components/features/analytics/MoodTrendChart';
import { EmotionDistributionChart } from '@/components/features/analytics/EmotionDistributionChart';

// Mock data for testing
const mockMoodTrendData = [
  { date: '2024-01-01', averageMood: 7, entryCount: 3 },
  { date: '2024-01-02', averageMood: 8, entryCount: 2 },
];

const mockEmotionData = [
  { emotion: 'joyful', count: 5, percentage: 50, averageMood: 8, color: '#F59E0B' },
  { emotion: 'calm', count: 3, percentage: 30, averageMood: 7, color: '#10B981' },
];

// Mock the mobile hook
const mockIsMobile = vi.fn();
vi.mock('@/hooks/use-mobile', () => ({
  useIsMobile: () => mockIsMobile(),
}));

// Mock Recharts components to avoid rendering issues in tests
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  Tooltip: ({ children }: any) => <div data-testid="tooltip">{children}</div>,
}));

describe('Analytics Responsive Design', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('MoodTrendChart', () => {
    it('should render with desktop layout', () => {
      mockIsMobile.mockReturnValue(false);

      render(
        <MoodTrendChart
          data={mockMoodTrendData}
          timeRange="30d"
          isLoading={false}
        />
      );

      expect(screen.getByText('Mood Trend')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    it('should render with mobile layout', () => {
      mockIsMobile.mockReturnValue(true);

      render(
        <MoodTrendChart
          data={mockMoodTrendData}
          timeRange="30d"
          isLoading={false}
        />
      );

      expect(screen.getByText('Mood Trend')).toBeInTheDocument();
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    it('should show loading state', () => {
      mockIsMobile.mockReturnValue(false);

      render(
        <MoodTrendChart
          data={undefined}
          timeRange="30d"
          isLoading={true}
        />
      );

      expect(screen.getByText('Loading mood trend...')).toBeInTheDocument();
    });
  });

  describe('EmotionDistributionChart', () => {
    it('should render with desktop layout', () => {
      mockIsMobile.mockReturnValue(false);

      render(
        <EmotionDistributionChart
          data={mockEmotionData}
          isLoading={false}
        />
      );

      expect(screen.getByText('Emotion Distribution')).toBeInTheDocument();
      expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    });

    it('should render with mobile layout', () => {
      mockIsMobile.mockReturnValue(true);

      render(
        <EmotionDistributionChart
          data={mockEmotionData}
          isLoading={false}
        />
      );

      expect(screen.getByText('Emotion Distribution')).toBeInTheDocument();
      expect(screen.getByTestId('pie-chart')).toBeInTheDocument();
    });

    it('should show loading state', () => {
      mockIsMobile.mockReturnValue(false);

      render(
        <EmotionDistributionChart
          data={undefined}
          isLoading={true}
        />
      );

      expect(screen.getByText('Loading emotion data...')).toBeInTheDocument();
    });
  });
});
