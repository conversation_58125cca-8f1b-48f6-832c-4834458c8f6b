/**
 * AI Configuration System
 * Centralized configuration for AI services with environment-specific settings
 */

import { AIServiceConfig, AIFeatureConfig, AIPromptTemplate } from '@/types';
import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * Get environment-specific AI configurations
 */
const getAIConfigs = () => {
  const env = getEnvironmentConfig();

  return {
    development: {
      apiKey: env.ai.geminiApiKey, // Optional for backward compatibility
      model: 'llama3.1:8b',
      maxRetries: 2,
      baseDelay: 500,
      timeout: 15000,
      serviceType: 'local-llm' as const,
      localLLMEndpoint: 'http://localhost:11434/v1/chat/completions',
    },
    staging: {
      apiKey: env.ai.geminiApiKey, // Optional for backward compatibility
      model: 'llama3.1:8b',
      maxRetries: 3,
      baseDelay: 1000,
      timeout: 20000,
      serviceType: 'local-llm' as const,
      localLLMEndpoint: 'http://localhost:11434/v1/chat/completions',
    },
    production: {
      apiKey: env.ai.geminiApiKey, // Optional for backward compatibility
      model: 'llama3.1:8b',
      maxRetries: 3,
      baseDelay: 1000,
      timeout: 30000,
      serviceType: 'local-llm' as const,
      localLLMEndpoint: 'http://localhost:11434/v1/chat/completions',
    },
  } as const;
};

/**
 * Get AI feature flags and configuration
 */
const getAIFeatures = (): AIFeatureConfig => {
  const env = getEnvironmentConfig();

  return {
    reflectionEnabled: env.features.aiEnabled,
    fallbackEnabled: true,
    showAISource: env.isDevelopment || env.features.debugMode,
    analyticsEnabled: env.features.analyticsEnabled,
    customPrompts: [], // Can be extended with custom prompts
  };
};

/**
 * AI prompt templates for different scenarios
 */
const AI_PROMPT_TEMPLATES: AIPromptTemplate[] = [
  {
    name: 'journal_reflection',
    description: 'Standard journal entry reflection prompt',
    variables: ['title', 'content', 'emotion', 'moodScore'],
    template: `Here is a journal entry: "{{title}} - {{content}}". Act like a thoughtful friend and respond with a JSON object containing:
- summary: A brief, empathetic summary of the main themes
- emotion: The primary emotion you sense (single word or short phrase)
- encouragement: A warm, supportive message that validates their feelings
- reflection_question: A gentle, open-ended question to help them reflect further

Keep the tone conversational, friend-like, and supportive. Avoid clinical or therapeutic language.

Current emotion they selected: {{emotion}}
Their mood score: {{moodScore}}/10

Respond with valid JSON only:
{
  "summary": "A friendly 1-2 sentence summary in conversational tone",
  "emotion": "The main emotion you detect (single word like 'joy', 'anxiety', 'gratitude')",
  "encouragement": "A supportive response as if you're their close friend, using casual language and empathy",
  "reflection_question": "A gentle question a caring friend might ask to help them reflect deeper"
}`,
  },
  {
    name: 'mood_check_in',
    description: 'Prompt for mood check-in without full journal entry',
    variables: ['emotion', 'moodScore'],
    template: `A user has shared their current mood: {{emotion}} with a score of {{moodScore}}/10. 
Respond as a caring friend with a JSON object containing:
- summary: A brief acknowledgment of their current state
- emotion: Reflect back their emotion with understanding
- encouragement: A supportive message appropriate for their mood level
- reflection_question: A gentle question to help them explore their feelings

Respond with valid JSON only.`,
  },
  {
    name: 'gratitude_reflection',
    description: 'Specialized prompt for gratitude entries',
    variables: ['title', 'content', 'emotion', 'moodScore'],
    template: `This is a gratitude journal entry: "{{title}} - {{content}}". 
Respond as a warm friend celebrating their gratitude practice with a JSON object containing:
- summary: Acknowledge what they're grateful for
- emotion: Reflect the positive emotion
- encouragement: Celebrate their gratitude practice and its benefits
- reflection_question: Ask how this gratitude might influence their day or week

Keep the tone uplifting and encouraging. Respond with valid JSON only.`,
  },
];

/**
 * Gets the current environment
 */
const getCurrentEnvironment = (): keyof ReturnType<typeof getAIConfigs> => {
  const env = getEnvironmentConfig();
  return env.environment;
};

/**
 * Gets AI configuration for current environment
 */
export const getAIConfig = (): AIServiceConfig => {
  const envName = getCurrentEnvironment();
  const configs = getAIConfigs();
  const config = configs[envName];
  const features = getAIFeatures();

  // Validate configuration
  if (features.reflectionEnabled) {
    if (config.serviceType === 'local-llm') {
      console.log(`AI reflection enabled using Local LLM (${config.model}) at ${config.localLLMEndpoint}`);
    } else if (!config.apiKey) {
      console.warn(`AI reflection is enabled but no API key found for ${envName} environment`);
    }
  }

  return config;
};

/**
 * Gets AI feature configuration
 */
export { getAIFeatures };

/**
 * Gets AI prompt template by name
 */
export const getPromptTemplate = (name: string): AIPromptTemplate | undefined => {
  return AI_PROMPT_TEMPLATES.find(template => template.name === name);
};

/**
 * Gets all available prompt templates
 */
export const getAllPromptTemplates = (): AIPromptTemplate[] => {
  return AI_PROMPT_TEMPLATES;
};

/**
 * Renders a prompt template with provided variables
 */
export const renderPromptTemplate = (
  templateName: string,
  variables: Record<string, string | number>
): string => {
  const template = getPromptTemplate(templateName);

  if (!template) {
    throw new Error(`Prompt template '${templateName}' not found`);
  }

  // Check if all required variables are provided
  const missingVariables = template.variables.filter(variable => !(variable in variables));

  if (missingVariables.length > 0) {
    throw new Error(
      `Missing required variables for template '${templateName}': ${missingVariables.join(', ')}`
    );
  }

  // Replace template variables
  let renderedTemplate = template.template;

  for (const [key, value] of Object.entries(variables)) {
    const placeholder = `{{${key}}}`;
    renderedTemplate = renderedTemplate.replace(new RegExp(placeholder, 'g'), String(value));
  }

  return renderedTemplate;
};

/**
 * Validates AI configuration
 */
export const validateAIConfig = (): { isValid: boolean; errors: string[]; warnings: string[] } => {
  const config = getAIConfig();
  const features = getAIFeatures();
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check API key
  if (features.reflectionEnabled && !config.apiKey) {
    errors.push('AI reflection is enabled but no API key is configured');
  }

  // Check timeout values
  if (config.timeout < 5000) {
    warnings.push('AI timeout is very low, may cause frequent timeouts');
  }

  if (config.timeout > 60000) {
    warnings.push('AI timeout is very high, may cause poor user experience');
  }

  // Check retry configuration
  if (config.maxRetries > 5) {
    warnings.push('High retry count may cause delays');
  }

  if (config.maxRetries < 1) {
    warnings.push('Low retry count may cause failures on temporary issues');
  }

  // Check model availability
  const supportedModels = ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-pro'];
  if (!supportedModels.includes(config.model)) {
    warnings.push(`Model '${config.model}' may not be supported`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Gets AI configuration summary for debugging
 */
export const getAIConfigSummary = () => {
  const config = getAIConfig();
  const features = getAIFeatures();
  const validation = validateAIConfig();

  return {
    environment: getCurrentEnvironment(),
    hasApiKey: !!config.apiKey,
    model: config.model,
    maxRetries: config.maxRetries,
    timeout: config.timeout,
    features: {
      reflectionEnabled: features.reflectionEnabled,
      fallbackEnabled: features.fallbackEnabled,
      showAISource: features.showAISource,
      analyticsEnabled: features.analyticsEnabled,
    },
    validation: {
      isValid: validation.isValid,
      errorCount: validation.errors.length,
      warningCount: validation.warnings.length,
    },
    availableTemplates: AI_PROMPT_TEMPLATES.map(t => t.name),
  };
};
