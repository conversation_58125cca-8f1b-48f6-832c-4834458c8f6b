/**
 * Performance Monitoring Hook
 * React hook for monitoring component and application performance
 */

import { useEffect, useRef, useState } from 'react';
import {
  performanceService,
  measurePerformance,
  markPerformance,
} from '@/services/performance.service';
import { webVitalsService, getWebVitalsSummary } from '@/services/webVitals.service';
import { errorTrackingService } from '@/services/errorTracking.service';
import { PerformanceMetrics, PerformanceAlert } from '@/types/performance';
import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * Hook for monitoring component performance
 */
export function useComponentPerformance(componentName: string) {
  const renderCount = useRef(0);
  const mountTime = useRef<number>(0);
  const env = getEnvironmentConfig();

  useEffect(() => {
    // Mark component mount
    mountTime.current = performance.now();
    markPerformance(`${componentName}-mount`);

    return () => {
      // Mark component unmount
      markPerformance(`${componentName}-unmount`);

      if (env.features.debugMode) {
        const totalTime = performance.now() - mountTime.current;
        console.log(`[Performance] ${componentName} was mounted for ${totalTime}ms`);
      }
    };
  }, [componentName, env.features.debugMode]);

  useEffect(() => {
    renderCount.current += 1;

    if (env.features.debugMode) {
      console.log(`[Performance] ${componentName} render #${renderCount.current}`);

      // Warn about excessive re-renders
      if (renderCount.current > 10) {
        console.warn(`[Performance] ${componentName} has rendered ${renderCount.current} times`);
      }
    }
  });

  return {
    renderCount: renderCount.current,
    measureRender: (fn: () => void) => measurePerformance(`${componentName}-render`, fn),
  };
}

/**
 * Hook for monitoring API performance
 */
export function useApiPerformance() {
  const [metrics, setMetrics] = useState<{
    averageResponseTime: number;
    totalRequests: number;
    errorRate: number;
  }>({
    averageResponseTime: 0,
    totalRequests: 0,
    errorRate: 0,
  });

  const measureApiCall = async <T>(
    apiCall: () => Promise<T>,
    operationName: string
  ): Promise<T> => {
    const startTime = performance.now();
    markPerformance(`api-${operationName}-start`);

    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;

      markPerformance(`api-${operationName}-end`);

      // Record successful API call metrics
      performanceService.recordMetrics({
        renderTime: 0,
        apiResponseTime: duration,
        cacheHitRate: 0,
        memoryUsage: 0,
      });

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;

      markPerformance(`api-${operationName}-error`);

      // Record failed API call metrics
      performanceService.recordMetrics({
        renderTime: 0,
        apiResponseTime: duration,
        cacheHitRate: 0,
        memoryUsage: 0,
      });

      throw error;
    }
  };

  useEffect(() => {
    // Update metrics periodically
    const interval = setInterval(() => {
      const allMetrics = performanceService.getMetrics();
      const apiMetrics = allMetrics.filter(m => m.apiResponseTime > 0);

      if (apiMetrics.length > 0) {
        const averageResponseTime =
          apiMetrics.reduce((sum, m) => sum + m.apiResponseTime, 0) / apiMetrics.length;
        setMetrics({
          averageResponseTime,
          totalRequests: apiMetrics.length,
          errorRate: 0, // This would need to be calculated based on error tracking
        });
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return {
    metrics,
    measureApiCall,
  };
}

/**
 * Hook for monitoring cache performance
 */
export function useCachePerformance() {
  const [cacheStats, setCacheStats] = useState<{
    hitRate: number;
    totalSize: number;
    totalItems: number;
  }>({
    hitRate: 0,
    totalSize: 0,
    totalItems: 0,
  });

  useEffect(() => {
    // This would integrate with the cache service to get real stats
    // For now, we'll simulate cache performance monitoring
    const interval = setInterval(() => {
      // In a real implementation, this would get actual cache stats
      setCacheStats({
        hitRate: Math.random() * 0.3 + 0.7, // Simulate 70-100% hit rate
        totalSize: Math.floor(Math.random() * 1000000), // Random size in bytes
        totalItems: Math.floor(Math.random() * 100), // Random item count
      });
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  return cacheStats;
}

/**
 * Hook for monitoring memory usage
 */
export function useMemoryMonitoring() {
  const [memoryUsage, setMemoryUsage] = useState<{
    used: number;
    total: number;
    percentage: number;
  }>({
    used: 0,
    total: 0,
    percentage: 0,
  });

  useEffect(() => {
    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const used = memory.usedJSHeapSize / 1024 / 1024; // MB
        const total = memory.totalJSHeapSize / 1024 / 1024; // MB
        const percentage = (used / total) * 100;

        setMemoryUsage({ used, total, percentage });

        // Record memory metrics
        performanceService.recordMetrics({
          renderTime: 0,
          apiResponseTime: 0,
          cacheHitRate: 0,
          memoryUsage: used,
        });
      }
    };

    checkMemory();
    const interval = setInterval(checkMemory, 5000);

    return () => clearInterval(interval);
  }, []);

  return memoryUsage;
}

/**
 * Hook for performance alerts
 */
export function usePerformanceAlerts() {
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      const currentAlerts = performanceService.getAlerts();
      setAlerts(currentAlerts.slice(-10)); // Keep last 10 alerts
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const dismissAlert = (timestamp: number) => {
    setAlerts(prev => prev.filter(alert => alert.timestamp !== timestamp));
  };

  return {
    alerts,
    dismissAlert,
  };
}

/**
 * Hook for Web Vitals monitoring
 */
export function useWebVitals() {
  const [webVitals, setWebVitals] = useState(getWebVitalsSummary());

  useEffect(() => {
    const interval = setInterval(() => {
      setWebVitals(getWebVitalsSummary());
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return webVitals;
}

/**
 * Hook for overall performance dashboard
 */
export function usePerformanceDashboard() {
  const componentPerf = useComponentPerformance('Dashboard');
  const apiPerf = useApiPerformance();
  const cachePerf = useCachePerformance();
  const memoryPerf = useMemoryMonitoring();
  const alertsPerf = usePerformanceAlerts();
  const webVitals = useWebVitals();

  const [overallScore, setOverallScore] = useState<number>(0);

  useEffect(() => {
    // Calculate overall performance score (0-100) including Web Vitals
    const scores = {
      api:
        apiPerf.metrics.averageResponseTime < 1000
          ? 100
          : Math.max(0, 100 - (apiPerf.metrics.averageResponseTime - 1000) / 10),
      cache: cachePerf.hitRate * 100,
      memory: Math.max(0, 100 - memoryPerf.percentage),
      alerts: Math.max(0, 100 - alertsPerf.alerts.length * 10),
      webVitals: calculateWebVitalsScore(webVitals),
    };

    const overall =
      Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.keys(scores).length;
    setOverallScore(Math.round(overall));
  }, [apiPerf.metrics, cachePerf.hitRate, memoryPerf.percentage, alertsPerf.alerts.length, webVitals]);

  return {
    overallScore,
    component: componentPerf,
    api: apiPerf,
    cache: cachePerf,
    memory: memoryPerf,
    alerts: alertsPerf,
    webVitals,
  };
}

/**
 * Calculate Web Vitals score
 */
function calculateWebVitalsScore(webVitals: Record<string, any>): number {
  const vitals = Object.values(webVitals);
  if (vitals.length === 0) return 100;

  const goodCount = vitals.filter((vital: any) => vital.rating === 'good').length;
  return (goodCount / vitals.length) * 100;
}



/**
 * Performance monitoring provider hook
 */
export function usePerformanceProvider() {
  const env = getEnvironmentConfig();

  useEffect(() => {
    if (!env.features.debugMode) return;

    // Log performance summary periodically
    const interval = setInterval(() => {
      const metrics = performanceService.getMetrics();
      const alerts = performanceService.getAlerts();

      console.group('[Performance Summary]');
      console.log(`Total metrics recorded: ${metrics.length}`);
      console.log(`Active alerts: ${alerts.length}`);

      if (metrics.length > 0) {
        const recent = metrics.slice(-10);
        const avgRender = recent.reduce((sum, m) => sum + m.renderTime, 0) / recent.length;
        const avgApi = recent.reduce((sum, m) => sum + m.apiResponseTime, 0) / recent.length;

        console.log(`Average render time: ${avgRender.toFixed(2)}ms`);
        console.log(`Average API time: ${avgApi.toFixed(2)}ms`);
      }

      console.groupEnd();
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [env.features.debugMode]);

  return {
    service: performanceService,
    isEnabled: env.features.debugMode,
  };
}
