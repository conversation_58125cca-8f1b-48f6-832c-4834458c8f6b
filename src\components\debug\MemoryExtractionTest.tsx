/**
 * Memory Extraction Test Component
 * Debug component to test the memory extraction pipeline
 */

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { extractMemoriesFromJournalEntry } from '@/services/memoryExtractionService';
import { getUserMemories } from '@/services/memoryPersistenceService';
import { Brain, TestTube, Database } from 'lucide-react';
import { toast } from 'sonner';

export const MemoryExtractionTest: React.FC = () => {
  const [testContent, setTestContent] = useState(
    "I have a cat named <PERSON><PERSON><PERSON> who loves to play with yarn. I'm currently learning Spanish because I want to travel to Barcelona next year. I work as a software engineer at a tech startup and I'm feeling stressed about the upcoming product launch. My goal is to exercise more and eat healthier. I love hiking on weekends and I'm a vegetarian."
  );
  const [isExtracting, setIsExtracting] = useState(false);
  const [isLoadingMemories, setIsLoadingMemories] = useState(false);
  const [extractionResult, setExtractionResult] = useState<any>(null);
  const [savedMemories, setSavedMemories] = useState<any[]>([]);

  const handleTestExtraction = async () => {
    setIsExtracting(true);
    setExtractionResult(null);
    
    try {
      console.log('🧪 [TEST] Starting memory extraction test...');
      
      const result = await extractMemoriesFromJournalEntry({
        title: "Test Journal Entry",
        content: testContent,
        emotion: "mixed",
        moodScore: 7,
      });
      
      console.log('🧪 [TEST] Extraction result:', result);
      setExtractionResult(result);
      
      if (result.success && result.memories.length > 0) {
        toast.success(`✅ Extracted ${result.memories.length} memories successfully!`);
      } else if (result.success && result.memories.length === 0) {
        toast.info('ℹ️ No memories extracted (this is normal for some content)');
      } else {
        toast.error(`❌ Extraction failed: ${result.error}`);
      }
    } catch (error) {
      console.error('🧪 [TEST] Extraction test failed:', error);
      toast.error('❌ Test failed - check console for details');
    } finally {
      setIsExtracting(false);
    }
  };

  const handleLoadSavedMemories = async () => {
    setIsLoadingMemories(true);
    
    try {
      console.log('🧪 [TEST] Loading saved memories from database...');
      
      const result = await getUserMemories();
      
      console.log('🧪 [TEST] Saved memories result:', result);
      
      if (result.success && result.data) {
        setSavedMemories(result.data);
        toast.success(`📚 Loaded ${result.data.length} saved memories`);
      } else {
        toast.error(`❌ Failed to load memories: ${result.error?.message}`);
      }
    } catch (error) {
      console.error('🧪 [TEST] Failed to load memories:', error);
      toast.error('❌ Failed to load memories - check console for details');
    } finally {
      setIsLoadingMemories(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card className="glass-effect border-amber-200/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-amber-900">
            <TestTube className="w-5 h-5" />
            Memory Extraction Test
          </CardTitle>
          <p className="text-amber-700/70 text-sm">
            Test the automatic memory extraction pipeline with sample content
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-amber-900 mb-2">
              Test Content (modify to test different scenarios):
            </label>
            <Textarea
              value={testContent}
              onChange={(e) => setTestContent(e.target.value)}
              rows={6}
              className="w-full"
              placeholder="Enter test journal content with personal information..."
            />
          </div>
          
          <div className="flex gap-4">
            <Button
              onClick={handleTestExtraction}
              disabled={isExtracting || !testContent.trim()}
              className="bg-amber-600 hover:bg-amber-700"
            >
              <Brain className="w-4 h-4 mr-2" />
              {isExtracting ? 'Extracting...' : 'Test Memory Extraction'}
            </Button>
            
            <Button
              onClick={handleLoadSavedMemories}
              disabled={isLoadingMemories}
              variant="outline"
              className="border-amber-300 text-amber-700 hover:bg-amber-50"
            >
              <Database className="w-4 h-4 mr-2" />
              {isLoadingMemories ? 'Loading...' : 'Load Saved Memories'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {extractionResult && (
        <Card className="glass-effect border-green-200/30">
          <CardHeader>
            <CardTitle className="text-green-900">Extraction Result</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <strong>Success:</strong> {extractionResult.success ? '✅ Yes' : '❌ No'}
              </div>
              
              {extractionResult.error && (
                <div>
                  <strong>Error:</strong> <span className="text-red-600">{extractionResult.error}</span>
                </div>
              )}
              
              <div>
                <strong>Processing Time:</strong> {extractionResult.processingTime}ms
              </div>
              
              <div>
                <strong>Memories Found:</strong> {extractionResult.memories?.length || 0}
              </div>
              
              {extractionResult.memories && extractionResult.memories.length > 0 && (
                <div>
                  <strong>Extracted Memories:</strong>
                  <div className="mt-2 space-y-2">
                    {extractionResult.memories.map((memory: any, index: number) => (
                      <div key={index} className="p-3 bg-green-50 rounded-lg border border-green-200">
                        <div><strong>Key:</strong> {memory.key}</div>
                        <div><strong>Value:</strong> {memory.value}</div>
                        <div><strong>Category:</strong> {memory.category}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {savedMemories.length > 0 && (
        <Card className="glass-effect border-blue-200/30">
          <CardHeader>
            <CardTitle className="text-blue-900">Saved Memories ({savedMemories.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {savedMemories.map((memory, index) => (
                <div key={index} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div><strong>Key:</strong> {memory.key}</div>
                  <div><strong>Value:</strong> {memory.value}</div>
                  <div><strong>Category:</strong> {memory.category}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
