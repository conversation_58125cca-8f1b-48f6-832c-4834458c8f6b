/**
 * React Query Configuration
 * Optimized QueryClient setup with caching strategies and performance optimizations
 */

import { QueryClient, DefaultOptions, MutationCache, QueryCache } from '@tanstack/react-query';
import { getEnvironmentConfig } from '@/config/environment.config';
import { handleApiError } from '@/utils/errorHandler';
import { toast } from '@/components/ui/sonner';

/**
 * Environment-specific query configurations
 */
const getQueryConfig = () => {
  const env = getEnvironmentConfig();

  const baseConfig = {
    // Stale time - how long data is considered fresh
    staleTime: 5 * 60 * 1000, // 5 minutes
    // Cache time - how long data stays in cache after component unmounts
    cacheTime: 10 * 60 * 1000, // 10 minutes
    // Retry configuration
    retry: (failureCount: number, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  };

  if (env.isDevelopment) {
    return {
      ...baseConfig,
      staleTime: 1 * 60 * 1000, // 1 minute in development for faster updates
      cacheTime: 5 * 60 * 1000, // 5 minutes in development
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    };
  }

  if (env.isProduction) {
    return {
      ...baseConfig,
      staleTime: 10 * 60 * 1000, // 10 minutes in production
      cacheTime: 30 * 60 * 1000, // 30 minutes in production
      refetchOnWindowFocus: false, // Reduce unnecessary requests in production
      refetchOnReconnect: true,
    };
  }

  return baseConfig;
};

/**
 * Default query options
 */
const createDefaultOptions = (): DefaultOptions => {
  const config = getQueryConfig();

  return {
    queries: {
      staleTime: config.staleTime,
      cacheTime: config.cacheTime,
      retry: config.retry,
      retryDelay: config.retryDelay,
      refetchOnWindowFocus: config.refetchOnWindowFocus ?? false,
      refetchOnReconnect: config.refetchOnReconnect ?? true,
      refetchOnMount: true,
      // Network mode configuration
      networkMode: 'online',
      // Error handling
      useErrorBoundary: false,
      // Suspense configuration
      suspense: false,
    },
    mutations: {
      retry: (failureCount: number, error: any) => {
        // Don't retry mutations by default to avoid duplicate operations
        return false;
      },
      // Error handling for mutations
      useErrorBoundary: false,
      // Network mode for mutations
      networkMode: 'online',
    },
  };
};

/**
 * Query cache configuration with error handling
 */
const createQueryCache = (): QueryCache => {
  return new QueryCache({
    onError: (error: any, query) => {
      // Log query errors for monitoring
      console.error('Query error:', {
        queryKey: query.queryKey,
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      // Handle specific error types
      if (error?.status === 401) {
        // Handle authentication errors
        toast.error('Your session has expired. Please sign in again.');
      } else if (error?.status >= 500) {
        // Handle server errors
        toast.error('Server error occurred. Please try again later.');
      }

      // Report error for monitoring
      handleApiError(
        {
          code: error?.status?.toString() || 'UNKNOWN',
          message: error?.message || 'Query failed',
          retryable: error?.status >= 500,
        },
        'QueryCache',
        'query_error',
        { showToast: false } // Already handled above
      );
    },
    onSuccess: (data, query) => {
      // Log successful queries in development
      const env = getEnvironmentConfig();
      if (env.features.debugMode) {
        console.log('Query success:', {
          queryKey: query.queryKey,
          dataSize: JSON.stringify(data).length,
          timestamp: new Date().toISOString(),
        });
      }
    },
  });
};

/**
 * Mutation cache configuration with error handling
 */
const createMutationCache = (): MutationCache => {
  return new MutationCache({
    onError: (error: any, variables, context, mutation) => {
      // Log mutation errors
      console.error('Mutation error:', {
        mutationKey: mutation.options.mutationKey,
        error: error.message,
        variables,
        timestamp: new Date().toISOString(),
      });

      // Handle specific error types
      if (error?.status === 401) {
        toast.error('Authentication required. Please sign in.');
      } else if (error?.status === 403) {
        toast.error('You do not have permission to perform this action.');
      } else if (error?.status === 409) {
        toast.error('Conflict detected. Please refresh and try again.');
      } else if (error?.status >= 500) {
        toast.error('Server error occurred. Please try again.');
      } else {
        toast.error(error?.message || 'Operation failed. Please try again.');
      }

      // Report error for monitoring
      handleApiError(
        {
          code: error?.status?.toString() || 'UNKNOWN',
          message: error?.message || 'Mutation failed',
          retryable: error?.status >= 500,
        },
        'MutationCache',
        'mutation_error',
        { showToast: false } // Already handled above
      );
    },
    onSuccess: (data, variables, context, mutation) => {
      // Log successful mutations in development
      const env = getEnvironmentConfig();
      if (env.features.debugMode) {
        console.log('Mutation success:', {
          mutationKey: mutation.options.mutationKey,
          variables,
          timestamp: new Date().toISOString(),
        });
      }
    },
  });
};

/**
 * Create optimized QueryClient instance
 */
export const createOptimizedQueryClient = (): QueryClient => {
  const env = getEnvironmentConfig();

  const queryClient = new QueryClient({
    defaultOptions: createDefaultOptions(),
    queryCache: createQueryCache(),
    mutationCache: createMutationCache(),
  });

  // Development logging is now handled through the QueryCache and MutationCache
  // onSuccess and onError callbacks above, which provide more granular control
  // React Query v5 removed the setLogger method in favor of these event handlers

  return queryClient;
};

/**
 * Query key factories for consistent cache management
 */
export const queryKeys = {
  // Journal entries
  journalEntries: {
    all: ['journalEntries'] as const,
    lists: () => [...queryKeys.journalEntries.all, 'list'] as const,
    list: (userId: string, filters?: any) =>
      [...queryKeys.journalEntries.lists(), userId, filters] as const,
    details: () => [...queryKeys.journalEntries.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.journalEntries.details(), id] as const,
    stats: (userId: string) => [...queryKeys.journalEntries.all, 'stats', userId] as const,
  },

  // User profile
  user: {
    all: ['user'] as const,
    profile: (userId: string) => [...queryKeys.user.all, 'profile', userId] as const,
    preferences: (userId: string) => [...queryKeys.user.all, 'preferences', userId] as const,
  },

  // AI reflections
  aiReflections: {
    all: ['aiReflections'] as const,
    generate: (input: any) => [...queryKeys.aiReflections.all, 'generate', input] as const,
    history: (userId: string) => [...queryKeys.aiReflections.all, 'history', userId] as const,
  },

  // Application data
  app: {
    all: ['app'] as const,
    config: () => [...queryKeys.app.all, 'config'] as const,
    health: () => [...queryKeys.app.all, 'health'] as const,
  },

  // Analytics data
  analytics: {
    all: ['analytics'] as const,
    mood: (userId: string, filters: any) => [...queryKeys.analytics.all, 'mood', userId, filters] as const,
    emotions: (userId: string, filters: any) => [...queryKeys.analytics.all, 'emotions', userId, filters] as const,
    trends: (userId: string, filters: any) => [...queryKeys.analytics.all, 'trends', userId, filters] as const,
    patterns: (userId: string, filters: any) => [...queryKeys.analytics.all, 'patterns', userId, filters] as const,
    calendar: (userId: string, filters: any) => [...queryKeys.analytics.all, 'calendar', userId, filters] as const,
  },
} as const;

/**
 * Cache invalidation utilities
 */
export const cacheUtils = {
  /**
   * Invalidate all journal entries for a user
   */
  invalidateJournalEntries: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.journalEntries.list(userId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.journalEntries.stats(userId),
    });
  },

  /**
   * Invalidate specific journal entry
   */
  invalidateJournalEntry: (queryClient: QueryClient, entryId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.journalEntries.detail(entryId),
    });
  },

  /**
   * Invalidate user data
   */
  invalidateUserData: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.profile(userId),
    });
    queryClient.invalidateQueries({
      queryKey: queryKeys.user.preferences(userId),
    });
  },

  /**
   * Clear all cache
   */
  clearAllCache: (queryClient: QueryClient) => {
    queryClient.clear();
  },

  /**
   * Remove stale data
   */
  removeStaleData: (queryClient: QueryClient) => {
    queryClient.removeQueries({
      stale: true,
    });
  },
};

/**
 * Prefetch utilities
 */
export const prefetchUtils = {
  /**
   * Prefetch journal entries for a user
   */
  prefetchJournalEntries: async (queryClient: QueryClient, userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.journalEntries.list(userId),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  },

  /**
   * Prefetch user profile
   */
  prefetchUserProfile: async (queryClient: QueryClient, userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.user.profile(userId),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  },
};
