/**
 * Lazy AI Reflection Component
 * Lazy-loaded version of AIReflection for better performance
 */

import { createLazyComponent } from '@/utils/performance.utils';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';

// Lazy load the AIReflection component
export const LazyAIReflection = createLazyComponent(
  () => import('./AIReflection').then(module => ({ default: module.AIReflection })),
  () => (
    <div className="flex items-center justify-center p-4">
      <LoadingSpinner size="md" message="Loading AI reflection..." />
    </div>
  ),
  ({ error }) => (
    <div className="flex items-center justify-center p-4 text-red-600">
      <div className="text-center">
        <p className="text-sm font-semibold mb-1">Failed to load AI reflection</p>
        <p className="text-xs">{error.message}</p>
      </div>
    </div>
  )
);
