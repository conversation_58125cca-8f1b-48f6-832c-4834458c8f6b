# Docker Compose configuration for AmberGlow Embedding Service
# Optional - for advanced users who prefer Docker deployment

version: '3.8'

services:
  embedding-service:
    build: .
    ports:
      - "5005:5005"
    environment:
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5005
      - FLASK_DEBUG=false
    volumes:
      # Optional: Mount logs directory
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5005/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# Optional: Add a reverse proxy for production
# nginx:
#   image: nginx:alpine
#   ports:
#     - "80:80"
#   volumes:
#     - ./nginx.conf:/etc/nginx/nginx.conf
#   depends_on:
#     - embedding-service
