/**
 * Emotion Badge Atom
 * Reusable badge component for displaying emotions with robust error handling
 */

import { Badge } from '@/components/ui/badge';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

/**
 * Valid emotion types that can be displayed in the badge
 * Add new emotions here and update the emotionColors mapping below
 */
export type ValidEmotion =
  | 'joyful'
  | 'calm'
  | 'neutral'
  | 'sad'
  | 'anxious'
  | 'excited'
  | 'grateful'
  | 'frustrated'
  | 'hopeful'
  | 'overwhelmed';

interface EmotionBadgeProps extends BaseComponentProps {
  /** Emotion to display - can be undefined or any string for defensive programming */
  emotion?: string | null;
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show emoji */
  showEmoji?: boolean;
}

/**
 * Emotion color and emoji mapping
 * When adding new emotions:
 * 1. Add the emotion to the ValidEmotion type above
 * 2. Add the corresponding entry here with emoji and Tailwind classes
 * 3. Ensure the emotion name matches exactly (case-sensitive)
 */
const emotionColors: Record<ValidEmotion, { emoji: string; colorClass: string }> = {
  joyful: { emoji: '😊', colorClass: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' },
  calm: { emoji: '😌', colorClass: 'bg-blue-100 text-blue-800 hover:bg-blue-200' },
  neutral: { emoji: '😐', colorClass: 'bg-gray-100 text-gray-800 hover:bg-gray-200' },
  sad: { emoji: '😢', colorClass: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200' },
  anxious: { emoji: '😰', colorClass: 'bg-purple-100 text-purple-800 hover:bg-purple-200' },
  excited: { emoji: '🤩', colorClass: 'bg-orange-100 text-orange-800 hover:bg-orange-200' },
  grateful: { emoji: '🙏', colorClass: 'bg-green-100 text-green-800 hover:bg-green-200' },
  frustrated: { emoji: '😤', colorClass: 'bg-red-100 text-red-800 hover:bg-red-200' },
  hopeful: { emoji: '🌟', colorClass: 'bg-pink-100 text-pink-800 hover:bg-pink-200' },
  overwhelmed: { emoji: '😵', colorClass: 'bg-slate-100 text-slate-800 hover:bg-slate-200' },
};

const sizeClasses = {
  sm: 'text-xs px-2 py-1',
  md: 'text-sm px-3 py-1',
  lg: 'text-base px-4 py-2',
};

/**
 * Type guard to check if a string is a valid emotion
 * @param emotion - The emotion string to validate
 * @returns True if the emotion is valid, false otherwise
 */
const isValidEmotion = (emotion: string): emotion is ValidEmotion => {
  return emotion in emotionColors;
};

/**
 * Fallback configuration for invalid or undefined emotions
 */
const fallbackConfig = {
  emoji: '❓',
  colorClass: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
  displayText: 'Unknown Emotion',
};

export const EmotionBadge = ({
  emotion,
  size = 'md',
  showEmoji = false,
  className,
  testId,
}: EmotionBadgeProps) => {
  // Defensive programming: handle undefined, null, empty string, and invalid emotions
  let config = fallbackConfig;
  let displayText = fallbackConfig.displayText;

  if (emotion && typeof emotion === 'string' && emotion.trim() !== '') {
    const trimmedEmotion = emotion.trim().toLowerCase();

    // Check if the emotion exists in our mapping
    if (isValidEmotion(trimmedEmotion)) {
      config = emotionColors[trimmedEmotion];
      displayText = trimmedEmotion;
    } else {
      // For invalid emotions, still show the original text but with fallback styling
      displayText = emotion;
    }
  }

  return (
    <Badge
      variant="secondary"
      className={cn(
        'capitalize font-medium transition-colors',
        config.colorClass,
        sizeClasses[size],
        className
      )}
      data-testid={testId}
    >
      {showEmoji && <span className="mr-1">{config.emoji}</span>}
      {displayText}
    </Badge>
  );
};
