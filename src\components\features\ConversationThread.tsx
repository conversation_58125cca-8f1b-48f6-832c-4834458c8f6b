/**
 * Conversation Thread Component
 * Main component for displaying and managing conversation threads
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Brain, MessageCircle, Trash2, RefreshCw } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ConversationMessageList } from './ConversationMessage';
import { ConversationInput, ConversationStarter } from './ConversationInput';
import { useConversationManager } from '@/hooks/useConversation';
import { JournalEntry } from '@/types';
import { toast } from 'sonner';

interface ConversationThreadProps {
  /** The journal entry this conversation is about */
  journalEntry: JournalEntry;
  /** Whether to automatically initialize the conversation */
  autoInitialize?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Test ID for testing */
  testId?: string;
}

export const ConversationThread: React.FC<ConversationThreadProps> = ({
  journalEntry,
  autoInitialize = false,
  className = '',
  testId,
}) => {
  const { state, actions, mutations } = useConversationManager(
    journalEntry.id,
    journalEntry,
    autoInitialize
  );

  // Handle starting a conversation
  const handleStartConversation = async () => {
    try {
      await actions.startConversation(journalEntry.id);
    } catch (error) {
      console.error('Error starting conversation:', error);
      toast.error('Failed to start conversation. Please try again.');
    }
  };

  // Handle sending a message
  const handleSendMessage = async (message: string) => {
    try {
      await actions.sendMessage(message);
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
    }
  };

  // Handle deleting conversation
  const handleDeleteConversation = async () => {
    if (!state.conversation) return;

    if (window.confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      try {
        await mutations.deleteConversation.mutateAsync(state.conversation.id);
      } catch (error) {
        console.error('Error deleting conversation:', error);
        toast.error('Failed to delete conversation. Please try again.');
      }
    }
  };

  // Handle retrying last response
  const handleRetryResponse = async () => {
    try {
      await actions.retryLastResponse();
    } catch (error) {
      console.error('Error retrying response:', error);
      toast.error('Failed to retry response. Please try again.');
    }
  };

  const hasConversation = !!state.conversation;
  const hasMessages = state.messages.length > 0;

  return (
    <Card className={`card-elevated border-amber-200/40 bg-gradient-to-br from-amber-50 to-orange-50 ${className}`} data-testid={testId}>
      <CardHeader className="section-padding pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3 text-amber-800 heading-modern">
            <div className="w-8 h-8 bg-amber-100 rounded-xl flex items-center justify-center shadow-sm">
              <Brain className="w-4 h-4 text-amber-600" />
            </div>
            Conversation with Amber
            {hasMessages && (
              <span className="text-sm font-normal text-amber-600 bg-amber-100 px-3 py-1 rounded-full">
                {state.messages.length} message{state.messages.length !== 1 ? 's' : ''}
              </span>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {hasConversation && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRetryResponse}
                  disabled={state.isGenerating || !hasMessages}
                  className="text-amber-600 hover:text-amber-700 hover:bg-amber-100"
                  title="Retry last response"
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDeleteConversation}
                  disabled={mutations.deleteConversation.isPending}
                  className="text-red-600 hover:text-red-700 hover:bg-red-100"
                  title="Delete conversation"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </>
            )}
            

          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
          {state.isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <Brain className="w-8 h-8 text-amber-600 animate-pulse mx-auto mb-2" />
                <p className="text-amber-600 text-sm">Loading conversation...</p>
              </div>
            </div>
          ) : state.error ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <p className="text-red-600 text-sm mb-2">Failed to load conversation</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => actions.loadConversation(state.conversation?.id || '')}
                  className="text-amber-600 border-amber-200 hover:bg-amber-50"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Retry
                </Button>
              </div>
            </div>
          ) : hasConversation || autoInitialize ? (
            <div className="flex flex-col h-96">
              {/* Messages */}
              <ConversationMessageList
                messages={state.messages}
                isGenerating={state.isGenerating}
                className="flex-1"
                testId={`${testId}-messages`}
              />

              {/* Input */}
              <ConversationInput
                onSendMessage={handleSendMessage}
                disabled={state.isGenerating || (!hasConversation && autoInitialize)}
                isLoading={mutations.sendMessage.isPending || mutations.createConversation.isPending}
                testId={`${testId}-input`}
              />
            </div>
          ) : (
            /* Conversation starter */
            <ConversationStarter
              onStartConversation={handleStartConversation}
              disabled={mutations.createConversation.isPending}
              isLoading={mutations.createConversation.isPending}
            />
          )}
      </CardContent>
    </Card>
  );
};

/**
 * Compact conversation thread for use in journal entry cards
 */
interface CompactConversationThreadProps {
  /** The journal entry this conversation is about */
  journalEntry: JournalEntry;
  /** Function called when user wants to expand to full conversation */
  onExpand?: () => void;
  /** Additional CSS classes */
  className?: string;
}

export const CompactConversationThread: React.FC<CompactConversationThreadProps> = ({
  journalEntry,
  onExpand,
  className = '',
}) => {
  const { state, actions } = useConversationManager(journalEntry.id, journalEntry);

  const hasConversation = !!state.conversation;
  const messageCount = state.messages.length;
  const lastMessage = state.messages[state.messages.length - 1];

  if (!hasConversation) {
    return (
      <div className={`p-3 border-t border-amber-200/30 ${className}`}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => actions.startConversation(journalEntry.id)}
          disabled={state.isLoading}
          className="w-full text-amber-600 hover:text-amber-700 hover:bg-amber-100"
        >
          <MessageCircle className="w-4 h-4 mr-2" />
          Start conversation with Amber
        </Button>
      </div>
    );
  }

  return (
    <div className={`p-3 border-t border-amber-200/30 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Brain className="w-4 h-4 text-amber-600" />
          <span className="text-sm font-medium text-amber-800">
            Conversation with Amber
          </span>
          <span className="text-xs text-amber-600">
            ({messageCount} message{messageCount !== 1 ? 's' : ''})
          </span>
        </div>
        
        {onExpand && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onExpand}
            className="text-amber-600 hover:text-amber-700 hover:bg-amber-100"
          >
            View
          </Button>
        )}
      </div>
      
      {lastMessage && (
        <div className="text-xs text-amber-700 bg-amber-50 rounded p-2 truncate">
          <span className="font-medium">
            {lastMessage.sender_type === 'ai' ? 'Amber' : 'You'}:
          </span>{' '}
          {lastMessage.message_content}
        </div>
      )}
    </div>
  );
};

export default ConversationThread;
