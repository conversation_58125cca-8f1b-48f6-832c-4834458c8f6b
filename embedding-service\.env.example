# AmberGlow Embedding Service Configuration
# Copy this file to .env and modify as needed

# Flask server configuration
FLASK_HOST=localhost
FLASK_PORT=5005
FLASK_DEBUG=False

# Model configuration (advanced users only)
# MODEL_NAME=all-MiniLM-L6-v2

# Logging configuration
# LOG_LEVEL=INFO
# LOG_FILE=embedding-service.log

# Performance tuning (advanced)
# MAX_TEXT_LENGTH=10000
# REQUEST_TIMEOUT=30

# CORS origins (comma-separated)
# CORS_ORIGINS=http://localhost:5173,http://localhost:3000,http://127.0.0.1:5173
