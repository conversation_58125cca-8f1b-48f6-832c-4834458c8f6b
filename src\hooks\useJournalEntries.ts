/**
 * Journal Entries Hook
 * Custom hook for managing journal entries state and operations with enhanced error handling
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { FormattedJournalEntry } from '@/types';
import { useServiceWithLoading } from './useServiceWithLoading';
import {
  getJournalEntries,
  deleteJournalEntry,
  formatJournalEntryForDisplay,
} from '@/services/journalService';

/**
 * Hook return type
 */
interface UseJournalEntriesReturn {
  /** Array of formatted journal entries */
  entries: FormattedJournalEntry[];
  /** Loading state from service integration */
  loadingState: ReturnType<typeof useServiceWithLoading>['loadingState'];
  /** Load entries function */
  loadEntries: () => Promise<void>;
  /** Add new entry function */
  addEntry: (entry: FormattedJournalEntry) => void;
  /** Update existing entry function */
  updateEntry: (id: string, updates: Partial<FormattedJournalEntry>) => void;
  /** Delete entry function */
  deleteEntry: (id: string) => Promise<void>;
  /** Refresh entries */
  refreshEntries: () => Promise<void>;
  /** Check if specific operation is loading */
  isOperationLoading: (operationId: string) => boolean;
}

/**
 * Custom hook for managing journal entries
 */
export const useJournalEntries = (): UseJournalEntriesReturn => {
  const [entries, setEntries] = useState<FormattedJournalEntry[]>([]);
  const { user } = useAuth();
  const serviceManager = useServiceWithLoading('useJournalEntries');
  const loadedUserRef = useRef<string | null>(null);

  /**
   * Load journal entries from database
   */
  const loadEntries = useCallback(async (): Promise<void> => {
    if (!user) {
      setEntries([]);
      loadedUserRef.current = null;
      return;
    }

    // Prevent multiple simultaneous loads
    if (serviceManager.loadingState.isLoading) {
      console.log('📚 Load already in progress, skipping...');
      return;
    }

    console.log('📚 Loading journal entries for user:', user.id);

    const journalEntries = await serviceManager.executeApiService(
      () => getJournalEntries(user.id),
      'load_entries',
      {
        operationId: 'load_entries',
        loadingMessage: 'Loading your journal entries...',
        enableRetry: true,
        maxRetries: 3,
      }
    );

    if (journalEntries) {
      const formattedEntries = journalEntries.map(formatJournalEntryForDisplay);
      setEntries(formattedEntries);
      console.log('📚 Loaded', formattedEntries.length, 'journal entries');
    } else {
      setEntries([]);
      console.log('📚 No journal entries found');
    }

    loadedUserRef.current = user.id;
  }, [user, serviceManager.executeApiService]);

  /**
   * Add a new entry to the local state
   */
  const addEntry = (entry: FormattedJournalEntry): void => {
    setEntries(prevEntries => [entry, ...prevEntries]);
  };

  /**
   * Update an existing entry in local state
   */
  const updateEntry = (id: string, updates: Partial<FormattedJournalEntry>): void => {
    setEntries(prevEntries =>
      prevEntries.map(entry => (entry.id === id ? { ...entry, ...updates } : entry))
    );
  };

  /**
   * Delete an entry
   */
  const deleteEntry = useCallback(
    async (id: string): Promise<void> => {
      if (!user) {
        console.warn('Delete entry called without user');
        return;
      }

      console.log('🗑️ Attempting to delete entry:', { id, userId: user.id });

      // Call the delete service directly to get the full response
      const response = await deleteJournalEntry(id, user.id);

      console.log('🗑️ Delete response:', response);

      if (response.success) {
        // Remove from local state on successful deletion
        setEntries(prevEntries => {
          const filteredEntries = prevEntries.filter(entry => entry.id !== id);
          console.log('🗑️ Updated entries count:', { before: prevEntries.length, after: filteredEntries.length });
          return filteredEntries;
        });
        console.log('✅ Entry deleted successfully from local state');
      } else {
        console.error('❌ Delete failed:', response.error);
        // Throw error to be caught by the calling component
        throw new Error(response.error?.message || 'Failed to delete journal entry');
      }
    },
    [user]
  );

  /**
   * Refresh entries (alias for loadEntries)
   */
  const refreshEntries = loadEntries;

  // Load entries when user changes (only once per user)
  useEffect(() => {
    if (user && loadedUserRef.current !== user.id) {
      console.log('📚 User changed, loading entries for:', user.id);
      loadEntries();
    } else if (!user) {
      console.log('📚 No user, clearing entries');
      setEntries([]);
      loadedUserRef.current = null;
    }
  }, [user]); // Removed loadEntries - it's stable enough for this use case

  return {
    entries,
    loadingState: serviceManager.loadingState,
    loadEntries,
    addEntry,
    updateEntry,
    deleteEntry,
    refreshEntries,
    isOperationLoading: serviceManager.isOperationLoading,
  };
};
