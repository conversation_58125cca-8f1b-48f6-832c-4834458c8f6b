/**
 * Analytics Service
 * Service for fetching and processing mood analytics data
 */

import { supabase } from '@/integrations/supabase/client';
import { 
  MoodAnalyticsData, 
  MoodAnalyticsStats,
  MoodTrendData,
  EmotionDistributionData,
  DailyMoodData,
  MoodEmotionCorrelationData,
  MoodPatternData,
  AnalyticsTimeRange, 
  AnalyticsFilters,
  ApiResponse,
  EmotionType,
  MoodScore,
} from '@/types';
import { subDays, format, startOfDay, endOfDay, getDay, getWeek, getHours, parseISO } from 'date-fns';

/**
 * Emotion colors for consistent chart styling
 */
const EMOTION_COLORS: Record<EmotionType, string> = {
  joyful: '#FCD34D',
  calm: '#60A5FA',
  neutral: '#9CA3AF',
  sad: '#A78BFA',
  anxious: '#F87171',
  excited: '#FBBF24',
  grateful: '#34D399',
  frustrated: '#FB7185',
  hopeful: '#A3E635',
  overwhelmed: '#F59E0B',
};

/**
 * Day names for pattern analysis
 */
const DAY_NAMES = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

/**
 * Get comprehensive mood analytics for a user
 */
export const getMoodAnalytics = async (
  userId: string,
  filters: AnalyticsFilters
): Promise<ApiResponse<MoodAnalyticsData>> => {
  try {
    const dateRange = getDateRangeFromFilter(filters.timeRange);
    
    // Fetch journal entries for the specified time range
    let query = supabase
      .from('journal_entries')
      .select('*')
      .eq('user_id', userId)
      .gte('created_at', dateRange.start.toISOString())
      .lte('created_at', dateRange.end.toISOString())
      .order('created_at', { ascending: true });

    // Apply additional filters
    if (filters.emotions && filters.emotions.length > 0) {
      query = query.in('emotion', filters.emotions);
    }

    if (filters.moodRange) {
      query = query
        .gte('mood_score', filters.moodRange.min)
        .lte('mood_score', filters.moodRange.max);
    }

    const { data: entries, error } = await query;

    if (error) throw error;

    // Process the data into analytics format
    const analytics = processAnalyticsData(entries || [], filters);

    return {
      success: true,
      data: analytics,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: 'ANALYTICS_ERROR',
        message: 'Failed to fetch mood analytics',
        details: (error as Error).message,
        retryable: true,
      },
    };
  }
};

/**
 * Process raw journal entries into analytics data
 */
const processAnalyticsData = (entries: any[], filters: AnalyticsFilters): MoodAnalyticsData => {
  // Calculate basic stats
  const stats = calculateMoodStats(entries, filters.timeRange);
  
  // Generate mood trend data
  const moodTrend = generateMoodTrend(entries, filters.timeRange);
  
  // Calculate emotion distribution
  const emotionDistribution = calculateEmotionDistribution(entries);
  
  // Generate daily mood data for calendar heatmap
  const dailyMoods = generateDailyMoodData(entries, filters.timeRange);
  
  // Calculate mood-emotion correlation
  const moodEmotionCorrelation = calculateMoodEmotionCorrelation(entries);
  
  // Analyze patterns
  const patterns = analyzeMoodPatterns(entries);

  return {
    stats,
    moodTrend,
    emotionDistribution,
    dailyMoods,
    moodEmotionCorrelation,
    patterns,
  };
};

/**
 * Calculate comprehensive mood statistics
 */
export const calculateMoodStats = (entries: any[], timeRange: AnalyticsTimeRange): MoodAnalyticsStats => {
  if (entries.length === 0) {
    return {
      totalEntries: 0,
      averageMood: 0,
      moodTrend: 'stable',
      mostCommonEmotion: 'neutral',
      moodRange: { min: 1, max: 10 },
      streakDays: 0,
      entriesThisWeek: 0,
      entriesThisMonth: 0,
      moodChangePercentage: 0,
    };
  }

  const moodScores = entries
    .map(entry => entry.mood_score)
    .filter(score => score !== null && score !== undefined) as number[];

  const averageMood = moodScores.length > 0 
    ? moodScores.reduce((sum, score) => sum + score, 0) / moodScores.length 
    : 0;

  // Count emotions
  const emotionCounts: Record<string, number> = {};
  entries.forEach(entry => {
    if (entry.emotion) {
      emotionCounts[entry.emotion] = (emotionCounts[entry.emotion] || 0) + 1;
    }
  });

  const mostCommonEmotion = Object.entries(emotionCounts).reduce(
    (max, [emotion, count]) => count > max.count ? { emotion, count } : max,
    { emotion: 'neutral', count: 0 }
  ).emotion as EmotionType;

  // Calculate mood trend
  const moodTrend = calculateMoodTrend(entries);

  // Time-based calculations
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  const entriesThisWeek = entries.filter(entry => 
    new Date(entry.created_at) >= oneWeekAgo
  ).length;

  const entriesThisMonth = entries.filter(entry => 
    new Date(entry.created_at) >= oneMonthAgo
  ).length;

  // Calculate streak (simplified)
  const streakDays = calculateStreakDays(entries);

  // Calculate mood change percentage
  const moodChangePercentage = calculateMoodChangePercentage(entries, timeRange);

  return {
    totalEntries: entries.length,
    averageMood: Math.round(averageMood * 100) / 100,
    moodTrend,
    mostCommonEmotion,
    moodRange: {
      min: Math.min(...moodScores) as MoodScore,
      max: Math.max(...moodScores) as MoodScore,
    },
    streakDays,
    entriesThisWeek,
    entriesThisMonth,
    moodChangePercentage,
  };
};

/**
 * Generate mood trend data for time series visualization
 */
export const generateMoodTrend = (entries: any[], timeRange: AnalyticsTimeRange): MoodTrendData[] => {
  if (entries.length === 0) return [];

  const dateRange = getDateRangeFromFilter(timeRange);
  const trendData: MoodTrendData[] = [];

  // Group entries by date
  const entriesByDate: Record<string, any[]> = {};
  entries.forEach(entry => {
    const date = format(parseISO(entry.created_at), 'yyyy-MM-dd');
    if (!entriesByDate[date]) {
      entriesByDate[date] = [];
    }
    entriesByDate[date].push(entry);
  });

  // Generate data points for each date in range
  let currentDate = dateRange.start;
  while (currentDate <= dateRange.end) {
    const dateStr = format(currentDate, 'yyyy-MM-dd');
    const dayEntries = entriesByDate[dateStr] || [];
    
    const moodScores = dayEntries
      .map(entry => entry.mood_score)
      .filter(score => score !== null && score !== undefined);

    const averageMood = moodScores.length > 0
      ? moodScores.reduce((sum, score) => sum + score, 0) / moodScores.length
      : 0;

    // Find dominant emotion
    const emotionCounts: Record<string, number> = {};
    dayEntries.forEach(entry => {
      if (entry.emotion) {
        emotionCounts[entry.emotion] = (emotionCounts[entry.emotion] || 0) + 1;
      }
    });

    const dominantEmotion = Object.entries(emotionCounts).reduce(
      (max, [emotion, count]) => count > max.count ? { emotion, count } : max,
      { emotion: null, count: 0 }
    ).emotion as EmotionType | null;

    trendData.push({
      date: dateStr,
      averageMood: Math.round(averageMood * 100) / 100,
      entryCount: dayEntries.length,
      dominantEmotion,
    });

    currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
  }

  return trendData;
};

/**
 * Calculate emotion distribution for pie/bar charts
 */
export const calculateEmotionDistribution = (entries: any[]): EmotionDistributionData[] => {
  if (entries.length === 0) return [];

  const emotionCounts: Record<string, { count: number; moodSum: number }> = {};
  
  entries.forEach(entry => {
    if (entry.emotion) {
      if (!emotionCounts[entry.emotion]) {
        emotionCounts[entry.emotion] = { count: 0, moodSum: 0 };
      }
      emotionCounts[entry.emotion].count++;
      emotionCounts[entry.emotion].moodSum += entry.mood_score || 0;
    }
  });

  return Object.entries(emotionCounts).map(([emotion, data]) => ({
    emotion: emotion as EmotionType,
    count: data.count,
    percentage: Math.round((data.count / entries.length) * 100 * 100) / 100,
    averageMood: Math.round((data.moodSum / data.count) * 100) / 100,
    color: EMOTION_COLORS[emotion as EmotionType] || '#9CA3AF',
  })).sort((a, b) => b.count - a.count);
};

/**
 * Get date range based on time filter
 */
const getDateRangeFromFilter = (timeRange: AnalyticsTimeRange) => {
  const end = endOfDay(new Date());
  let start: Date;

  switch (timeRange) {
    case '7d':
      start = startOfDay(subDays(end, 7));
      break;
    case '30d':
      start = startOfDay(subDays(end, 30));
      break;
    case '90d':
      start = startOfDay(subDays(end, 90));
      break;
    case '1y':
      start = startOfDay(subDays(end, 365));
      break;
    default:
      start = startOfDay(subDays(end, 30));
  }

  return { start, end };
};

/**
 * Calculate mood trend direction
 */
const calculateMoodTrend = (entries: any[]): 'improving' | 'declining' | 'stable' => {
  if (entries.length < 4) return 'stable';

  const recentEntries = entries.slice(-Math.ceil(entries.length / 2));
  const olderEntries = entries.slice(0, Math.floor(entries.length / 2));

  const recentAvg = recentEntries
    .filter(e => e.mood_score)
    .reduce((sum, e) => sum + (e.mood_score || 0), 0) / recentEntries.length;

  const olderAvg = olderEntries
    .filter(e => e.mood_score)
    .reduce((sum, e) => sum + (e.mood_score || 0), 0) / olderEntries.length;

  const difference = recentAvg - olderAvg;
  if (difference > 0.5) return 'improving';
  if (difference < -0.5) return 'declining';
  return 'stable';
};

/**
 * Calculate consecutive days streak (simplified)
 */
const calculateStreakDays = (entries: any[]): number => {
  if (entries.length === 0) return 0;

  const uniqueDates = new Set(
    entries.map(entry => format(parseISO(entry.created_at), 'yyyy-MM-dd'))
  );

  const sortedDates = Array.from(uniqueDates).sort().reverse();
  let streak = 0;
  let currentDate = new Date();

  for (const dateStr of sortedDates) {
    const entryDate = parseISO(dateStr);
    const daysDiff = Math.floor((currentDate.getTime() - entryDate.getTime()) / (24 * 60 * 60 * 1000));
    
    if (daysDiff === streak) {
      streak++;
      currentDate = entryDate;
    } else {
      break;
    }
  }

  return streak;
};

/**
 * Calculate mood change percentage
 */
const calculateMoodChangePercentage = (entries: any[], timeRange: AnalyticsTimeRange): number => {
  if (entries.length < 2) return 0;

  const midpoint = Math.floor(entries.length / 2);
  const firstHalf = entries.slice(0, midpoint);
  const secondHalf = entries.slice(midpoint);

  const firstAvg = firstHalf
    .filter(e => e.mood_score)
    .reduce((sum, e) => sum + (e.mood_score || 0), 0) / firstHalf.length;

  const secondAvg = secondHalf
    .filter(e => e.mood_score)
    .reduce((sum, e) => sum + (e.mood_score || 0), 0) / secondHalf.length;

  if (firstAvg === 0) return 0;
  return Math.round(((secondAvg - firstAvg) / firstAvg) * 100 * 100) / 100;
};

/**
 * Generate daily mood data for calendar heatmap
 */
export const generateDailyMoodData = (entries: any[], timeRange: AnalyticsTimeRange): DailyMoodData[] => {
  const dateRange = getDateRangeFromFilter(timeRange);
  const dailyData: DailyMoodData[] = [];

  // Group entries by date
  const entriesByDate: Record<string, any[]> = {};
  entries.forEach(entry => {
    const date = format(parseISO(entry.created_at), 'yyyy-MM-dd');
    if (!entriesByDate[date]) {
      entriesByDate[date] = [];
    }
    entriesByDate[date].push(entry);
  });

  // Generate data for each date in range
  let currentDate = dateRange.start;
  while (currentDate <= dateRange.end) {
    const dateStr = format(currentDate, 'yyyy-MM-dd');
    const dayEntries = entriesByDate[dateStr] || [];

    const moodScores = dayEntries
      .map(entry => entry.mood_score)
      .filter(score => score !== null && score !== undefined);

    const averageMood = moodScores.length > 0
      ? Math.round((moodScores.reduce((sum, score) => sum + score, 0) / moodScores.length))
      : null;

    // Find primary emotion
    const emotionCounts: Record<string, number> = {};
    dayEntries.forEach(entry => {
      if (entry.emotion) {
        emotionCounts[entry.emotion] = (emotionCounts[entry.emotion] || 0) + 1;
      }
    });

    const primaryEmotion = Object.entries(emotionCounts).reduce(
      (max, [emotion, count]) => count > max.count ? { emotion, count } : max,
      { emotion: null, count: 0 }
    ).emotion as EmotionType | null;

    dailyData.push({
      date: dateStr,
      mood: averageMood as MoodScore | null,
      emotion: primaryEmotion,
      hasEntry: dayEntries.length > 0,
      entryCount: dayEntries.length,
    });

    currentDate = new Date(currentDate.getTime() + 24 * 60 * 60 * 1000);
  }

  return dailyData;
};

/**
 * Calculate mood-emotion correlation
 */
export const calculateMoodEmotionCorrelation = (entries: any[]): MoodEmotionCorrelationData[] => {
  if (entries.length === 0) return [];

  const correlationMap: Record<string, { count: number; moodSum: number; aiEmotions: string[] }> = {};

  entries.forEach(entry => {
    if (entry.emotion && entry.mood_score) {
      const key = entry.emotion;
      if (!correlationMap[key]) {
        correlationMap[key] = { count: 0, moodSum: 0, aiEmotions: [] };
      }
      correlationMap[key].count++;
      correlationMap[key].moodSum += entry.mood_score;
      if (entry.ai_emotion) {
        correlationMap[key].aiEmotions.push(entry.ai_emotion);
      }
    }
  });

  return Object.entries(correlationMap).map(([emotion, data]) => {
    const averageMood = Math.round((data.moodSum / data.count));
    const mostCommonAIEmotion = data.aiEmotions.length > 0
      ? data.aiEmotions.reduce((a, b, i, arr) =>
          arr.filter(v => v === a).length >= arr.filter(v => v === b).length ? a : b
        )
      : '';

    return {
      userEmotion: emotion as EmotionType,
      aiEmotion: mostCommonAIEmotion,
      moodScore: averageMood as MoodScore,
      count: data.count,
      correlationStrength: Math.min(data.count / entries.length, 1),
    };
  }).sort((a, b) => b.count - a.count);
};

/**
 * Analyze mood patterns (weekly, monthly, time-of-day)
 */
export const analyzeMoodPatterns = (entries: any[]): MoodPatternData => {
  if (entries.length === 0) {
    return {
      weeklyPattern: DAY_NAMES.map((dayName, index) => ({
        dayOfWeek: index,
        dayName,
        averageMood: 0,
        entryCount: 0,
      })),
      monthlyPattern: [],
      timeOfDayPattern: [],
      bestDay: { dayOfWeek: 0, dayName: 'Sunday', averageMood: 0 },
      worstDay: { dayOfWeek: 0, dayName: 'Sunday', averageMood: 0 },
    };
  }

  // Weekly pattern analysis
  const weeklyData: Record<number, { moodSum: number; count: number }> = {};

  entries.forEach(entry => {
    if (entry.mood_score) {
      const dayOfWeek = getDay(parseISO(entry.created_at));
      if (!weeklyData[dayOfWeek]) {
        weeklyData[dayOfWeek] = { moodSum: 0, count: 0 };
      }
      weeklyData[dayOfWeek].moodSum += entry.mood_score;
      weeklyData[dayOfWeek].count++;
    }
  });

  const weeklyPattern = DAY_NAMES.map((dayName, index) => {
    const data = weeklyData[index] || { moodSum: 0, count: 0 };
    return {
      dayOfWeek: index,
      dayName,
      averageMood: data.count > 0 ? Math.round((data.moodSum / data.count) * 100) / 100 : 0,
      entryCount: data.count,
    };
  });

  // Find best and worst days
  const validDays = weeklyPattern.filter(day => day.entryCount > 0);
  const bestDay = validDays.reduce((best, day) =>
    day.averageMood > best.averageMood ? day : best,
    validDays[0] || weeklyPattern[0]
  );
  const worstDay = validDays.reduce((worst, day) =>
    day.averageMood < worst.averageMood ? day : worst,
    validDays[0] || weeklyPattern[0]
  );

  // Monthly pattern (simplified - by week of month)
  const monthlyData: Record<number, { moodSum: number; count: number }> = {};
  entries.forEach(entry => {
    if (entry.mood_score) {
      const week = getWeek(parseISO(entry.created_at));
      if (!monthlyData[week]) {
        monthlyData[week] = { moodSum: 0, count: 0 };
      }
      monthlyData[week].moodSum += entry.mood_score;
      monthlyData[week].count++;
    }
  });

  const monthlyPattern = Object.entries(monthlyData).map(([week, data]) => ({
    week: parseInt(week),
    averageMood: Math.round((data.moodSum / data.count) * 100) / 100,
    entryCount: data.count,
  })).sort((a, b) => a.week - b.week);

  // Time of day pattern (if available)
  const timeData: Record<number, { moodSum: number; count: number }> = {};
  entries.forEach(entry => {
    if (entry.mood_score) {
      const hour = getHours(parseISO(entry.created_at));
      if (!timeData[hour]) {
        timeData[hour] = { moodSum: 0, count: 0 };
      }
      timeData[hour].moodSum += entry.mood_score;
      timeData[hour].count++;
    }
  });

  const timeOfDayPattern = Object.entries(timeData).map(([hour, data]) => ({
    hour: parseInt(hour),
    averageMood: Math.round((data.moodSum / data.count) * 100) / 100,
    entryCount: data.count,
  })).sort((a, b) => a.hour - b.hour);

  return {
    weeklyPattern,
    monthlyPattern,
    timeOfDayPattern,
    bestDay,
    worstDay,
  };
};
