/**
 * Cached API Service
 * Wrapper service that adds intelligent caching to API calls
 */

import { ApiResponse } from '@/types';
import { CacheService, caches } from './cache.service';
import { getEnvironmentConfig } from '@/config/environment.config';
import { handleApiError } from '@/utils/errorHandler';

/**
 * Cache strategy options
 */
export interface CacheStrategy {
  /** Cache key generator function */
  keyGenerator: (...args: any[]) => string;
  /** Time to live in milliseconds */
  ttl?: number;
  /** Whether to use stale data while revalidating */
  staleWhileRevalidate?: boolean;
  /** Whether to cache errors */
  cacheErrors?: boolean;
  /** Cache invalidation tags */
  tags?: string[];
  /** Whether to bypass cache */
  bypassCache?: boolean;
}

/**
 * Cached API call options
 */
export interface CachedApiOptions extends CacheStrategy {
  /** Force refresh from API */
  forceRefresh?: boolean;
  /** Fallback data if API fails */
  fallbackData?: any;
  /** Whether to show loading state */
  showLoading?: boolean;
}

/**
 * Cache-aware API service
 */
export class CachedApiService {
  private cache = caches.api;
  private env = getEnvironmentConfig();

  /**
   * Execute API call with caching
   */
  async execute<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    options: CachedApiOptions
  ): Promise<ApiResponse<T>> {
    const {
      keyGenerator,
      ttl,
      staleWhileRevalidate = false,
      cacheErrors = false,
      forceRefresh = false,
      fallbackData,
      bypassCache = false,
    } = options;

    const cacheKey = keyGenerator();

    // Check if we should bypass cache
    if (bypassCache || forceRefresh) {
      return this.executeAndCache(apiCall, cacheKey, { ttl, cacheErrors });
    }

    // Try to get from cache first
    const cachedResult = this.cache.get(cacheKey);

    if (cachedResult) {
      // Return cached data immediately
      if (!staleWhileRevalidate) {
        return cachedResult;
      }

      // Return cached data but revalidate in background
      this.revalidateInBackground(apiCall, cacheKey, { ttl, cacheErrors });
      return cachedResult;
    }

    // No cached data, execute API call
    try {
      return await this.executeAndCache(apiCall, cacheKey, { ttl, cacheErrors });
    } catch (error) {
      // Return fallback data if available
      if (fallbackData) {
        return {
          success: true,
          data: fallbackData,
          meta: {
            timestamp: new Date().toISOString(),
            source: 'fallback',
          },
        };
      }

      throw error;
    }
  }

  /**
   * Execute API call and cache result
   */
  private async executeAndCache<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    cacheKey: string,
    options: { ttl?: number; cacheErrors?: boolean }
  ): Promise<ApiResponse<T>> {
    const { ttl, cacheErrors = false } = options;

    try {
      const result = await apiCall();

      // Cache successful results
      if (result.success) {
        this.cache.set(cacheKey, result, ttl);
      } else if (cacheErrors) {
        // Cache errors for a shorter time to avoid repeated failures
        this.cache.set(cacheKey, result, Math.min(ttl || 60000, 60000));
      }

      return result;
    } catch (error) {
      // Handle and potentially cache errors
      if (cacheErrors) {
        const errorResponse: ApiResponse<T> = {
          success: false,
          error: {
            code: 'API_ERROR',
            message: error instanceof Error ? error.message : 'Unknown error',
            retryable: true,
          },
        };

        this.cache.set(cacheKey, errorResponse, 30000); // Cache errors for 30 seconds
        return errorResponse;
      }

      throw error;
    }
  }

  /**
   * Revalidate data in background
   */
  private async revalidateInBackground<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    cacheKey: string,
    options: { ttl?: number; cacheErrors?: boolean }
  ): Promise<void> {
    try {
      const result = await this.executeAndCache(apiCall, cacheKey, options);

      // Log successful background revalidation in debug mode
      if (this.env.features.debugMode) {
        console.log('Background revalidation completed:', cacheKey);
      }
    } catch (error) {
      // Silently fail background revalidation
      console.warn('Background revalidation failed:', cacheKey, error);
    }
  }

  /**
   * Invalidate cache by key
   */
  invalidate(cacheKey: string): boolean {
    return this.cache.delete(cacheKey);
  }

  /**
   * Invalidate cache by pattern
   */
  invalidatePattern(pattern: string): number {
    const keys = this.cache.keys();
    let invalidated = 0;

    keys.forEach(key => {
      if (key.includes(pattern)) {
        this.cache.delete(key);
        invalidated++;
      }
    });

    return invalidated;
  }

  /**
   * Invalidate cache by tags
   */
  invalidateTags(tags: string[]): number {
    // This would require a more sophisticated tagging system
    // For now, we'll use pattern matching
    let invalidated = 0;

    tags.forEach(tag => {
      invalidated += this.invalidatePattern(tag);
    });

    return invalidated;
  }

  /**
   * Prefetch data
   */
  async prefetch<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    options: CachedApiOptions
  ): Promise<void> {
    try {
      await this.execute(apiCall, { ...options, showLoading: false });
    } catch (error) {
      // Silently fail prefetch attempts
      console.warn('Prefetch failed:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return this.cache.getStats();
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

/**
 * Default cached API service instance
 */
export const cachedApiService = new CachedApiService();

/**
 * Cache key generators for common patterns
 */
export const cacheKeyGenerators = {
  /**
   * Generate key for user-specific data
   */
  userSpecific: (userId: string, resource: string, ...params: any[]) =>
    `user:${userId}:${resource}:${params.join(':')}`,

  /**
   * Generate key for list data with filters
   */
  list: (resource: string, filters: Record<string, any> = {}) => {
    const filterString = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return `list:${resource}:${filterString}`;
  },

  /**
   * Generate key for single resource
   */
  detail: (resource: string, id: string) => `detail:${resource}:${id}`,

  /**
   * Generate key for search results
   */
  search: (query: string, filters: Record<string, any> = {}) => {
    const filterString = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
    return `search:${encodeURIComponent(query)}:${filterString}`;
  },

  /**
   * Generate key for AI-generated content
   */
  aiGenerated: (type: string, input: any) => {
    const inputHash = btoa(JSON.stringify(input)).slice(0, 16);
    return `ai:${type}:${inputHash}`;
  },
};

/**
 * Predefined cache strategies for common use cases
 */
export const cacheStrategies = {
  /**
   * Strategy for user profile data
   */
  userProfile: {
    keyGenerator: (userId: string) => cacheKeyGenerators.userSpecific(userId, 'profile'),
    ttl: 15 * 60 * 1000, // 15 minutes
    staleWhileRevalidate: true,
    cacheErrors: false,
  },

  /**
   * Strategy for journal entries list
   */
  journalEntries: {
    keyGenerator: (userId: string, filters?: any) =>
      cacheKeyGenerators.userSpecific(userId, 'journal-entries', JSON.stringify(filters || {})),
    ttl: 5 * 60 * 1000, // 5 minutes
    staleWhileRevalidate: true,
    cacheErrors: false,
  },

  /**
   * Strategy for individual journal entry
   */
  journalEntry: {
    keyGenerator: (entryId: string) => cacheKeyGenerators.detail('journal-entry', entryId),
    ttl: 10 * 60 * 1000, // 10 minutes
    staleWhileRevalidate: true,
    cacheErrors: false,
  },

  /**
   * Strategy for AI reflections
   */
  aiReflection: {
    keyGenerator: (input: any) => cacheKeyGenerators.aiGenerated('reflection', input),
    ttl: 30 * 60 * 1000, // 30 minutes
    staleWhileRevalidate: false, // AI responses are relatively stable
    cacheErrors: true, // Cache errors to avoid repeated API calls
  },

  /**
   * Strategy for application configuration
   */
  appConfig: {
    keyGenerator: () => 'app:config',
    ttl: 60 * 60 * 1000, // 1 hour
    staleWhileRevalidate: true,
    cacheErrors: false,
  },
};

/**
 * Utility functions for cache management
 */
export const cacheUtils = {
  /**
   * Warm up cache with common data
   */
  async warmUp(userId: string): Promise<void> {
    if (!userId) return;

    try {
      // Prefetch user profile
      await cachedApiService.prefetch(async () => {
        // This would be replaced with actual API call
        throw new Error('Not implemented');
      }, cacheStrategies.userProfile);

      // Prefetch recent journal entries
      await cachedApiService.prefetch(async () => {
        // This would be replaced with actual API call
        throw new Error('Not implemented');
      }, cacheStrategies.journalEntries);
    } catch (error) {
      console.warn('Cache warm-up failed:', error);
    }
  },

  /**
   * Clear user-specific cache
   */
  clearUserCache(userId: string): number {
    return cachedApiService.invalidatePattern(`user:${userId}`);
  },

  /**
   * Clear all journal-related cache
   */
  clearJournalCache(): number {
    return cachedApiService.invalidatePattern('journal');
  },

  /**
   * Clear AI-related cache
   */
  clearAICache(): number {
    return cachedApiService.invalidatePattern('ai:');
  },
};
