/**
 * Test script for semantic memory deduplication and recall functionality
 * Run this script to verify the semantic embedding features are working correctly
 */

// Test configuration
const TEST_CONFIG = {
  embeddingServiceUrl: 'http://localhost:8080/api/embedding',
  testMemories: [
    { key: 'pet_cat', value: 'I have a cat named <PERSON><PERSON><PERSON> who loves to play with yarn', category: 'fact' },
    { key: 'pet_feline', value: 'My feline companion Whiskers enjoys playing with string', category: 'fact' }, // Similar to above
    { key: 'work_stress', value: 'Feeling overwhelmed with quarterly reports and deadlines', category: 'emotion' },
    { key: 'hobby_reading', value: 'I love reading science fiction novels in my spare time', category: 'preference' },
    { key: 'goal_fitness', value: 'Want to run a marathon next year and improve my endurance', category: 'goal' },
  ],
  testPrompts: [
    'Tell me about my pets',
    'How am I feeling about work lately?',
    'What do I like to do for fun?',
    'What are my fitness goals?',
  ]
};

/**
 * Test embedding service health and functionality
 */
async function testEmbeddingService() {
  console.log('🧪 Testing embedding service...');
  
  try {
    // Test health check
    const healthResponse = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/health`);
    if (!healthResponse.ok) {
      throw new Error(`Health check failed: ${healthResponse.status}`);
    }
    
    const healthData = await healthResponse.json();
    console.log('✅ Embedding service health:', healthData);
    
    // Test embedding generation
    const testText = 'This is a test sentence for embedding generation';
    const embeddingResponse = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: testText })
    });
    
    if (!embeddingResponse.ok) {
      throw new Error(`Embedding generation failed: ${embeddingResponse.status}`);
    }
    
    const embeddingData = await embeddingResponse.json();
    console.log('✅ Embedding generation successful:', {
      dimensions: embeddingData.embedding?.length,
      sampleValues: embeddingData.embedding?.slice(0, 5)
    });
    
    return true;
  } catch (error) {
    console.error('❌ Embedding service test failed:', error);
    return false;
  }
}

/**
 * Test semantic similarity calculation
 */
async function testSemanticSimilarity() {
  console.log('\n🧪 Testing semantic similarity...');
  
  try {
    const text1 = 'I have a cat named Whiskers';
    const text2 = 'My feline companion is called Whiskers';
    const text3 = 'I work as a software engineer';
    
    // Generate embeddings
    const responses = await Promise.all([
      fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: text1 })
      }),
      fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: text2 })
      }),
      fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: text3 })
      })
    ]);
    
    const embeddings = await Promise.all(responses.map(r => r.json()));
    
    // Calculate cosine similarity
    function cosineSimilarity(a, b) {
      let dotProduct = 0;
      let normA = 0;
      let normB = 0;
      
      for (let i = 0; i < a.length; i++) {
        dotProduct += a[i] * b[i];
        normA += a[i] * a[i];
        normB += b[i] * b[i];
      }
      
      return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    const similarity12 = cosineSimilarity(embeddings[0].embedding, embeddings[1].embedding);
    const similarity13 = cosineSimilarity(embeddings[0].embedding, embeddings[2].embedding);
    
    console.log('✅ Semantic similarity results:');
    console.log(`   "${text1}" vs "${text2}": ${similarity12.toFixed(3)} (should be high)`);
    console.log(`   "${text1}" vs "${text3}": ${similarity13.toFixed(3)} (should be low)`);
    
    // Verify expected behavior
    if (similarity12 > 0.7 && similarity13 < 0.5) {
      console.log('✅ Semantic similarity working correctly!');
      return true;
    } else {
      console.log('⚠️  Semantic similarity results unexpected');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Semantic similarity test failed:', error);
    return false;
  }
}

/**
 * Test deduplication threshold
 */
async function testDeduplicationThreshold() {
  console.log('\n🧪 Testing deduplication threshold...');
  
  try {
    const memory1 = 'I have a cat named Whiskers who loves to play with yarn';
    const memory2 = 'My feline companion Whiskers enjoys playing with string';
    
    const responses = await Promise.all([
      fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: memory1 })
      }),
      fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: memory2 })
      })
    ]);
    
    const embeddings = await Promise.all(responses.map(r => r.json()));
    
    function cosineSimilarity(a, b) {
      let dotProduct = 0;
      let normA = 0;
      let normB = 0;
      
      for (let i = 0; i < a.length; i++) {
        dotProduct += a[i] * b[i];
        normA += a[i] * a[i];
        normB += b[i] * b[i];
      }
      
      return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }
    
    const similarity = cosineSimilarity(embeddings[0].embedding, embeddings[1].embedding);
    const threshold = 0.92;
    
    console.log(`✅ Deduplication test:`);
    console.log(`   Memory 1: "${memory1}"`);
    console.log(`   Memory 2: "${memory2}"`);
    console.log(`   Similarity: ${similarity.toFixed(3)}`);
    console.log(`   Threshold: ${threshold}`);
    console.log(`   Would be deduplicated: ${similarity >= threshold ? 'YES' : 'NO'}`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Deduplication threshold test failed:', error);
    return false;
  }
}

/**
 * Test memory recall relevance
 */
async function testMemoryRecall() {
  console.log('\n🧪 Testing memory recall relevance...');
  
  try {
    const memories = TEST_CONFIG.testMemories;
    const prompts = TEST_CONFIG.testPrompts;
    
    // Generate embeddings for all memories
    const memoryEmbeddings = await Promise.all(
      memories.map(async (memory) => {
        const response = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: `${memory.key}: ${memory.value}` })
        });
        const data = await response.json();
        return { ...memory, embedding: data.embedding };
      })
    );
    
    // Test each prompt
    for (const prompt of prompts) {
      console.log(`\n🔍 Testing prompt: "${prompt}"`);
      
      // Generate prompt embedding
      const promptResponse = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: prompt })
      });
      const promptData = await promptResponse.json();
      
      // Calculate similarities
      const similarities = memoryEmbeddings.map(memory => {
        function cosineSimilarity(a, b) {
          let dotProduct = 0;
          let normA = 0;
          let normB = 0;
          
          for (let i = 0; i < a.length; i++) {
            dotProduct += a[i] * b[i];
            normA += a[i] * a[i];
            normB += b[i] * b[i];
          }
          
          return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
        }
        
        const similarity = cosineSimilarity(promptData.embedding, memory.embedding);
        return { ...memory, similarity };
      });
      
      // Sort by similarity and show top results
      const sortedMemories = similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, 3);
      
      console.log('   Top relevant memories:');
      sortedMemories.forEach((memory, index) => {
        const relevanceIcon = memory.similarity >= 0.6 ? '🔥' : 
                             memory.similarity >= 0.4 ? '⭐' : 
                             memory.similarity >= 0.2 ? '💡' : '📝';
        console.log(`   ${index + 1}. ${relevanceIcon} ${memory.key}: ${memory.similarity.toFixed(3)}`);
      });
    }
    
    console.log('\n✅ Memory recall test completed!');
    return true;
    
  } catch (error) {
    console.error('❌ Memory recall test failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting semantic memory system tests...\n');
  
  const results = {
    embeddingService: await testEmbeddingService(),
    semanticSimilarity: await testSemanticSimilarity(),
    deduplicationThreshold: await testDeduplicationThreshold(),
    memoryRecall: await testMemoryRecall(),
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n${allPassed ? '🎉' : '⚠️'} Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎯 Semantic memory system is ready for use!');
    console.log('   • Semantic deduplication will prevent duplicate memories');
    console.log('   • Memory recall will provide contextually relevant memories to Amber');
    console.log('   • Embedding service is functioning correctly');
  } else {
    console.log('\n🔧 Please check the failed tests and ensure:');
    console.log('   • Embedding service is running on localhost:5005');
    console.log('   • Vite proxy is configured correctly');
    console.log('   • Database migration has been applied');
  }
}

// Run tests if this script is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  runAllTests();
} else {
  // Node.js environment
  console.log('This test script is designed to run in a browser environment.');
  console.log('Please open the browser console and run the test functions manually.');
}
