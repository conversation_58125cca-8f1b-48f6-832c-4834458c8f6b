/**
 * Button Styling Consistency Tests
 * Tests to verify that FormModal and JournalEntryForm both use AmberButtonPair with form variant
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { JournalEntryForm } from '@/components/forms/JournalEntryForm';
import { FormModal } from '@/components/ui/form-modal';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock the auth context
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user', email: '<EMAIL>' },
  }),
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Button Styling Consistency', () => {
  const mockFormData = {
    title: 'Test Entry',
    content: 'Test content',
    emotion: 'joyful' as const,
    moodScore: 7 as const,
  };

  describe('JournalEntryForm buttons', () => {
    it('has full-width buttons with correct styling', () => {
      render(
        <JournalEntryForm
          data={mockFormData}
          onChange={vi.fn()}
          onSubmit={vi.fn()}
          onCancel={vi.fn()}
        />
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      const saveButton = screen.getByRole('button', { name: /save entry/i });

      // Check that buttons have flex-1 class for full width
      expect(cancelButton).toHaveClass('flex-1');
      expect(saveButton).toHaveClass('flex-1');

      // Check padding
      expect(cancelButton).toHaveClass('px-4', 'py-2');
      expect(saveButton).toHaveClass('px-4', 'py-2');

      // Check border and background styling
      expect(cancelButton).toHaveClass('border', 'border-gray-300', 'bg-white');
      expect(saveButton).toHaveClass('bg-amber-500');
    });
  });

  describe('FormModal buttons', () => {
    it('has full-width buttons with matching styling', () => {
      render(
        <TestWrapper>
          <FormModal
            isOpen={true}
            onClose={vi.fn()}
            onSubmit={vi.fn()}
            title="Test Modal"
            submitText="Save"
            cancelText="Cancel"
          >
            <div>Test content</div>
          </FormModal>
        </TestWrapper>
      );

      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      const saveButton = screen.getByRole('button', { name: /save/i });

      // Check that buttons have flex-1 class for full width
      expect(cancelButton).toHaveClass('flex-1');
      expect(saveButton).toHaveClass('flex-1');

      // Check padding matches JournalEntryForm
      expect(cancelButton).toHaveClass('px-4', 'py-2');
      expect(saveButton).toHaveClass('px-4', 'py-2');

      // Check border and background styling matches
      expect(cancelButton).toHaveClass('border', 'border-gray-300', 'bg-white');
      expect(saveButton).toHaveClass('bg-amber-500');
    });

    it('shows loading state correctly', () => {
      render(
        <TestWrapper>
          <FormModal
            isOpen={true}
            onClose={vi.fn()}
            onSubmit={vi.fn()}
            title="Test Modal"
            submitText="Save"
            cancelText="Cancel"
            isLoading={true}
          >
            <div>Test content</div>
          </FormModal>
        </TestWrapper>
      );

      const saveButton = screen.getByRole('button', { name: /saving/i });
      
      // Check that loading state is displayed
      expect(saveButton).toBeInTheDocument();
      expect(saveButton).toBeDisabled();
      
      // Check for loading spinner
      const spinner = saveButton.querySelector('.animate-spin');
      expect(spinner).toBeInTheDocument();
    });
  });

  describe('Component reusability', () => {
    it('Both FormModal and JournalEntryForm use AmberButtonPair with form variant', () => {
      // Render JournalEntryForm
      const { unmount: unmountForm } = render(
        <JournalEntryForm
          data={mockFormData}
          onChange={vi.fn()}
          onSubmit={vi.fn()}
          onCancel={vi.fn()}
          testId="journal-form"
        />
      );

      const formCancelButton = screen.getByRole('button', { name: /cancel/i });
      const formSaveButton = screen.getByRole('button', { name: /save entry/i });

      // Verify form variant styling (custom buttons, not AmberButton components)
      expect(formCancelButton).toHaveClass('flex-1', 'px-4', 'py-2', 'border-gray-300');
      expect(formSaveButton).toHaveClass('flex-1', 'px-4', 'py-2', 'bg-amber-500');

      unmountForm();

      // Render FormModal
      render(
        <TestWrapper>
          <FormModal
            isOpen={true}
            onClose={vi.fn()}
            onSubmit={vi.fn()}
            title="Test Modal"
            submitText="Save Entry"
            cancelText="Cancel"
            testId="form-modal"
          >
            <div>Test content</div>
          </FormModal>
        </TestWrapper>
      );

      const modalCancelButton = screen.getByRole('button', { name: /cancel/i });
      const modalSaveButton = screen.getByRole('button', { name: /save entry/i });

      // Verify both have identical styling from form variant
      expect(modalCancelButton).toHaveClass('flex-1', 'px-4', 'py-2', 'border-gray-300');
      expect(modalSaveButton).toHaveClass('flex-1', 'px-4', 'py-2', 'bg-amber-500');
    });

    it('Both components use the same AmberButtonPair component', () => {
      // This test verifies that we're reusing components rather than duplicating code
      // Focus on the specific Cancel and Save/Update buttons

      const { unmount: unmountForm } = render(
        <JournalEntryForm
          data={mockFormData}
          onChange={vi.fn()}
          onSubmit={vi.fn()}
          onCancel={vi.fn()}
        />
      );

      const formCancelButton = screen.getByRole('button', { name: /cancel/i });
      const formSaveButton = screen.getByRole('button', { name: /save entry/i });

      unmountForm();

      render(
        <TestWrapper>
          <FormModal
            isOpen={true}
            onClose={vi.fn()}
            onSubmit={vi.fn()}
            title="Test Modal"
            submitText="Save Entry"
            cancelText="Cancel"
          >
            <div>Test content</div>
          </FormModal>
        </TestWrapper>
      );

      const modalCancelButton = screen.getByRole('button', { name: /cancel/i });
      const modalSaveButton = screen.getByRole('button', { name: /save entry/i });

      // Key styling classes should be identical since both use AmberButtonPair with form variant
      const keyClasses = ['flex-1', 'px-4', 'py-2', 'transition-colors', 'border-gray-300', 'bg-amber-500'];

      // Check cancel buttons have same classes
      keyClasses.forEach(className => {
        if (className === 'bg-amber-500') return; // Skip amber background for cancel button
        const formHasClass = formCancelButton.classList.contains(className);
        const modalHasClass = modalCancelButton.classList.contains(className);
        expect(formHasClass).toBe(modalHasClass);
      });

      // Check save buttons have same classes
      keyClasses.forEach(className => {
        if (className === 'border-gray-300') return; // Skip gray border for save button
        const formHasClass = formSaveButton.classList.contains(className);
        const modalHasClass = modalSaveButton.classList.contains(className);
        expect(formHasClass).toBe(modalHasClass);
      });
    });
  });
});
