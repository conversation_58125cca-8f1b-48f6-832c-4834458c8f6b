/**
 * Application Initialization Service
 * Centralized initialization with configuration validation and error handling
 */

import { getEnvironmentConfig } from '@/config/environment.config';
import { initializeSecureConfig } from '@/utils/secureConfig';
import { initializeConfigValidation } from '@/services/configValidation.service';
import { registerServiceWorker } from '@/utils/serviceWorker';
import { webVitalsService } from '@/services/webVitals.service';
import { errorTrackingService } from '@/services/errorTracking.service';
import { seoService } from '@/services/seo.service';
import { accessibilityService } from '@/services/accessibility.service';
import { handleError } from '@/utils/errorHandler';

/**
 * Initialization result interface
 */
export interface InitializationResult {
  success: boolean;
  canProceed: boolean;
  environment: string;
  errors: string[];
  warnings: string[];
  timestamp: string;
}

/**
 * Initialize application with comprehensive validation
 */
export const initializeApplication = async (): Promise<InitializationResult> => {
  const startTime = Date.now();
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    console.log('🚀 Initializing Amberglow application...');

    // 1. Initialize secure configuration
    console.log('🔒 Initializing secure configuration...');
    initializeSecureConfig();

    // 2. Validate configuration
    console.log('🔍 Validating configuration...');
    const validationResult = initializeConfigValidation();

    // Collect validation issues
    for (const issue of validationResult.issues) {
      if (issue.severity === 'error') {
        errors.push(issue.message);
      } else if (issue.severity === 'warning') {
        warnings.push(issue.message);
      }
    }

    // 3. Get environment configuration
    const env = getEnvironmentConfig();

    // 4. Initialize environment-specific features
    if (env.features.debugMode) {
      console.log('🔧 Debug mode enabled');
      // Enable additional debugging features
      window.__AMBERGLOW_DEBUG__ = {
        environment: env,
        validation: validationResult,
        timestamp: new Date().toISOString(),
      };
    }

    if (env.features.analyticsEnabled) {
      console.log('📊 Analytics enabled');
      // Initialize analytics (placeholder for future implementation)
    }

    // 4.5. Register service worker for caching
    if (env.isProduction || env.features.serviceWorkerEnabled) {
      console.log('🔧 Registering service worker...');
      try {
        const swResult = await registerServiceWorker();
        if (swResult.success) {
          console.log('✅ Service worker registered successfully');
        } else {
          console.warn('⚠️ Service worker registration failed:', swResult.message);
          warnings.push(`Service worker: ${swResult.message}`);
        }
      } catch (error) {
        console.warn('⚠️ Service worker registration error:', error);
        warnings.push('Service worker registration failed');
      }
    }

    // 4.6. Initialize performance monitoring
    console.log('📊 Initializing performance monitoring...');
    try {
      // Web Vitals monitoring is automatically initialized in the service constructor
      if (webVitalsService.isMonitoringEnabled()) {
        console.log('✅ Core Web Vitals monitoring enabled');
      }

      // Error tracking is automatically initialized in the service constructor
      console.log('✅ Error tracking initialized');

      // Add initialization breadcrumb
      errorTrackingService.addBreadcrumb({
        timestamp: Date.now(),
        category: 'navigation',
        message: 'Application initialization completed',
        level: 'info',
        data: { environment: env.environment },
      });
    } catch (error) {
      console.warn('⚠️ Performance monitoring initialization error:', error);
      warnings.push('Performance monitoring initialization failed');
    }

    // 4.7. Initialize SEO and accessibility
    console.log('🔍 Initializing SEO and accessibility...');
    try {
      // Initialize SEO meta tags and structured data
      seoService.initializeDefaultSEO();
      console.log('✅ SEO service initialized');

      // Accessibility service is automatically initialized in constructor
      console.log('✅ Accessibility service initialized');

      // Add accessibility breadcrumb
      errorTrackingService.addBreadcrumb({
        timestamp: Date.now(),
        category: 'navigation',
        message: 'SEO and accessibility services initialized',
        level: 'info',
      });
    } catch (error) {
      console.warn('⚠️ SEO/Accessibility initialization error:', error);
      warnings.push('SEO/Accessibility initialization failed');
    }

    // 5. Log initialization summary
    const duration = Date.now() - startTime;
    const success = validationResult.canProceed;

    if (success) {
      console.log(`✅ Application initialized successfully in ${duration}ms`);
      console.log(`🌍 Environment: ${env.environment}`);
      console.log(
        `🎯 Features: AI=${env.features.aiEnabled}, Analytics=${env.features.analyticsEnabled}, Debug=${env.features.debugMode}`
      );
    } else {
      console.error(`❌ Application initialization failed after ${duration}ms`);
    }

    return {
      success,
      canProceed: validationResult.canProceed,
      environment: env.environment,
      errors,
      warnings,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
    errors.push(errorMessage);

    console.error('💥 Critical initialization error:', error);

    // Log the error for monitoring
    await handleError(
      error instanceof Error ? error : new Error(errorMessage),
      {
        component: 'InitializationService',
        action: 'initialize_application',
        metadata: { duration: Date.now() - startTime },
      },
      { showToast: false } // Don't show toast during initialization
    );

    return {
      success: false,
      canProceed: false,
      environment: 'unknown',
      errors,
      warnings,
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Check if application is properly initialized
 */
export const checkInitializationStatus = (): boolean => {
  try {
    const env = getEnvironmentConfig();
    return !!(env.supabase.url && env.supabase.anonKey);
  } catch {
    return false;
  }
};

/**
 * Get initialization debug information
 */
export const getInitializationDebugInfo = () => {
  if (typeof window !== 'undefined' && window.__AMBERGLOW_DEBUG__) {
    return window.__AMBERGLOW_DEBUG__;
  }
  return null;
};

/**
 * Reinitialize application (useful for configuration changes)
 */
export const reinitializeApplication = async (): Promise<InitializationResult> => {
  console.log('🔄 Reinitializing application...');

  // Clear any cached configuration
  if (typeof window !== 'undefined') {
    delete window.__AMBERGLOW_DEBUG__;
  }

  return initializeApplication();
};

/**
 * Global type augmentation for debug info
 */
declare global {
  interface Window {
    __AMBERGLOW_DEBUG__?: {
      environment: any;
      validation: any;
      timestamp: string;
    };
  }
}
