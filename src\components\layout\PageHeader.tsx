/**
 * Page Header Component
 * Reusable page header component with consistent styling and layout
 * Consolidates page header patterns across the application
 */

import React from 'react';
import { AmberButton } from '@/components/ui/amber-button';
import { BreadcrumbButton } from '@/components/ui/navigation-button';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface BreadcrumbItem {
  label: string;
  to?: string;
  onClick?: () => void;
  isCurrent?: boolean;
}

interface ActionButton {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  icon?: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
}

interface PageHeaderProps extends BaseComponentProps {
  /** Page title */
  title: string;
  /** Page subtitle/description */
  subtitle?: string;
  /** Breadcrumb navigation items */
  breadcrumbs?: BreadcrumbItem[];
  /** Action buttons */
  actions?: ActionButton[];
  /** Additional content to render in the header */
  children?: React.ReactNode;
  /** Header layout variant */
  layout?: 'default' | 'centered' | 'split';
  /** Whether to add bottom border */
  bordered?: boolean;
  /** Background variant */
  background?: 'transparent' | 'white' | 'glass';
  /** Padding size */
  padding?: 'sm' | 'md' | 'lg';
}

/**
 * PageHeader component with consistent styling and layout options
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  breadcrumbs = [],
  actions = [],
  children,
  layout = 'default',
  bordered = true,
  background = 'transparent',
  padding = 'md',
  className,
  testId,
  ...props
}) => {
  // Background styles
  const backgroundStyles = {
    transparent: '',
    white: 'bg-white',
    glass: 'glass-effect bg-white/95 backdrop-blur-md',
  };

  // Padding styles
  const paddingStyles = {
    sm: 'px-4 py-3',
    md: 'px-4 py-6',
    lg: 'px-6 py-8',
  };

  // Layout styles
  const getLayoutStyles = () => {
    switch (layout) {
      case 'centered':
        return 'text-center';
      case 'split':
        return 'flex items-center justify-between';
      default:
        return '';
    }
  };

  return (
    <header
      className={cn(
        paddingStyles[padding],
        backgroundStyles[background],
        bordered && 'border-b border-gray-200',
        'transition-all duration-200',
        className
      )}
      data-testid={testId}
      {...props}
    >
      <div className="container mx-auto">
        {/* Breadcrumbs */}
        {breadcrumbs.length > 0 && (
          <nav className="mb-4" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-sm">
              {breadcrumbs.map((item, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <svg
                      className="w-4 h-4 mx-2 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  )}
                  <BreadcrumbButton
                    to={item.to}
                    onClick={item.onClick}
                    isCurrent={item.isCurrent}
                  >
                    {item.label}
                  </BreadcrumbButton>
                </li>
              ))}
            </ol>
          </nav>
        )}

        {/* Header Content */}
        <div className={getLayoutStyles()}>
          {layout === 'split' ? (
            <>
              {/* Title and Subtitle */}
              <div className="flex-1 min-w-0">
                <h1 className="text-3xl font-bold text-gradient mb-2">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-lg text-muted-foreground">
                    {subtitle}
                  </p>
                )}
              </div>

              {/* Actions */}
              {actions.length > 0 && (
                <div className="flex items-center gap-3 ml-6">
                  {actions.map((action, index) => (
                    <AmberButton
                      key={index}
                      variant={action.variant || 'primary'}
                      onClick={action.onClick}
                      icon={action.icon}
                      isLoading={action.isLoading}
                      disabled={action.disabled}
                    >
                      {action.label}
                    </AmberButton>
                  ))}
                </div>
              )}
            </>
          ) : (
            <>
              {/* Title and Subtitle */}
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-gradient mb-2">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-lg text-muted-foreground">
                    {subtitle}
                  </p>
                )}
              </div>

              {/* Actions */}
              {actions.length > 0 && (
                <div className={cn(
                  'flex gap-3 mb-6',
                  layout === 'centered' ? 'justify-center' : 'justify-start'
                )}>
                  {actions.map((action, index) => (
                    <AmberButton
                      key={index}
                      variant={action.variant || 'primary'}
                      onClick={action.onClick}
                      icon={action.icon}
                      isLoading={action.isLoading}
                      disabled={action.disabled}
                    >
                      {action.label}
                    </AmberButton>
                  ))}
                </div>
              )}
            </>
          )}

          {/* Additional Content */}
          {children && (
            <div className={cn(
              layout === 'split' && 'mt-4',
              layout === 'centered' && 'mt-6'
            )}>
              {children}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

/**
 * SimplePageHeader component for basic page headers
 */
interface SimplePageHeaderProps extends BaseComponentProps {
  /** Page title */
  title: string;
  /** Page subtitle */
  subtitle?: string;
  /** Back button handler */
  onBack?: () => void;
  /** Back button text */
  backText?: string;
}

export const SimplePageHeader: React.FC<SimplePageHeaderProps> = ({
  title,
  subtitle,
  onBack,
  backText = 'Back',
  className,
  testId,
  ...props
}) => {
  return (
    <div
      className={cn('mb-6', className)}
      data-testid={testId}
      {...props}
    >
      {onBack && (
        <button
          onClick={onBack}
          className="flex items-center gap-2 text-amber-600 hover:text-amber-700 mb-4 transition-colors duration-200"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          {backText}
        </button>
      )}
      
      <h1 className="text-3xl font-bold text-gradient mb-2">
        {title}
      </h1>
      
      {subtitle && (
        <p className="text-lg text-muted-foreground">
          {subtitle}
        </p>
      )}
    </div>
  );
};
