/**
 * Form Section Component
 * Reusable component for grouping related form fields with consistent styling
 */

import React from 'react';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface FormSectionProps extends BaseComponentProps {
  /** Section title */
  title?: string;
  /** Section description */
  description?: string;
  /** Section content */
  children: React.ReactNode;
  /** Layout variant */
  layout?: 'vertical' | 'horizontal' | 'grid';
  /** Number of columns for grid layout */
  columns?: 1 | 2 | 3 | 4;
  /** Gap between form fields */
  gap?: 'sm' | 'md' | 'lg';
  /** Whether to show a border around the section */
  bordered?: boolean;
  /** Whether to add padding to the section */
  padded?: boolean;
}

/**
 * FormSection component for organizing form fields
 */
export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  layout = 'vertical',
  columns = 2,
  gap = 'md',
  bordered = false,
  padded = false,
  className,
  testId,
  ...props
}) => {
  // Gap classes mapping
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4',
    lg: 'gap-6',
  };

  // Layout classes mapping
  const layoutClasses = {
    vertical: `flex flex-col ${gapClasses[gap]}`,
    horizontal: `flex flex-row ${gapClasses[gap]} items-end`,
    grid: `grid grid-cols-1 md:grid-cols-${columns} ${gapClasses[gap]}`,
  };

  const sectionClasses = cn(
    layoutClasses[layout],
    bordered && 'border border-amber-200 rounded-lg',
    padded && 'p-4',
    className
  );

  return (
    <div className={sectionClasses} data-testid={testId} {...props}>
      {(title || description) && (
        <div className={cn(
          'space-y-1',
          layout === 'grid' && 'md:col-span-full'
        )}>
          {title && (
            <h3 className="text-lg font-medium text-gray-900">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      )}
      
      {layout === 'grid' ? (
        // For grid layout, render children directly as they'll be positioned by grid
        children
      ) : (
        // For vertical/horizontal layouts, wrap each child if needed
        children
      )}
    </div>
  );
};

/**
 * FormFieldGroup component for grouping multiple fields in a single row
 */
interface FormFieldGroupProps extends BaseComponentProps {
  /** Group content */
  children: React.ReactNode;
  /** Whether fields should be equal width */
  equalWidth?: boolean;
  /** Gap between fields */
  gap?: 'sm' | 'md' | 'lg';
}

export const FormFieldGroup: React.FC<FormFieldGroupProps> = ({
  children,
  equalWidth = true,
  gap = 'md',
  className,
  testId,
  ...props
}) => {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-3',
    lg: 'gap-4',
  };

  return (
    <div 
      className={cn(
        'flex',
        gapClasses[gap],
        equalWidth && 'space-x-0 [&>*]:flex-1',
        className
      )}
      data-testid={testId}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * FormActions component for form submit/cancel buttons
 */
interface FormActionsProps extends BaseComponentProps {
  /** Actions content */
  children: React.ReactNode;
  /** Alignment of actions */
  align?: 'left' | 'center' | 'right' | 'between';
  /** Whether to add top border */
  bordered?: boolean;
  /** Whether to add top padding */
  padded?: boolean;
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  align = 'right',
  bordered = true,
  padded = true,
  className,
  testId,
  ...props
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div 
      className={cn(
        'flex gap-3',
        alignClasses[align],
        bordered && 'border-t border-gray-200',
        padded && 'pt-6 mt-6',
        className
      )}
      data-testid={testId}
      {...props}
    >
      {children}
    </div>
  );
};
