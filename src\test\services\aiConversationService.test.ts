/**
 * AI Conversation Service Tests
 * Unit tests for AI conversation generation
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { generateAIConversationResponse } from '@/services/aiConversationService';
import { AIConversationInput, ConversationMessage } from '@/types';

// Mock Google Generative AI
vi.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: vi.fn().mockImplementation(() => ({
    getGenerativeModel: vi.fn().mockReturnValue({
      generateContent: vi.fn(),
    }),
  })),
}));

// Mock AI config
vi.mock('@/config/ai.config', () => ({
  getAIConfig: vi.fn().mockReturnValue({
    apiKey: 'test-api-key',
    model: 'gemini-pro',
    maxRetries: 3,
    baseDelay: 1000,
  }),
}));

// Mock error handler
vi.mock('@/utils/errorHandler', () => ({
  handleApiError: vi.fn().mockReturnValue({
    message: 'Test error',
    code: 'TEST_ERROR',
  }),
}));

describe('AIConversationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variables
    process.env.VITE_GEMINI_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  const mockJournalEntry = {
    title: 'Test Entry',
    content: 'This is a test journal entry about my day.',
    emotion: 'happy',
    mood_score: 7,
  };

  const mockConversationHistory: ConversationMessage[] = [
    {
      id: 'msg-1',
      conversation_id: 'conv-1',
      sender_type: 'ai',
      message_content: 'How are you feeling about what you wrote?',
      message_type: 'reflection_question',
      ai_metadata: null,
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'msg-2',
      conversation_id: 'conv-1',
      sender_type: 'user',
      message_content: 'I feel good about it, but also a bit anxious.',
      message_type: 'text',
      ai_metadata: null,
      created_at: '2024-01-01T00:01:00Z',
    },
  ];

  const mockInput: AIConversationInput = {
    user_message: 'I feel good about it, but also a bit anxious.',
    conversation_history: mockConversationHistory,
    journal_entry: mockJournalEntry,
    initial_reflection: {
      summary: 'A positive day with some challenges',
      emotion: 'mixed',
      encouragement: 'You handled things well today',
      reflection_question: 'What made you feel most proud today?',
    },
  };

  describe('generateAIConversationResponse', () => {
    it('should generate a successful AI response', async () => {
      const mockGeminiResponse = {
        response: {
          text: () => JSON.stringify({
            message: 'That\'s completely understandable. Can you tell me more about what\'s making you feel anxious?',
            message_type: 'follow_up',
            metadata: {
              emotion: 'supportive',
              confidence: 0.9,
            },
          }),
        },
      };

      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockModel = {
        generateContent: vi.fn().mockResolvedValue(mockGeminiResponse),
      };
      const mockGenAI = {
        getGenerativeModel: vi.fn().mockReturnValue(mockModel),
      };
      
      vi.mocked(GoogleGenerativeAI).mockImplementation(() => mockGenAI as any);

      const result = await generateAIConversationResponse(mockInput);

      expect(result.success).toBe(true);
      expect(result.data?.message).toContain('anxious');
      expect(result.data?.message_type).toBe('follow_up');
      expect(result.data?.metadata.emotion).toBe('supportive');
      expect(result.data?.metadata.confidence).toBe(0.9);
      expect(mockModel.generateContent).toHaveBeenCalledWith(
        expect.stringContaining('You are Amber')
      );
    });

    it('should handle invalid JSON response from Gemini', async () => {
      const mockGeminiResponse = {
        response: {
          text: () => 'Invalid JSON response',
        },
      };

      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockModel = {
        generateContent: vi.fn().mockResolvedValue(mockGeminiResponse),
      };
      const mockGenAI = {
        getGenerativeModel: vi.fn().mockReturnValue(mockModel),
      };
      
      vi.mocked(GoogleGenerativeAI).mockImplementation(() => mockGenAI as any);

      const result = await generateAIConversationResponse(mockInput);

      // Should fall back to mock response
      expect(result.success).toBe(true);
      expect(result.data?.success).toBe(false); // AI response failed, but service returned fallback
      expect(result.data?.message).toBeTruthy();
      expect(result.data?.metadata.modelVersion).toBe('fallback');
    });

    it('should handle missing API key gracefully', async () => {
      // Remove API key
      process.env.VITE_GEMINI_API_KEY = '';
      
      // Mock getAIConfig to return no API key
      const { getAIConfig } = await import('@/config/ai.config');
      vi.mocked(getAIConfig).mockReturnValue({
        apiKey: '',
        model: 'gemini-pro',
        maxRetries: 3,
        baseDelay: 1000,
      });

      const result = await generateAIConversationResponse(mockInput);

      // Should fall back to mock response
      expect(result.success).toBe(true);
      expect(result.data?.metadata.modelVersion).toBe('fallback');
    });

    it('should include conversation context in prompt', async () => {
      const mockGeminiResponse = {
        response: {
          text: () => JSON.stringify({
            message: 'Based on our conversation, I understand you\'re feeling mixed emotions.',
            message_type: 'follow_up',
            metadata: { emotion: 'understanding', confidence: 0.8 },
          }),
        },
      };

      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockModel = {
        generateContent: vi.fn().mockResolvedValue(mockGeminiResponse),
      };
      const mockGenAI = {
        getGenerativeModel: vi.fn().mockReturnValue(mockModel),
      };
      
      vi.mocked(GoogleGenerativeAI).mockImplementation(() => mockGenAI as any);

      await generateAIConversationResponse(mockInput);

      const promptCall = mockModel.generateContent.mock.calls[0][0];
      
      // Check that prompt includes conversation history
      expect(promptCall).toContain('Previous conversation:');
      expect(promptCall).toContain('Amber: How are you feeling');
      expect(promptCall).toContain('User: I feel good about it');
      
      // Check that prompt includes journal context
      expect(promptCall).toContain('Original journal entry context:');
      expect(promptCall).toContain('Test Entry');
      expect(promptCall).toContain('This is a test journal entry');
      
      // Check that prompt includes initial reflection
      expect(promptCall).toContain('Your initial reflection:');
      expect(promptCall).toContain('A positive day with some challenges');
    });

    it('should validate required input fields', async () => {
      const invalidInput = {
        ...mockInput,
        user_message: '', // Empty message
      };

      const result = await generateAIConversationResponse(invalidInput);

      expect(result.success).toBe(true); // Still returns success with fallback
      expect(result.data?.metadata.modelVersion).toBe('fallback');
    });

    it('should handle network errors with retry', async () => {
      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockModel = {
        generateContent: vi.fn()
          .mockRejectedValueOnce(new Error('Network error'))
          .mockRejectedValueOnce(new Error('Network error'))
          .mockResolvedValueOnce({
            response: {
              text: () => JSON.stringify({
                message: 'Success after retry',
                message_type: 'follow_up',
                metadata: { emotion: 'supportive', confidence: 0.8 },
              }),
            },
          }),
      };
      const mockGenAI = {
        getGenerativeModel: vi.fn().mockReturnValue(mockModel),
      };
      
      vi.mocked(GoogleGenerativeAI).mockImplementation(() => mockGenAI as any);

      const result = await generateAIConversationResponse(mockInput);

      expect(result.success).toBe(true);
      expect(result.data?.message).toBe('Success after retry');
      expect(mockModel.generateContent).toHaveBeenCalledTimes(3); // 2 failures + 1 success
    });

    it('should limit conversation history length', async () => {
      // Create a long conversation history
      const longHistory: ConversationMessage[] = Array.from({ length: 20 }, (_, i) => ({
        id: `msg-${i}`,
        conversation_id: 'conv-1',
        sender_type: i % 2 === 0 ? 'user' : 'ai',
        message_content: `Message ${i}`,
        message_type: 'text',
        ai_metadata: null,
        created_at: new Date(Date.now() + i * 1000).toISOString(),
      }));

      const inputWithLongHistory = {
        ...mockInput,
        conversation_history: longHistory,
      };

      const mockGeminiResponse = {
        response: {
          text: () => JSON.stringify({
            message: 'Response with limited context',
            message_type: 'follow_up',
            metadata: { emotion: 'supportive', confidence: 0.8 },
          }),
        },
      };

      const { GoogleGenerativeAI } = await import('@google/generative-ai');
      const mockModel = {
        generateContent: vi.fn().mockResolvedValue(mockGeminiResponse),
      };
      const mockGenAI = {
        getGenerativeModel: vi.fn().mockReturnValue(mockModel),
      };
      
      vi.mocked(GoogleGenerativeAI).mockImplementation(() => mockGenAI as any);

      await generateAIConversationResponse(inputWithLongHistory);

      const promptCall = mockModel.generateContent.mock.calls[0][0];
      
      // Should not include all 20 messages (limited by maxContextMessages config)
      const messageMatches = promptCall.match(/Message \d+/g);
      expect(messageMatches?.length).toBeLessThan(20);
    });
  });
});
