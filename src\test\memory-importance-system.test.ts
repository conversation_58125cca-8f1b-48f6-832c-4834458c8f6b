/**
 * Memory Importance System Integration Tests
 * Tests for the complete memory importance scoring and filtering system
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { evaluateMemoryImportance } from '@/services/memoryImportanceService';
import { addUserMemory, getUserMemories } from '@/services/memoryPersistenceService';
import { UserMemory } from '@/types';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } }
      })
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          gte: vi.fn().mockReturnValue({
            order: vi.fn().mockReturnValue({
              limit: vi.fn().mockResolvedValue({
                data: [],
                error: null
              })
            })
          })
        })
      }),
      insert: vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: { id: 'test-id', importance: 5 },
            error: null
          })
        })
      })
    })
  }
}));

// Mock environment config
vi.mock('@/config/environment.config', () => ({
  getEnvironmentConfig: vi.fn().mockReturnValue({
    ai: {
      geminiApiKey: 'test-key',
    }
  })
}));

// Mock AI config
vi.mock('@/config/ai.config', () => ({
  getAIConfig: vi.fn().mockReturnValue({
    model: 'test-model',
    serviceType: 'local-llm'
  })
}));

// Mock Google Generative AI
vi.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: vi.fn().mockImplementation(() => ({
    getGenerativeModel: vi.fn().mockReturnValue({
      generateContent: vi.fn().mockResolvedValue({
        response: {
          text: vi.fn().mockReturnValue('7')
        }
      })
    })
  }))
}));

// Mock Local LLM Service
vi.mock('@/services/localLLMService', () => ({
  callLocalLLM: vi.fn().mockResolvedValue({
    content: '6'
  }),
  LocalLLMError: class extends Error {}
}));

describe('Memory Importance System', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Memory Importance Evaluation', () => {
    it('should evaluate high importance for emotional memories', async () => {
      const memory: UserMemory = {
        key: 'relationship_milestone',
        value: 'Got engaged to my partner after 5 years together',
        category: 'event'
      };

      const result = await evaluateMemoryImportance(memory);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.importance).toBeGreaterThanOrEqual(1);
      expect(result.data!.importance).toBeLessThanOrEqual(10);
    });

    it('should evaluate medium importance for goal-related memories', async () => {
      const memory: UserMemory = {
        key: 'fitness_goal',
        value: 'Started going to the gym 3 times a week',
        category: 'goal'
      };

      const result = await evaluateMemoryImportance(memory);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.importance).toBeGreaterThanOrEqual(1);
      expect(result.data!.importance).toBeLessThanOrEqual(10);
    });

    it('should use fallback calculation when AI services fail', async () => {
      // Mock AI services to fail
      vi.mocked(require('@google/generative-ai').GoogleGenerativeAI).mockImplementation(() => {
        throw new Error('API Error');
      });
      
      vi.mocked(require('@/services/localLLMService').callLocalLLM).mockRejectedValue(new Error('LLM Error'));

      const memory: UserMemory = {
        key: 'daily_routine',
        value: 'Had coffee this morning',
        category: 'fact'
      };

      const result = await evaluateMemoryImportance(memory);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data!.source).toBe('fallback');
      expect(result.data!.importance).toBeGreaterThanOrEqual(1);
      expect(result.data!.importance).toBeLessThanOrEqual(10);
    });

    it('should assign higher importance to identity-related memories', async () => {
      const memory: UserMemory = {
        key: 'core_identity',
        value: 'I am a software engineer who loves helping people',
        category: 'identity'
      };

      const result = await evaluateMemoryImportance(memory);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      // Identity memories should get high importance in fallback
      if (result.data!.source === 'fallback') {
        expect(result.data!.importance).toBeGreaterThanOrEqual(7);
      }
    });

    it('should assign lower importance to routine facts', async () => {
      const memory: UserMemory = {
        key: 'coffee_preference',
        value: 'Likes coffee',
        category: 'preference'
      };

      const result = await evaluateMemoryImportance(memory);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      // Preference memories should get medium-low importance in fallback
      if (result.data!.source === 'fallback') {
        expect(result.data!.importance).toBeLessThanOrEqual(6);
      }
    });
  });

  describe('Memory Filtering and Retrieval', () => {
    it('should filter out low importance memories by default', async () => {
      const result = await getUserMemories();
      
      expect(result.success).toBe(true);
      // Should use default filtering (importance >= 4)
    });

    it('should return all memories when override is specified', async () => {
      const result = await getUserMemories({ includeAllMemories: true });
      
      expect(result.success).toBe(true);
      // Should include all memories regardless of importance
    });

    it('should limit results to specified number', async () => {
      const result = await getUserMemories({ limit: 5 });
      
      expect(result.success).toBe(true);
      // Should limit to 5 memories
    });

    it('should order by importance when specified', async () => {
      const result = await getUserMemories({ orderByImportance: true });
      
      expect(result.success).toBe(true);
      // Should order by importance DESC, then updated_at DESC
    });
  });

  describe('Memory Archiving', () => {
    it('should skip saving memories with very low importance', async () => {
      const lowImportanceMemory: UserMemory = {
        key: 'trivial_fact',
        value: 'Had a sandwich',
        category: 'fact',
        importance: 2
      };

      const result = await addUserMemory(lowImportanceMemory);

      expect(result.success).toBe(true);
      expect(result.meta?.skipped).toBe(true);
      expect(result.meta?.reason).toBe('Low importance score');
    });

    it('should save memories with acceptable importance', async () => {
      const acceptableMemory: UserMemory = {
        key: 'important_fact',
        value: 'Started a new job',
        category: 'event',
        importance: 6
      };

      const result = await addUserMemory(acceptableMemory);

      expect(result.success).toBe(true);
      expect(result.meta?.skipped).toBeUndefined();
    });
  });

  describe('Backward Compatibility', () => {
    it('should handle memories without importance scores', async () => {
      const memoryWithoutImportance: UserMemory = {
        key: 'legacy_memory',
        value: 'This memory has no importance score',
        category: 'fact'
      };

      const result = await addUserMemory(memoryWithoutImportance);

      expect(result.success).toBe(true);
      // Should automatically evaluate importance
    });

    it('should default to importance 5 for existing memories', async () => {
      // This test verifies that the database migration worked correctly
      // and existing memories have the default importance of 5
      const result = await getUserMemories({ includeAllMemories: true });
      
      expect(result.success).toBe(true);
      // All memories should have importance defined (default 5)
    });
  });
});
