/**
 * Environment Configuration System
 * Centralized environment variable management with validation and type safety
 */

import {
  Environment,
  EnvironmentConfig,
  EnvironmentValidationResult,
  EnvironmentVariableDefinition,
  ConfigurationError,
} from '@/types/environment';

/**
 * Environment variable definitions with validation rules
 */
const ENVIRONMENT_DEFINITIONS: EnvironmentVariableDefinition[] = [
  // Required Supabase Configuration
  {
    key: 'VITE_SUPABASE_URL',
    required: true,
    type: 'url',
    description: 'Supabase project URL',
    validator: (value: string) => value.includes('supabase.co'),
  },
  {
    key: 'VITE_SUPABASE_ANON_KEY',
    required: true,
    type: 'string',
    description: 'Supabase anonymous key',
    validator: (value: string) => value.length > 100, // JWT tokens are typically long
  },

  // AI Configuration (Legacy - kept for backward compatibility)
  {
    key: 'VITE_GEMINI_API_KEY',
    required: false, // Optional - now using local LLM by default
    type: 'string',
    description: 'Google Gemini API key for AI features (legacy)',
    validator: (value: string) => value.startsWith('AIza'),
  },

  // Local LLM Configuration
  {
    key: 'VITE_LOCAL_LLM_ENDPOINT',
    required: false,
    type: 'url',
    description: 'Local LLM endpoint URL (defaults to http://localhost:11434/v1/chat/completions)',
    validator: (value: string) => value.includes('localhost') || value.includes('127.0.0.1'),
  },

  // Application Configuration
  {
    key: 'VITE_APP_NAME',
    required: false,
    type: 'string',
    description: 'Application name',
    defaultValue: 'Amberglow',
  },
  {
    key: 'VITE_APP_VERSION',
    required: false,
    type: 'string',
    description: 'Application version',
    defaultValue: '1.0.0',
  },
  {
    key: 'VITE_APP_ENVIRONMENT',
    required: false,
    type: 'string',
    description: 'Application environment',
    validator: (value: string) => ['development', 'staging', 'production'].includes(value),
  },

  // Feature Flags
  {
    key: 'VITE_ENABLE_AI_FEATURES',
    required: false,
    type: 'boolean',
    description: 'Enable AI-powered features',
    defaultValue: 'true',
    transformer: (value: string) => value.toLowerCase() === 'true',
  },
  {
    key: 'VITE_ENABLE_ANALYTICS',
    required: false,
    type: 'boolean',
    description: 'Enable analytics tracking',
    defaultValue: 'false',
    transformer: (value: string) => value.toLowerCase() === 'true',
  },
  {
    key: 'VITE_ENABLE_DEBUG_MODE',
    required: false,
    type: 'boolean',
    description: 'Enable debug mode',
    transformer: (value: string) => value.toLowerCase() === 'true',
  },
  {
    key: 'VITE_ENABLE_SERVICE_WORKER',
    required: false,
    type: 'boolean',
    description: 'Enable service worker for caching and offline support',
    defaultValue: 'false',
    transformer: (value: string) => value.toLowerCase() === 'true',
  },

  // API Configuration
  {
    key: 'VITE_API_TIMEOUT',
    required: false,
    type: 'number',
    description: 'API request timeout in milliseconds',
    defaultValue: '30000',
    transformer: (value: string) => parseInt(value, 10),
    validator: (value: string) => !isNaN(parseInt(value, 10)) && parseInt(value, 10) > 0,
  },
  {
    key: 'VITE_API_RETRY_ATTEMPTS',
    required: false,
    type: 'number',
    description: 'Number of API retry attempts',
    defaultValue: '3',
    transformer: (value: string) => parseInt(value, 10),
    validator: (value: string) => !isNaN(parseInt(value, 10)) && parseInt(value, 10) >= 0,
  },

  // Security Configuration
  {
    key: 'VITE_ENABLE_HTTPS_ONLY',
    required: false,
    type: 'boolean',
    description: 'Enforce HTTPS-only connections',
    defaultValue: 'true',
    transformer: (value: string) => value.toLowerCase() === 'true',
  },
  {
    key: 'VITE_ENABLE_STRICT_CSP',
    required: false,
    type: 'boolean',
    description: 'Enable strict Content Security Policy',
    defaultValue: 'false',
    transformer: (value: string) => value.toLowerCase() === 'true',
  },
];

/**
 * Get current environment from Vite
 */
const getCurrentEnvironment = (): Environment => {
  // Check explicit environment variable first
  const explicitEnv = import.meta.env.VITE_APP_ENVIRONMENT as Environment;
  if (explicitEnv && ['development', 'staging', 'production'].includes(explicitEnv)) {
    return explicitEnv;
  }

  // Fall back to Vite's built-in environment detection
  if (import.meta.env.PROD) return 'production';
  if (import.meta.env.MODE === 'staging') return 'staging';
  return 'development';
};

/**
 * Validate a single environment variable
 */
const validateEnvironmentVariable = (
  definition: EnvironmentVariableDefinition,
  value: string | undefined
): ConfigurationError[] => {
  const errors: ConfigurationError[] = [];

  // Check if required variable is missing
  if (definition.required && (!value || value.trim() === '')) {
    errors.push({
      type: 'MISSING_REQUIRED_VAR',
      variable: definition.key,
      message: `Required environment variable ${definition.key} is missing`,
      suggestion: `Set ${definition.key} in your .env file. ${definition.description}`,
    });
    return errors;
  }

  // Skip validation if value is empty and not required
  if (!value || value.trim() === '') {
    return errors;
  }

  // Type-specific validation
  switch (definition.type) {
    case 'url':
      try {
        new URL(value);
      } catch {
        errors.push({
          type: 'INVALID_FORMAT',
          variable: definition.key,
          message: `${definition.key} must be a valid URL`,
          suggestion: 'Ensure the URL includes protocol (https://) and is properly formatted',
        });
      }
      break;

    case 'number':
      if (isNaN(Number(value))) {
        errors.push({
          type: 'INVALID_FORMAT',
          variable: definition.key,
          message: `${definition.key} must be a valid number`,
          suggestion: 'Provide a numeric value',
        });
      }
      break;

    case 'boolean':
      if (!['true', 'false'].includes(value.toLowerCase())) {
        errors.push({
          type: 'INVALID_FORMAT',
          variable: definition.key,
          message: `${definition.key} must be 'true' or 'false'`,
          suggestion: 'Use lowercase "true" or "false"',
        });
      }
      break;
  }

  // Custom validator
  if (definition.validator && !definition.validator(value)) {
    errors.push({
      type: 'VALIDATION_FAILED',
      variable: definition.key,
      message: `${definition.key} failed custom validation`,
      suggestion: definition.description,
    });
  }

  return errors;
};

/**
 * Validate all environment variables
 */
export const validateEnvironment = (): EnvironmentValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const missingRequired: string[] = [];
  const missingOptional: string[] = [];

  for (const definition of ENVIRONMENT_DEFINITIONS) {
    const value = import.meta.env[definition.key];
    const validationErrors = validateEnvironmentVariable(definition, value);

    for (const error of validationErrors) {
      if (error.type === 'MISSING_REQUIRED_VAR') {
        missingRequired.push(definition.key);
        errors.push(`${error.message}. ${error.suggestion}`);
      } else {
        errors.push(`${error.message}. ${error.suggestion}`);
      }
    }

    // Track missing optional variables
    if (!definition.required && (!value || value.trim() === '')) {
      missingOptional.push(definition.key);
      if (definition.defaultValue) {
        warnings.push(`Using default value for ${definition.key}: ${definition.defaultValue}`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    missingRequired,
    missingOptional,
  };
};

/**
 * Get environment variable value with fallback
 */
const getEnvironmentValue = (definition: EnvironmentVariableDefinition): any => {
  const value = import.meta.env[definition.key] || definition.defaultValue || '';

  if (definition.transformer) {
    return definition.transformer(value);
  }

  return value;
};

/**
 * Create environment configuration object
 */
export const createEnvironmentConfig = (): EnvironmentConfig => {
  const environment = getCurrentEnvironment();

  return {
    // Environment info
    environment,
    isDevelopment: environment === 'development',
    isProduction: environment === 'production',
    isStaging: environment === 'staging',

    // Application info
    app: {
      name: getEnvironmentValue(ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_APP_NAME')!),
      version: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_APP_VERSION')!
      ),
    },

    // Supabase configuration
    supabase: {
      url: getEnvironmentValue(ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_SUPABASE_URL')!),
      anonKey: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_SUPABASE_ANON_KEY')!
      ),
    },

    // AI configuration
    ai: {
      geminiApiKey: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_GEMINI_API_KEY')!
      ),
      localLLMEndpoint: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_LOCAL_LLM_ENDPOINT')!
      ) || 'http://localhost:11434/v1/chat/completions',
      enabled: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_AI_FEATURES')!
      ),
      timeout: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_API_TIMEOUT')!
      ),
      retryAttempts: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_API_RETRY_ATTEMPTS')!
      ),
    },

    // Feature flags
    features: {
      aiEnabled: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_AI_FEATURES')!
      ),
      analyticsEnabled: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_ANALYTICS')!
      ),
      debugMode:
        getEnvironmentValue(
          ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_DEBUG_MODE')!
        ) || false,
      serviceWorkerEnabled:
        getEnvironmentValue(
          ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_SERVICE_WORKER')!
        ) || false,
    },

    // Security settings
    security: {
      httpsOnly: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_HTTPS_ONLY')!
      ),
      strictCSP: getEnvironmentValue(
        ENVIRONMENT_DEFINITIONS.find(d => d.key === 'VITE_ENABLE_STRICT_CSP')!
      ),
    },
  };
};

/**
 * Initialize and validate environment configuration
 */
let environmentConfig: EnvironmentConfig | null = null;
let validationResult: EnvironmentValidationResult | null = null;

/**
 * Get validated environment configuration
 * Performs validation on first access and caches the result
 */
export const getEnvironmentConfig = (): EnvironmentConfig => {
  if (!environmentConfig) {
    // Validate environment first
    validationResult = validateEnvironment();

    // Log validation results
    if (validationResult.warnings.length > 0) {
      console.warn('Environment configuration warnings:', validationResult.warnings);
    }

    if (!validationResult.isValid) {
      console.error('Environment configuration errors:', validationResult.errors);

      // In development, log errors but don't throw to prevent app crash
      if (import.meta.env.DEV) {
        console.error(
          `Environment configuration is invalid:\n${validationResult.errors.join('\n')}`
        );
        console.warn('Continuing with potentially invalid configuration...');
      }
    }

    // Create configuration even if validation failed (for graceful degradation)
    environmentConfig = createEnvironmentConfig();

    // Log successful initialization
    console.log(`🌍 Environment initialized: ${environmentConfig.environment}`);
    if (environmentConfig.features.debugMode) {
      console.log('🔧 Environment config:', environmentConfig);
    }
  }

  return environmentConfig;
};

/**
 * Get environment validation result
 */
export const getEnvironmentValidation = (): EnvironmentValidationResult => {
  if (!validationResult) {
    validationResult = validateEnvironment();
  }
  return validationResult;
};

/**
 * Check if a specific feature is enabled
 */
export const isFeatureEnabled = (feature: keyof EnvironmentConfig['features']): boolean => {
  const config = getEnvironmentConfig();
  return config.features[feature];
};

/**
 * Get environment-specific configuration value
 */
export const getConfigValue = <T>(path: string, defaultValue?: T): T => {
  const config = getEnvironmentConfig();
  const keys = path.split('.');
  let value: any = config;

  for (const key of keys) {
    value = value?.[key];
    if (value === undefined) {
      return defaultValue as T;
    }
  }

  return value as T;
};

/**
 * Export the main configuration instance
 */
export const env = getEnvironmentConfig();
