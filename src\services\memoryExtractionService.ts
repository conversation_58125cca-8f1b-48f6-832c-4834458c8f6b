/**
 * Memory Extraction Service
 * Service for extracting important long-term facts about users from journal entries and conversations
 */

import {
  MemoryExtractionInput,
  MemoryExtractionResponse,
  UserMemory,
  LLMMessage,
  AIServiceConfig,
} from '@/types';
import { callLocalLLM, LocalLLMError } from './localLLMService';
import { handleApiError } from '@/utils/errorHandler';
import { addUserMemory, updateMemoryExtractionStats, getUserMemories } from './memoryPersistenceService';
import { getRelevantMemories, RelevanceRetrievalOptions } from './memoryRelevanceService';
import { generateEmbedding, calculateCosineSimilarity } from './embeddingService';

// Performance optimization: Cache recent extractions to avoid duplicate processing
const extractionCache = new Map<string, { result: MemoryExtractionResponse; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Semantic deduplication configuration
const SEMANTIC_SIMILARITY_THRESHOLD = 0.92; // Threshold for considering memories as duplicates
const SEMANTIC_DEDUPLICATION_ENABLED = true; // Feature flag for semantic deduplication

// Performance optimization: Debounce rapid extraction requests
const pendingExtractions = new Map<string, Promise<MemoryExtractionResponse>>();

/**
 * Generate a cache key for content to avoid duplicate processing
 */
const generateCacheKey = (content: string, sourceType: string): string => {
  // Create a simple hash of the content for caching
  let hash = 0;
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return `${sourceType}_${Math.abs(hash)}`;
};

/**
 * Clean up expired cache entries
 */
const cleanupCache = () => {
  const now = Date.now();
  for (const [key, entry] of extractionCache.entries()) {
    if (now - entry.timestamp > CACHE_DURATION) {
      extractionCache.delete(key);
    }
  }
};

/**
 * Check if a new memory is semantically similar to existing memories
 * @param newMemory The memory to check for duplicates
 * @returns Object with isDuplicate flag and similar memory if found
 */
const checkSemanticDuplication = async (
  newMemory: UserMemory
): Promise<{ isDuplicate: boolean; similarMemory?: UserMemory; similarity?: number }> => {
  if (!SEMANTIC_DEDUPLICATION_ENABLED) {
    return { isDuplicate: false };
  }

  try {
    console.log(`🧠 [DEDUP] Checking semantic duplication for memory: "${newMemory.key}"`);

    // Generate embedding for the new memory
    const newMemoryText = `${newMemory.key}: ${newMemory.value}`;
    const embeddingResult = await generateEmbedding(newMemoryText);

    if (!embeddingResult.success) {
      console.warn('🧠 [DEDUP] Failed to generate embedding for new memory, skipping deduplication');
      return { isDuplicate: false };
    }

    // Get existing memories with embeddings
    const existingMemoriesResult = await getUserMemories({
      includeAllMemories: true,
      minImportance: 1, // Include all memories for deduplication
    });

    if (!existingMemoriesResult.success || !existingMemoriesResult.data) {
      console.log('🧠 [DEDUP] No existing memories found');
      return { isDuplicate: false };
    }

    // Check similarity with existing memories
    let maxSimilarity = 0;
    let mostSimilarMemory: UserMemory | undefined;

    for (const existingMemory of existingMemoriesResult.data) {
      // Skip if no embedding available for existing memory
      if (!existingMemory.embedding) {
        continue;
      }

      try {
        // Parse existing memory embedding
        let existingEmbedding: number[];
        if (typeof existingMemory.embedding === 'string') {
          existingEmbedding = JSON.parse(existingMemory.embedding);
        } else if (Array.isArray(existingMemory.embedding)) {
          existingEmbedding = existingMemory.embedding;
        } else {
          continue;
        }

        // Calculate cosine similarity
        const similarity = calculateCosineSimilarity(embeddingResult.data, existingEmbedding);

        console.log(`🧠 [DEDUP] Similarity with "${existingMemory.key}": ${similarity.toFixed(3)}`);

        if (similarity > maxSimilarity) {
          maxSimilarity = similarity;
          mostSimilarMemory = existingMemory;
        }

        // Early exit if we find a very similar memory
        if (similarity >= SEMANTIC_SIMILARITY_THRESHOLD) {
          console.log(`🧠 [DEDUP] Found duplicate memory (similarity: ${similarity.toFixed(3)}): "${existingMemory.key}"`);
          return {
            isDuplicate: true,
            similarMemory: existingMemory,
            similarity,
          };
        }
      } catch (error) {
        console.warn(`🧠 [DEDUP] Error comparing with memory "${existingMemory.key}":`, error);
        continue;
      }
    }

    console.log(`🧠 [DEDUP] No duplicate found. Max similarity: ${maxSimilarity.toFixed(3)}`);
    return {
      isDuplicate: false,
      similarMemory: mostSimilarMemory,
      similarity: maxSimilarity,
    };

  } catch (error) {
    console.error('🧠 [DEDUP] Error in semantic deduplication check:', error);
    return { isDuplicate: false };
  }
};

/**
 * Memory extraction prompt template
 */
const MEMORY_EXTRACTION_PROMPT = `You are a memory extraction system that identifies important long-term facts about users from either journal entries or conversations with an AI assistant named Amber.

**OBJECTIVE:**
Extract only information that Amber should remember to provide more personalized, emotionally aware conversations in future interactions. Focus on persistent facts, ongoing emotions, significant events, personal goals, preferences, and identity markers that would be relevant across multiple conversations.

**OUTPUT FORMAT:**
Return a JSON array of memory objects with this exact structure:
[
  {
    "key": "string",        // Concise 1-3 word label (e.g., "pet", "work_stress", "language_goal", "home_location")
    "value": "string",      // Specific memory content (e.g., "Enzo the cat", "feeling overwhelmed about quarterly reviews")
    "category": "string"    // Must be one of: "fact", "emotion", "event", "goal", "preference", "identity"
  }
]

**CATEGORY DEFINITIONS:**
- **fact**: Objective information about the user (pets, family, job, location, possessions)
- **emotion**: Current emotional states or patterns (stress, anxiety, excitement, depression)
- **event**: Significant upcoming or recent events (presentations, trips, milestones, life changes)
- **goal**: Things the user wants to achieve or work toward (learning languages, fitness, career)
- **preference**: Likes, dislikes, habits, or personal choices (hobbies, food, activities)
- **identity**: Core aspects of who they are (profession, relationships, values, beliefs)

**EXTRACTION GUIDELINES:**
- Only extract information that would be useful for Amber to remember across multiple conversations
- Avoid temporary or trivial details (what they ate today, current weather, one-time events)
- Be specific but concise in the "value" field
- Use descriptive but brief "key" labels that Amber could easily reference
- If no long-term memory is worth saving, return an empty array: []

**EXAMPLES:**

*Example 1 – Journal Entry:*
Input: "I've been feeling overwhelmed at work lately. Enzo hasn't been eating much either — I hope he's okay. I really want to get back into Mandarin practice this week."

Output:
[
  { "key": "work_stress", "value": "feeling overwhelmed at work lately", "category": "emotion" },
  { "key": "pet", "value": "Enzo the cat", "category": "fact" },
  { "key": "language_goal", "value": "wants to resume Mandarin practice", "category": "goal" }
]

*Example 2 – Chat with Amber:*
Input:
User: "I've been super stressed lately."
Amber: "I'm here for you. Do you know why you've been feeling that way?"
User: "Probably because of the upcoming presentation and the pressure to impress the execs."

Output:
[
  { "key": "work_stress", "value": "stressed about upcoming executive presentation", "category": "emotion" },
  { "key": "work_event", "value": "important presentation to executives coming up", "category": "event" }
]

**RESPONSE:**
Return only the JSON array of memory objects. Do not include any explanatory text or markdown formatting.`;

/**
 * Validate memory extraction response
 */
const validateMemoryResponse = (response: any): UserMemory[] => {
  if (!Array.isArray(response)) {
    throw new LocalLLMError('invalid_response', 'Response must be an array of memory objects');
  }

  const validCategories = ['fact', 'emotion', 'event', 'goal', 'preference', 'identity'];
  
  return response.map((memory: any, index: number) => {
    if (!memory || typeof memory !== 'object') {
      throw new LocalLLMError('invalid_response', `Memory at index ${index} is not a valid object`);
    }

    const { key, value, category } = memory;

    if (!key || typeof key !== 'string' || key.trim().length === 0) {
      throw new LocalLLMError('invalid_response', `Memory at index ${index} has invalid or missing key`);
    }

    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new LocalLLMError('invalid_response', `Memory at index ${index} has invalid or missing value`);
    }

    if (!category || !validCategories.includes(category)) {
      throw new LocalLLMError('invalid_response', `Memory at index ${index} has invalid category. Must be one of: ${validCategories.join(', ')}`);
    }

    return {
      key: key.trim(),
      value: value.trim(),
      category: category as UserMemory['category'],
    };
  });
};

/**
 * Extract memories from journal entry or conversation content with performance optimizations
 */
export const extractMemories = async (
  input: MemoryExtractionInput,
  config?: Partial<AIServiceConfig>
): Promise<MemoryExtractionResponse> => {
  const startTime = Date.now();

  // Performance optimization: Generate cache key
  const cacheKey = generateCacheKey(input.content, input.sourceType);

  // Check cache first
  cleanupCache();
  const cachedResult = extractionCache.get(cacheKey);
  if (cachedResult) {
    console.log('🧠 Using cached memory extraction result');
    return cachedResult.result;
  }

  // Check if extraction is already in progress for this content
  const pendingExtraction = pendingExtractions.get(cacheKey);
  if (pendingExtraction) {
    console.log('🧠 Memory extraction already in progress, waiting for result');
    return pendingExtraction;
  }

  // Create new extraction promise
  const extractionPromise = performExtraction(input, config, startTime);
  pendingExtractions.set(cacheKey, extractionPromise);

  try {
    const result = await extractionPromise;

    // Cache the result
    extractionCache.set(cacheKey, {
      result,
      timestamp: Date.now(),
    });

    return result;
  } finally {
    // Clean up pending extraction
    pendingExtractions.delete(cacheKey);
  }
};

/**
 * Perform the actual memory extraction
 */
const performExtraction = async (
  input: MemoryExtractionInput,
  config?: Partial<AIServiceConfig>,
  startTime: number = Date.now()
): Promise<MemoryExtractionResponse> => {
  try {
    const systemMessage: LLMMessage = {
      role: 'system',
      content: MEMORY_EXTRACTION_PROMPT,
    };

    const userMessage: LLMMessage = {
      role: 'user',
      content: `Source: ${input.sourceType}
Content:
${input.content}`,
    };

    console.log('🧠 Extracting memories from content:', {
      sourceType: input.sourceType,
      contentLength: input.content.length,
    });

    const response = await callLocalLLM([systemMessage, userMessage], config);

    if (!response || typeof response !== 'string') {
      throw new LocalLLMError('invalid_response', 'Empty or invalid response from local LLM');
    }

    // Clean up the response (remove markdown code blocks if present)
    const cleanedResponse = response
      .replace(/```json\s*/gi, '')
      .replace(/```\s*/g, '')
      .replace(/^json\s*/gi, '')
      .trim();

    console.log('🔍 Attempting to parse memory extraction response:', cleanedResponse.substring(0, 200));

    let parsed: any;
    try {
      parsed = JSON.parse(cleanedResponse);
    } catch (parseError) {
      console.error('❌ Failed to parse memory extraction response:', cleanedResponse);
      throw new LocalLLMError('invalid_response', 'Invalid JSON response from local LLM');
    }

    const memories = validateMemoryResponse(parsed);
    const processingTime = Date.now() - startTime;

    console.log('✅ Successfully extracted memories:', {
      count: memories.length,
      processingTime,
      memories: memories.map(m => ({ key: m.key, category: m.category })),
    });

    return {
      memories,
      success: true,
      processingTime,
    };

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ Memory extraction failed:', error);

    if (error instanceof LocalLLMError) {
      return {
        memories: [],
        success: false,
        error: error.message,
        processingTime,
      };
    }

    return {
      memories: [],
      success: false,
      error: `Memory extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      processingTime,
    };
  }
};

/**
 * Extract memories from a journal entry and automatically save them
 */
export const extractMemoriesFromJournalEntry = async (
  journalEntry: {
    title: string;
    content: string;
    emotion: string;
    moodScore: number;
  },
  config?: Partial<AIServiceConfig>
): Promise<MemoryExtractionResponse> => {
  const startTime = Date.now();
  const content = `Title: ${journalEntry.title}\n\nContent: ${journalEntry.content}`;

  try {
    // Extract memories from the journal entry
    const result = await extractMemories(
      {
        sourceType: 'journal_entry',
        content,
      },
      config
    );

    // If successful, save each memory and update stats
    if (result.success && result.memories.length > 0) {
      console.log(`🧠 Extracted ${result.memories.length} memories from journal entry`);

      // Save each memory to the database with semantic deduplication
      let savedCount = 0;
      let duplicateCount = 0;

      for (const memory of result.memories) {
        try {
          // Check for semantic duplicates before saving
          const duplicationCheck = await checkSemanticDuplication(memory);

          if (duplicationCheck.isDuplicate && duplicationCheck.similarMemory) {
            console.log(`🧠 [DEDUP] Skipping duplicate memory "${memory.key}" (similar to "${duplicationCheck.similarMemory.key}", similarity: ${duplicationCheck.similarity?.toFixed(3)})`);
            duplicateCount++;
            continue;
          }

          // Save the memory if it's not a duplicate
          await addUserMemory(memory, 'journal_entry');
          savedCount++;

          if (duplicationCheck.similarity && duplicationCheck.similarity > 0.7) {
            console.log(`🧠 [DEDUP] Saved memory "${memory.key}" (max similarity: ${duplicationCheck.similarity.toFixed(3)})`);
          }
        } catch (error) {
          console.warn('Failed to save memory:', error);
        }
      }

      console.log(`🧠 [DEDUP] Memory extraction summary: ${savedCount} saved, ${duplicateCount} duplicates skipped`);

      // Update the result to reflect actual saved memories
      result.memories = result.memories.slice(0, savedCount);

      // Find relevant existing memories based on the journal content
      try {
        const relevanceOptions: RelevanceRetrievalOptions = {
          minImportance: 3, // Lower threshold to include more memories
          minRelevance: 0.15, // Lower threshold for context relevance
          relevanceWeight: 0.6,
          importanceWeight: 0.4,
          limit: 10,
        };
        
        const relevantResult = await getRelevantMemories(content, relevanceOptions);
        if (relevantResult.success && relevantResult.data && relevantResult.data.length > 0) {
          console.log(`🧠 Found ${relevantResult.data.length} relevant existing memories for this journal entry`);
          
          // Log the top relevant memories for debugging
          const topMemories = relevantResult.data.slice(0, 3);
          console.log('🧠 Top relevant memories:', topMemories.map(m => 
            `${m.key}: ${m.value} (relevance: ${(m.relevance * 100).toFixed(0)}%, importance: ${m.importance}/10)`
          ));
        }
      } catch (relevanceError) {
        console.warn('🧠 Failed to find relevant memories (non-critical):', relevanceError);
      }

      // Update extraction stats
      await updateMemoryExtractionStats(
        'journal_entry',
        1,
        1,
        result.memories.length,
        Date.now() - startTime
      );
    } else if (!result.success) {
      console.warn('Memory extraction failed:', result.error);
    }

    return result;
  } catch (error) {
    console.error('Error in memory extraction:', error);

    // Update stats for failed extraction
    await updateMemoryExtractionStats('journal_entry', 1, 0, 0, Date.now() - startTime);

    return {
      success: false,
      memories: [],
      error: error instanceof Error ? error.message : 'Unknown error',
      processingTime: Date.now() - startTime,
    };
  }
};

/**
 * Extract memories from a conversation between user and AI and automatically save them
 */
export const extractMemoriesFromConversation = async (
  conversation: {
    messages: Array<{
      sender_type: 'user' | 'ai';
      message_content: string;
    }>;
    journalContext?: {
      title: string;
      content: string;
      emotion: string;
      moodScore: number;
    };
  },
  config?: Partial<AIServiceConfig>
): Promise<MemoryExtractionResponse> => {
  const startTime = Date.now();

  // Format conversation for memory extraction
  const userMessages = conversation.messages
    .filter((msg) => msg.sender_type === 'user')
    .map((msg) => msg.message_content);

  // If there are no user messages, there's nothing to extract
  if (userMessages.length === 0) {
    return {
      success: false,
      memories: [],
      error: 'No user messages found in conversation',
      processingTime: 0,
    };
  }

  // Combine user messages with journal context if available
  let content = userMessages.join('\n\n');
  if (conversation.journalContext) {
    content = `Journal Entry: ${conversation.journalContext.title}\n${conversation.journalContext.content}\n\nConversation: ${content}`;
  }

  try {
    // Extract memories from the conversation
    const result = await extractMemories(
      {
        sourceType: 'conversation',
        content,
      },
      config
    );

    // If successful, save each memory and update stats
    if (result.success && result.memories.length > 0) {
      console.log(`🧠 Extracted ${result.memories.length} memories from conversation`);

      // Save each memory to the database with semantic deduplication
      let savedCount = 0;
      let duplicateCount = 0;

      for (const memory of result.memories) {
        try {
          // Check for semantic duplicates before saving
          const duplicationCheck = await checkSemanticDuplication(memory);

          if (duplicationCheck.isDuplicate && duplicationCheck.similarMemory) {
            console.log(`🧠 [DEDUP] Skipping duplicate memory "${memory.key}" (similar to "${duplicationCheck.similarMemory.key}", similarity: ${duplicationCheck.similarity?.toFixed(3)})`);
            duplicateCount++;
            continue;
          }

          // Save the memory if it's not a duplicate
          await addUserMemory(memory, 'conversation');
          savedCount++;

          if (duplicationCheck.similarity && duplicationCheck.similarity > 0.7) {
            console.log(`🧠 [DEDUP] Saved memory "${memory.key}" (max similarity: ${duplicationCheck.similarity.toFixed(3)})`);
          }
        } catch (error) {
          console.warn('Failed to save memory:', error);
        }
      }

      console.log(`🧠 [DEDUP] Conversation memory summary: ${savedCount} saved, ${duplicateCount} duplicates skipped`);

      // Update the result to reflect actual saved memories
      result.memories = result.memories.slice(0, savedCount);
      
      // Find relevant existing memories based on the conversation content
      try {
        const relevanceOptions: RelevanceRetrievalOptions = {
          minImportance: 3, // Lower threshold to include more memories
          minRelevance: 0.15, // Lower threshold for context relevance
          relevanceWeight: 0.7, // Higher weight on relevance for conversations
          importanceWeight: 0.3,
          limit: 10,
        };
        
        const relevantResult = await getRelevantMemories(content, relevanceOptions);
        if (relevantResult.success && relevantResult.data && relevantResult.data.length > 0) {
          console.log(`🧠 Found ${relevantResult.data.length} relevant existing memories for this conversation`);
          
          // Log the top relevant memories for debugging
          const topMemories = relevantResult.data.slice(0, 3);
          console.log('🧠 Top relevant memories:', topMemories.map(m => 
            `${m.key}: ${m.value} (relevance: ${(m.relevance * 100).toFixed(0)}%, importance: ${m.importance}/10)`
          ));
        }
      } catch (relevanceError) {
        console.warn('🧠 Failed to find relevant memories (non-critical):', relevanceError);
      }

      // Update extraction stats
      await updateMemoryExtractionStats(
        'conversation',
        1,
        1,
        result.memories.length,
        Date.now() - startTime
      );
    }

    return result;
  } catch (error) {
    console.error('Error in memory extraction:', error);
    
    // Update statistics for failed extractions
    try {
      await updateMemoryExtractionStats(
        'conversation',
        1, // total extractions
        0, // successful extractions
        0, // memories extracted
        Date.now() - startTime // processing time
      );
    } catch (statsError) {
      console.warn('🧠 Failed to update extraction statistics:', statsError);
    }
    
    return {
      success: false,
      memories: [],
      error: error instanceof Error ? error.message : 'Unknown error',
      processingTime: Date.now() - startTime,
    };
  }
};
