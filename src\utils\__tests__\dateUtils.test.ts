/**
 * Date Utils Tests
 * Test suite for date utility functions to ensure proper timezone handling
 */

import {
  utcTimestampToLocalDateString,
  parseLocalDateString,
  formatDateForDisplay,
  getCurrentLocalDateString,
  getCurrentLocalDateForDisplay,
} from '../dateUtils';

describe('dateUtils', () => {
  describe('utcTimestampToLocalDateString', () => {
    it('should convert UTC timestamp to local date string', () => {
      // Test with a known UTC timestamp
      const utcTimestamp = '2025-07-12T03:00:00.000Z'; // 3 AM UTC
      const result = utcTimestampToLocalDateString(utcTimestamp);
      
      // The result should be a date string in YYYY-MM-DD format
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}$/);
      
      // For most timezones, this should be July 11 or July 12
      expect(result).toMatch(/^2025-07-(11|12)$/);
    });

    it('should handle different UTC timestamps correctly', () => {
      const timestamps = [
        '2025-07-11T23:59:59.999Z',
        '2025-07-12T00:00:00.000Z',
        '2025-07-12T12:00:00.000Z',
      ];

      timestamps.forEach(timestamp => {
        const result = utcTimestampToLocalDateString(timestamp);
        expect(result).toMatch(/^\d{4}-\d{2}-\d{2}$/);
      });
    });
  });

  describe('parseLocalDateString', () => {
    it('should parse date string to Date object correctly', () => {
      const dateString = '2025-07-12';
      const result = parseLocalDateString(dateString);
      
      expect(result).toBeInstanceOf(Date);
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(6); // July is month 6 (0-indexed)
      expect(result.getDate()).toBe(12);
    });

    it('should handle different date strings', () => {
      const testCases = [
        { input: '2025-01-01', year: 2025, month: 0, date: 1 },
        { input: '2025-12-31', year: 2025, month: 11, date: 31 },
        { input: '2024-02-29', year: 2024, month: 1, date: 29 }, // Leap year
      ];

      testCases.forEach(({ input, year, month, date }) => {
        const result = parseLocalDateString(input);
        expect(result.getFullYear()).toBe(year);
        expect(result.getMonth()).toBe(month);
        expect(result.getDate()).toBe(date);
      });
    });
  });

  describe('formatDateForDisplay', () => {
    it('should format date for full display', () => {
      const dateString = '2025-07-12';
      const result = formatDateForDisplay(dateString, true);
      
      expect(result).toBe('Saturday, July 12, 2025');
    });

    it('should format date for short display', () => {
      const dateString = '2025-07-12';
      const result = formatDateForDisplay(dateString, false);
      
      expect(result).toBe('Jul 12, 2025');
    });

    it('should default to full date format', () => {
      const dateString = '2025-07-12';
      const result = formatDateForDisplay(dateString);
      
      expect(result).toBe('Saturday, July 12, 2025');
    });
  });

  describe('getCurrentLocalDateString', () => {
    it('should return current date in YYYY-MM-DD format', () => {
      const result = getCurrentLocalDateString();
      
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}$/);
      
      // Should be today's date
      const today = new Date();
      const expectedYear = today.getFullYear();
      const expectedMonth = String(today.getMonth() + 1).padStart(2, '0');
      const expectedDate = String(today.getDate()).padStart(2, '0');
      const expected = `${expectedYear}-${expectedMonth}-${expectedDate}`;
      
      expect(result).toBe(expected);
    });
  });

  describe('getCurrentLocalDateForDisplay', () => {
    it('should return current date formatted for display', () => {
      const result = getCurrentLocalDateForDisplay();
      
      // Should match the pattern for full date display
      expect(result).toMatch(/^\w+, \w+ \d{1,2}, \d{4}$/);
      
      // Should be today's date
      const today = new Date();
      const expected = today.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      
      expect(result).toBe(expected);
    });
  });

  describe('timezone consistency', () => {
    it('should maintain consistency between write page and saved entry dates', () => {
      // Simulate the scenario where a user creates an entry
      const currentDisplayDate = getCurrentLocalDateForDisplay();
      
      // Simulate saving to database (which would use UTC timestamp)
      const now = new Date();
      const utcTimestamp = now.toISOString();
      
      // Simulate retrieving and formatting for display
      const savedDateString = utcTimestampToLocalDateString(utcTimestamp);
      const savedDisplayDate = formatDateForDisplay(savedDateString, true);
      
      // The dates should match
      expect(savedDisplayDate).toBe(currentDisplayDate);
    });
  });
});
