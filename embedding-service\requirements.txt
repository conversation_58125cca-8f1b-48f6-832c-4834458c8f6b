# AmberGlow Embedding Service Dependencies
# Python 3.8+ required

# Core Flask framework
Flask==3.0.0

# CORS support for cross-origin requests from React frontend
flask-cors==4.0.0

# Sentence transformers for semantic embeddings
sentence-transformers==2.2.2

# Additional dependencies (automatically installed with sentence-transformers)
# torch>=1.11.0
# transformers>=4.21.0
# numpy>=1.17.0
# scikit-learn>=0.24.1
# scipy>=1.7.0
# huggingface-hub>=0.10.0

# Optional: For better performance and GPU support
# torch-audio  # Uncomment if you have GPU and want audio processing
# torch-vision # Uncomment if you have GPU and want vision processing

# Development dependencies (optional)
# pytest==7.4.0
# pytest-flask==1.2.0
# black==23.7.0
# flake8==6.0.0
