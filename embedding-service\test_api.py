#!/usr/bin/env python3
"""
Test script for AmberGlow Embedding Service API

This script tests the embedding service endpoints to ensure they work correctly.
Run this after starting the service to verify functionality.
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# Service configuration
BASE_URL = "http://localhost:5005"
HEALTH_ENDPOINT = f"{BASE_URL}/health"
EMBED_ENDPOINT = f"{BASE_URL}/embed"

def test_health_endpoint() -> bool:
    """Test the health check endpoint."""
    print("🔍 Testing health endpoint...")
    
    try:
        response = requests.get(HEALTH_ENDPOINT, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed")
            print(f"   Status: {data.get('status')}")
            print(f"   Model: {data.get('model')}")
            print(f"   Model Status: {data.get('model_status')}")
            print(f"   Embedding Dimension: {data.get('embedding_dimension')}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to service. Is it running on localhost:5005?")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_embed_endpoint() -> bool:
    """Test the embedding generation endpoint."""
    print("\n📡 Testing embed endpoint...")
    
    test_cases = [
        {
            "name": "Simple text",
            "text": "I had a wonderful day at the park.",
            "should_succeed": True
        },
        {
            "name": "Journal entry",
            "text": "Today I felt really anxious about my upcoming presentation. I spent most of the morning preparing slides and practicing my speech. Despite the nerves, I'm excited about sharing my ideas with the team.",
            "should_succeed": True
        },
        {
            "name": "Memory text",
            "text": "User loves hiking in the mountains and finds it very peaceful and meditative.",
            "should_succeed": True
        },
        {
            "name": "Empty text",
            "text": "",
            "should_succeed": False
        },
        {
            "name": "Whitespace only",
            "text": "   \n\t   ",
            "should_succeed": False
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n  Test {i}: {test_case['name']}")
        
        try:
            payload = {"text": test_case["text"]}
            response = requests.post(
                EMBED_ENDPOINT,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if test_case["should_succeed"]:
                if response.status_code == 200:
                    data = response.json()
                    embedding = data.get("embedding")
                    
                    if embedding and isinstance(embedding, list) and len(embedding) == 384:
                        print(f"    ✅ Success - Generated {len(embedding)}-dimensional embedding")
                        print(f"    📊 Sample values: {embedding[:3]}...")
                        
                        # Verify normalization (should be close to 1.0)
                        import math
                        magnitude = math.sqrt(sum(x*x for x in embedding))
                        print(f"    📏 Vector magnitude: {magnitude:.6f} (should be ~1.0)")
                        
                        if abs(magnitude - 1.0) > 0.01:
                            print(f"    ⚠️  Warning: Vector not properly normalized")
                    else:
                        print(f"    ❌ Invalid embedding format")
                        all_passed = False
                else:
                    print(f"    ❌ Failed with status {response.status_code}: {response.text}")
                    all_passed = False
            else:
                if response.status_code == 400:
                    error_data = response.json()
                    print(f"    ✅ Correctly rejected - {error_data.get('error')}")
                else:
                    print(f"    ❌ Should have failed but got status {response.status_code}")
                    all_passed = False
                    
        except Exception as e:
            print(f"    ❌ Test error: {e}")
            all_passed = False
    
    return all_passed

def test_invalid_requests() -> bool:
    """Test invalid request handling."""
    print("\n🚫 Testing invalid request handling...")
    
    test_cases = [
        {
            "name": "No JSON body",
            "data": None,
            "content_type": "text/plain"
        },
        {
            "name": "Missing text field",
            "data": {"message": "hello"},
            "content_type": "application/json"
        },
        {
            "name": "Non-string text field",
            "data": {"text": 123},
            "content_type": "application/json"
        },
        {
            "name": "Very long text",
            "data": {"text": "x" * 20000},
            "content_type": "application/json"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n  Test {i}: {test_case['name']}")
        
        try:
            headers = {"Content-Type": test_case["content_type"]}
            
            if test_case["data"] is None:
                response = requests.post(EMBED_ENDPOINT, headers=headers, timeout=10)
            else:
                response = requests.post(
                    EMBED_ENDPOINT,
                    json=test_case["data"],
                    headers=headers,
                    timeout=10
                )
            
            if response.status_code == 400:
                error_data = response.json()
                print(f"    ✅ Correctly rejected - {error_data.get('error')}")
            else:
                print(f"    ❌ Should have failed but got status {response.status_code}")
                all_passed = False
                
        except Exception as e:
            print(f"    ❌ Test error: {e}")
            all_passed = False
    
    return all_passed

def test_performance() -> bool:
    """Test basic performance metrics."""
    print("\n⚡ Testing performance...")
    
    test_text = "This is a sample journal entry to test the performance of the embedding service."
    
    try:
        # Warm up
        requests.post(EMBED_ENDPOINT, json={"text": test_text}, timeout=30)
        
        # Time multiple requests
        times = []
        for i in range(5):
            start_time = time.time()
            response = requests.post(EMBED_ENDPOINT, json={"text": test_text}, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                times.append(end_time - start_time)
            else:
                print(f"    ❌ Request {i+1} failed")
                return False
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"    ✅ Performance metrics:")
        print(f"       Average: {avg_time*1000:.1f}ms")
        print(f"       Min: {min_time*1000:.1f}ms")
        print(f"       Max: {max_time*1000:.1f}ms")
        
        if avg_time > 1.0:  # More than 1 second is concerning
            print(f"    ⚠️  Warning: Average response time is high")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Performance test error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 AmberGlow Embedding Service API Tests")
    print("=" * 50)
    
    # Check if service is running
    if not test_health_endpoint():
        print("\n❌ Service is not running or not healthy. Please start the service first.")
        print("   Run: python start.py")
        sys.exit(1)
    
    # Run all tests
    tests = [
        ("Embedding Generation", test_embed_endpoint),
        ("Invalid Request Handling", test_invalid_requests),
        ("Performance", test_performance)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "="*60)
    print("📋 Test Summary")
    print("="*60)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! The embedding service is working correctly.")
        print("🔗 You can now integrate it with the AmberGlow frontend.")
    else:
        print("\n⚠️  Some tests failed. Please check the service implementation.")
    
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    main()
