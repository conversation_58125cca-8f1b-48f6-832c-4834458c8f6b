/**
 * Accessibility Service
 * Manages WCAG compliance, keyboard navigation, and accessibility features
 */

import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * Accessibility preferences
 */
export interface AccessibilityPreferences {
  reducedMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
}

/**
 * Focus trap configuration
 */
interface FocusTrapConfig {
  element: HTMLElement;
  initialFocus?: HTMLElement;
  returnFocus?: HTMLElement;
}

/**
 * Accessibility service for WCAG compliance
 */
class AccessibilityService {
  private preferences: AccessibilityPreferences;
  private focusTraps: Map<string, FocusTrapConfig> = new Map();
  private announcements: HTMLElement | null = null;

  constructor() {
    this.preferences = this.loadPreferences();
    this.initialize();
  }

  /**
   * Initialize accessibility features
   */
  private initialize(): void {
    console.log('[A11y] Initializing accessibility service...');

    // Create live region for announcements
    this.createLiveRegion();

    // Apply user preferences
    this.applyPreferences();

    // Set up keyboard navigation
    this.setupKeyboardNavigation();

    // Monitor for accessibility violations in development
    if (getEnvironmentConfig().isDevelopment) {
      this.setupAccessibilityMonitoring();
    }

    console.log('[A11y] Accessibility service initialized');
  }

  /**
   * Create ARIA live region for screen reader announcements
   */
  private createLiveRegion(): void {
    this.announcements = document.createElement('div');
    this.announcements.setAttribute('aria-live', 'polite');
    this.announcements.setAttribute('aria-atomic', 'true');
    this.announcements.setAttribute('aria-relevant', 'additions text');
    this.announcements.className = 'sr-only';
    this.announcements.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `;
    document.body.appendChild(this.announcements);
  }

  /**
   * Announce message to screen readers
   */
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.announcements) return;

    this.announcements.setAttribute('aria-live', priority);
    this.announcements.textContent = message;

    // Clear after announcement
    setTimeout(() => {
      if (this.announcements) {
        this.announcements.textContent = '';
      }
    }, 1000);
  }

  /**
   * Load accessibility preferences from localStorage
   */
  private loadPreferences(): AccessibilityPreferences {
    const stored = localStorage.getItem('accessibility-preferences');
    const defaults: AccessibilityPreferences = {
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      highContrast: window.matchMedia('(prefers-contrast: high)').matches,
      largeText: false,
      screenReader: this.detectScreenReader(),
      keyboardNavigation: false,
    };

    return stored ? { ...defaults, ...JSON.parse(stored) } : defaults;
  }

  /**
   * Save accessibility preferences
   */
  private savePreferences(): void {
    localStorage.setItem('accessibility-preferences', JSON.stringify(this.preferences));
  }

  /**
   * Apply accessibility preferences to the document
   */
  private applyPreferences(): void {
    const root = document.documentElement;

    // Reduced motion
    if (this.preferences.reducedMotion) {
      root.style.setProperty('--animation-duration', '0.01ms');
      root.style.setProperty('--transition-duration', '0.01ms');
      root.classList.add('reduce-motion');
    }

    // High contrast
    if (this.preferences.highContrast) {
      root.classList.add('high-contrast');
    }

    // Large text
    if (this.preferences.largeText) {
      root.classList.add('large-text');
    }

    // Keyboard navigation indicators
    if (this.preferences.keyboardNavigation) {
      root.classList.add('keyboard-navigation');
    }
  }

  /**
   * Detect screen reader usage
   */
  private detectScreenReader(): boolean {
    // Check for common screen reader indicators
    return !!(
      navigator.userAgent.includes('NVDA') ||
      navigator.userAgent.includes('JAWS') ||
      navigator.userAgent.includes('VoiceOver') ||
      window.speechSynthesis ||
      'speechSynthesis' in window
    );
  }

  /**
   * Set up keyboard navigation
   */
  private setupKeyboardNavigation(): void {
    // Track keyboard usage
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Tab') {
        this.preferences.keyboardNavigation = true;
        document.documentElement.classList.add('keyboard-navigation');
      }
    });

    // Remove keyboard navigation class on mouse use
    document.addEventListener('mousedown', () => {
      document.documentElement.classList.remove('keyboard-navigation');
    });

    // Skip links for keyboard navigation
    this.createSkipLinks();
  }

  /**
   * Create skip links for keyboard navigation
   */
  private createSkipLinks(): void {
    const skipLinks = document.createElement('div');
    skipLinks.className = 'skip-links';
    skipLinks.innerHTML = `
      <a href="#main-content" class="skip-link">Skip to main content</a>
      <a href="#navigation" class="skip-link">Skip to navigation</a>
    `;
    
    // Style skip links
    const style = document.createElement('style');
    style.textContent = `
      .skip-links {
        position: absolute;
        top: -40px;
        left: 6px;
        z-index: 1000;
      }
      .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: bold;
        z-index: 1001;
      }
      .skip-link:focus {
        top: 6px;
      }
    `;
    document.head.appendChild(style);
    document.body.insertBefore(skipLinks, document.body.firstChild);
  }

  /**
   * Create focus trap for modals and dialogs
   */
  public createFocusTrap(id: string, element: HTMLElement, options: Partial<FocusTrapConfig> = {}): void {
    const config: FocusTrapConfig = {
      element,
      initialFocus: options.initialFocus,
      returnFocus: options.returnFocus || document.activeElement as HTMLElement,
    };

    this.focusTraps.set(id, config);

    // Focus initial element or first focusable element
    const initialFocus = config.initialFocus || this.getFirstFocusableElement(element);
    if (initialFocus) {
      initialFocus.focus();
    }

    // Trap focus within element
    element.addEventListener('keydown', this.handleFocusTrap.bind(this, id));
  }

  /**
   * Remove focus trap
   */
  public removeFocusTrap(id: string): void {
    const config = this.focusTraps.get(id);
    if (config) {
      // Return focus to previous element
      if (config.returnFocus && document.contains(config.returnFocus)) {
        config.returnFocus.focus();
      }

      // Remove event listener
      config.element.removeEventListener('keydown', this.handleFocusTrap.bind(this, id));
      this.focusTraps.delete(id);
    }
  }

  /**
   * Handle focus trap keyboard events
   */
  private handleFocusTrap(id: string, event: KeyboardEvent): void {
    if (event.key !== 'Tab') return;

    const config = this.focusTraps.get(id);
    if (!config) return;

    const focusableElements = this.getFocusableElements(config.element);
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      // Shift + Tab
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // Tab
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }

  /**
   * Get focusable elements within a container
   */
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ];

    return Array.from(
      container.querySelectorAll(focusableSelectors.join(', '))
    ) as HTMLElement[];
  }

  /**
   * Get first focusable element
   */
  private getFirstFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    return focusableElements.length > 0 ? focusableElements[0] : null;
  }

  /**
   * Set up accessibility monitoring in development
   */
  private setupAccessibilityMonitoring(): void {
    // Monitor for missing alt text
    const images = document.querySelectorAll('img:not([alt])');
    if (images.length > 0) {
      console.warn('[A11y] Images without alt text found:', images);
    }

    // Monitor for missing form labels
    const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
    inputs.forEach(input => {
      const label = document.querySelector(`label[for="${input.id}"]`);
      if (!label && input.type !== 'hidden') {
        console.warn('[A11y] Input without label found:', input);
      }
    });
  }

  /**
   * Update accessibility preference
   */
  public updatePreference<K extends keyof AccessibilityPreferences>(
    key: K,
    value: AccessibilityPreferences[K]
  ): void {
    this.preferences[key] = value;
    this.savePreferences();
    this.applyPreferences();
    
    this.announce(`${key} ${value ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get current preferences
   */
  public getPreferences(): AccessibilityPreferences {
    return { ...this.preferences };
  }

  /**
   * Check if element is visible to screen readers
   */
  public isVisibleToScreenReaders(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    return !(
      style.display === 'none' ||
      style.visibility === 'hidden' ||
      element.hasAttribute('aria-hidden') ||
      element.offsetParent === null
    );
  }

  /**
   * Ensure element has accessible name
   */
  public ensureAccessibleName(element: HTMLElement, fallbackText: string): void {
    if (
      !element.getAttribute('aria-label') &&
      !element.getAttribute('aria-labelledby') &&
      !element.textContent?.trim()
    ) {
      element.setAttribute('aria-label', fallbackText);
    }
  }
}

// Export singleton instance
export const accessibilityService = new AccessibilityService();

// Export utility functions
export const announce = (message: string, priority?: 'polite' | 'assertive') =>
  accessibilityService.announce(message, priority);

export const createFocusTrap = (id: string, element: HTMLElement, options?: Partial<FocusTrapConfig>) =>
  accessibilityService.createFocusTrap(id, element, options);

export const removeFocusTrap = (id: string) =>
  accessibilityService.removeFocusTrap(id);
