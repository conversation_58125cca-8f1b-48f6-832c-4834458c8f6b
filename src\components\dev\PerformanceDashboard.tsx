/**
 * Performance Dashboard Component
 * Development-only component for monitoring application performance
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  usePerformanceDashboard,
  useWebVitals,
  usePerformanceProvider,
} from '@/hooks/usePerformanceMonitoring';
import { getEnvironmentConfig } from '@/config/environment.config';
import { Activity, AlertTriangle, CheckCircle, XCircle, BarChart3, Zap } from 'lucide-react';

/**
 * Performance score indicator
 */
const PerformanceScore = ({ score }: { score: number }) => {
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="w-4 h-4" />;
    if (score >= 70) return <AlertTriangle className="w-4 h-4" />;
    return <XCircle className="w-4 h-4" />;
  };

  return (
    <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${getScoreColor(score)}`}>
      {getScoreIcon(score)}
      <span className="font-semibold">{score}/100</span>
    </div>
  );
};

/**
 * Metric card component
 */
const MetricCard = ({
  title,
  value,
  unit,
  icon: Icon,
  trend,
}: {
  title: string;
  value: number;
  unit: string;
  icon: React.ComponentType<any>;
  trend?: 'up' | 'down' | 'stable';
}) => {
  const getTrendColor = (trend?: string) => {
    switch (trend) {
      case 'up':
        return 'text-red-500';
      case 'down':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <Card className="p-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">
            {value.toFixed(1)}
            <span className="text-sm font-normal ml-1">{unit}</span>
          </p>
        </div>
        <div className="flex flex-col items-end gap-1">
          <Icon className="w-5 h-5 text-muted-foreground" />
          {trend && (
            <span className={`text-xs ${getTrendColor(trend)}`}>
              {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
            </span>
          )}
        </div>
      </div>
    </Card>
  );
};

/**
 * Web vitals component
 */
const WebVitalsCard = () => {
  const vitals = useWebVitals();

  const getVitalScore = (value: number | undefined, thresholds: [number, number]) => {
    if (!value) return 'secondary';
    if (value <= thresholds[0]) return 'default';
    if (value <= thresholds[1]) return 'secondary';
    return 'destructive';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5" />
          Web Vitals
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm">First Contentful Paint</span>
          <Badge variant={getVitalScore(vitals.fcp, [1800, 3000])}>
            {vitals.fcp ? `${vitals.fcp.toFixed(0)}ms` : 'N/A'}
          </Badge>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm">Largest Contentful Paint</span>
          <Badge variant={getVitalScore(vitals.lcp, [2500, 4000])}>
            {vitals.lcp ? `${vitals.lcp.toFixed(0)}ms` : 'N/A'}
          </Badge>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm">Cumulative Layout Shift</span>
          <Badge variant={getVitalScore(vitals.cls, [0.1, 0.25])}>
            {vitals.cls ? vitals.cls.toFixed(3) : 'N/A'}
          </Badge>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm">First Input Delay</span>
          <Badge variant={getVitalScore(vitals.fid, [100, 300])}>
            {vitals.fid ? `${vitals.fid.toFixed(0)}ms` : 'N/A'}
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
};

/**
 * Performance alerts component
 */
const AlertsCard = ({
  alerts,
  onDismiss,
}: {
  alerts: any[];
  onDismiss: (timestamp: number) => void;
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5" />
          Performance Alerts ({alerts.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {alerts.length === 0 ? (
          <p className="text-sm text-muted-foreground">No performance alerts</p>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {alerts.slice(0, 5).map(alert => (
              <div
                key={alert.timestamp}
                className={`p-2 rounded border-l-4 ${
                  alert.type === 'error'
                    ? 'border-red-500 bg-red-50'
                    : 'border-yellow-500 bg-yellow-50'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium">{alert.message}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onDismiss(alert.timestamp)}
                    className="h-6 w-6 p-0"
                  >
                    ×
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * Main performance dashboard component
 */
export const PerformanceDashboard = () => {
  const [isVisible, setIsVisible] = useState(false);
  const dashboard = usePerformanceDashboard();
  const { isEnabled } = usePerformanceProvider();
  const env = getEnvironmentConfig();

  // Only show in development with debug mode
  if (!env.isDevelopment || !env.features.debugMode || !isEnabled) {
    return null;
  }

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
        >
          <Activity className="w-4 h-4 mr-2" />
          Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-y-auto bg-white border rounded-lg shadow-xl">
      <div className="p-4 border-b bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            <h3 className="font-semibold">Performance Monitor</h3>
          </div>
          <div className="flex items-center gap-2">
            <PerformanceScore score={dashboard.overallScore} />
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsVisible(false)}
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-2">
          <MetricCard
            title="Avg Render"
            value={dashboard.component.renderCount}
            unit="renders"
            icon={BarChart3}
          />
          <MetricCard
            title="API Response"
            value={dashboard.api.metrics.averageResponseTime}
            unit="ms"
            icon={Activity}
          />
          <MetricCard
            title="Cache Hit Rate"
            value={dashboard.cache.hitRate * 100}
            unit="%"
            icon={CheckCircle}
          />
          <MetricCard
            title="Memory Usage"
            value={dashboard.memory.used}
            unit="MB"
            icon={AlertTriangle}
          />
        </div>

        {/* Web Vitals */}
        <WebVitalsCard />

        {/* Performance Alerts */}
        <AlertsCard alerts={dashboard.alerts.alerts} onDismiss={dashboard.alerts.dismissAlert} />

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              dashboard.component.measureRender(() => {
                console.log('Manual performance measurement triggered');
              });
            }}
            className="flex-1"
          >
            Measure
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // Clear performance data
              console.log('Performance data cleared');
            }}
            className="flex-1"
          >
            Clear
          </Button>
        </div>
      </div>
    </div>
  );
};
