/**
 * Form Field Component
 * Reusable form field component that consolidates Label + Input/Textarea + Error patterns
 * with consistent amber/orange styling and accessibility
 */

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface FormFieldProps extends BaseComponentProps {
  /** Field label text */
  label: string;
  /** Field type - determines which input component to render */
  type?: 'text' | 'email' | 'password' | 'textarea' | 'number';
  /** Input placeholder text */
  placeholder?: string;
  /** Current field value */
  value: string | number;
  /** Change handler */
  onChange: (value: string | number) => void;
  /** Whether the field is required */
  required?: boolean;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Error message to display */
  error?: string;
  /** Help text to display below the field */
  helpText?: string;
  /** Icon to display in the input (for text inputs) */
  icon?: React.ReactNode;
  /** Whether to show password toggle (for password inputs) */
  showPasswordToggle?: boolean;
  /** Additional props for the input element */
  inputProps?: React.InputHTMLAttributes<HTMLInputElement> | React.TextareaHTMLAttributes<HTMLTextAreaElement>;
  /** Custom input styling */
  inputClassName?: string;
  /** Minimum height for textarea */
  minHeight?: string;
}

/**
 * FormField component with amber/orange branding and consistent styling
 */
export const FormField: React.FC<FormFieldProps> = ({
  label,
  type = 'text',
  placeholder,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  helpText,
  icon,
  showPasswordToggle = false,
  inputProps,
  inputClassName,
  minHeight = '120px',
  className,
  testId,
  ...props
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const fieldId = React.useId();
  const errorId = React.useId();
  const helpId = React.useId();

  // Amber/orange styling for inputs
  const amberInputStyles = cn(
    'border-amber-200 focus:border-amber-400 focus:ring-amber-400',
    'transition-colors duration-200',
    error && 'border-red-300 focus:border-red-400 focus:ring-red-400',
    disabled && 'opacity-50 cursor-not-allowed',
    inputClassName
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
    onChange(newValue);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const renderInput = () => {
    const commonProps = {
      id: fieldId,
      placeholder,
      value: value.toString(),
      onChange: handleChange,
      required,
      disabled,
      className: amberInputStyles,
      'aria-describedby': cn(
        error && errorId,
        helpText && helpId
      ),
      'aria-invalid': !!error,
      'data-testid': testId ? `${testId}-input` : undefined,
      ...inputProps,
    };

    if (type === 'textarea') {
      return (
        <Textarea
          {...(commonProps as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
          style={{ minHeight }}
          className={cn(amberInputStyles, 'resize-none')}
        />
      );
    }

    if (type === 'password' && showPasswordToggle) {
      return (
        <div className="relative">
          <Input
            {...(commonProps as React.InputHTMLAttributes<HTMLInputElement>)}
            type={showPassword ? 'text' : 'password'}
            className={cn(amberInputStyles, 'pr-10')}
          />
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
            ) : (
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            )}
          </button>
        </div>
      );
    }

    if (icon) {
      return (
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
          <Input
            {...(commonProps as React.InputHTMLAttributes<HTMLInputElement>)}
            type={type}
            className={cn(amberInputStyles, 'pl-10')}
          />
        </div>
      );
    }

    return (
      <Input
        {...(commonProps as React.InputHTMLAttributes<HTMLInputElement>)}
        type={type}
      />
    );
  };

  return (
    <div className={cn('space-y-2', className)} data-testid={testId} {...props}>
      <Label 
        htmlFor={fieldId} 
        className={cn(
          'text-base font-medium',
          required && "after:content-['*'] after:ml-0.5 after:text-red-500"
        )}
      >
        {label}
      </Label>
      
      {renderInput()}
      
      {error && (
        <p 
          id={errorId}
          className="text-sm font-medium text-red-600"
          role="alert"
          data-testid={testId ? `${testId}-error` : undefined}
        >
          {error}
        </p>
      )}
      
      {helpText && !error && (
        <p 
          id={helpId}
          className="text-sm text-muted-foreground"
          data-testid={testId ? `${testId}-help` : undefined}
        >
          {helpText}
        </p>
      )}
    </div>
  );
};
