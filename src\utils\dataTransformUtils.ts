/**
 * Data Transformation Utilities
 * Centralized utilities for converting between Supabase types and application types,
 * date formatting, and data mapping across the application
 */

import { utcTimestampToLocalDateString } from '@/utils/dateUtils';
import {
  FormattedJournalEntry,
  EmotionType,
  MoodScore,
  UserProfile,
} from '@/types';

/**
 * Supabase journal entry type (raw from database)
 */
export interface SupabaseJournalEntry {
  id: string;
  user_id: string;
  title: string;
  content: string;
  emotion: string;
  mood_score: number;
  ai_reflection?: string | null;
  ai_summary?: string | null;
  ai_emotion?: string | null;
  ai_encouragement?: string | null;
  ai_reflection_question?: string | null;
  created_at: string;
  updated_at?: string | null;
}

/**
 * Supabase user profile type (raw from database)
 */
export interface SupabaseUserProfile {
  id: string;
  email: string;
  full_name?: string | null;
  avatar_url?: string | null;
  created_at: string;
  updated_at?: string | null;
}

/**
 * Journal entry insert type for Supabase
 */
export interface JournalEntryInsert {
  user_id: string;
  title: string;
  content: string;
  emotion: EmotionType;
  mood_score: MoodScore;
  ai_reflection?: string | null;
  ai_summary?: string | null;
  ai_emotion?: string | null;
  ai_encouragement?: string | null;
  ai_reflection_question?: string | null;
}

/**
 * Journal entry update type for Supabase
 */
export interface JournalEntryUpdate {
  title?: string;
  content?: string;
  emotion?: EmotionType;
  mood_score?: MoodScore;
  ai_reflection?: string | null;
  ai_summary?: string | null;
  ai_emotion?: string | null;
  ai_encouragement?: string | null;
  ai_reflection_question?: string | null;
  updated_at?: string;
}

/**
 * Transform Supabase journal entry to application format
 */
export const supabaseToJournalEntry = (entry: SupabaseJournalEntry): FormattedJournalEntry => {
  return {
    id: entry.id,
    date: utcTimestampToLocalDateString(entry.created_at),
    title: entry.title,
    content: entry.content,
    emotion: entry.emotion as EmotionType,
    mood_score: entry.mood_score as MoodScore,
    ai_reflection: entry.ai_reflection,
    ai_summary: entry.ai_summary,
    ai_emotion: entry.ai_emotion,
    ai_encouragement: entry.ai_encouragement,
    ai_reflection_question: entry.ai_reflection_question,
    created_at: entry.created_at,
    updated_at: entry.updated_at,
  };
};

/**
 * Transform application journal entry data to Supabase insert format
 */
export const journalEntryToSupabaseInsert = (
  entry: {
    title: string;
    content: string;
    emotion: EmotionType | '';
    moodScore: MoodScore;
  },
  userId: string,
  aiReflection?: {
    summary?: string;
    emotion?: string;
    encouragement?: string;
    reflection_question?: string;
    reflection?: string;
  }
): JournalEntryInsert => {
  return {
    user_id: userId,
    title: entry.title.trim(),
    content: entry.content.trim(),
    emotion: entry.emotion as EmotionType,
    mood_score: entry.moodScore,
    ai_reflection: aiReflection?.reflection || null,
    ai_summary: aiReflection?.summary || null,
    ai_emotion: aiReflection?.emotion || null,
    ai_encouragement: aiReflection?.encouragement || null,
    ai_reflection_question: aiReflection?.reflection_question || null,
  };
};

/**
 * Transform application journal entry data to Supabase update format
 */
export const journalEntryToSupabaseUpdate = (
  entry: {
    title: string;
    content: string;
    emotion: EmotionType | '';
    moodScore: MoodScore;
  },
  aiReflection?: {
    summary?: string;
    emotion?: string;
    encouragement?: string;
    reflection_question?: string;
    reflection?: string;
  }
): JournalEntryUpdate => {
  return {
    title: entry.title.trim(),
    content: entry.content.trim(),
    emotion: entry.emotion as EmotionType,
    mood_score: entry.moodScore,
    ai_reflection: aiReflection?.reflection || null,
    ai_summary: aiReflection?.summary || null,
    ai_emotion: aiReflection?.emotion || null,
    ai_encouragement: aiReflection?.encouragement || null,
    ai_reflection_question: aiReflection?.reflection_question || null,
    updated_at: new Date().toISOString(),
  };
};

/**
 * Transform Supabase user profile to application format
 */
export const supabaseToUserProfile = (profile: SupabaseUserProfile): UserProfile => {
  return {
    id: profile.id,
    email: profile.email,
    full_name: profile.full_name,
    avatar_url: profile.avatar_url,
    created_at: profile.created_at,
    updated_at: profile.updated_at,
  };
};

/**
 * Transform multiple Supabase journal entries to application format
 */
export const supabaseToJournalEntries = (entries: SupabaseJournalEntry[]): FormattedJournalEntry[] => {
  return entries.map(supabaseToJournalEntry);
};

/**
 * Sort journal entries by date (newest first)
 */
export const sortJournalEntriesByDate = (entries: FormattedJournalEntry[]): FormattedJournalEntry[] => {
  return [...entries].sort((a, b) => {
    const dateA = new Date(a.created_at);
    const dateB = new Date(b.created_at);
    return dateB.getTime() - dateA.getTime();
  });
};

/**
 * Group journal entries by date
 */
export const groupJournalEntriesByDate = (entries: FormattedJournalEntry[]): Record<string, FormattedJournalEntry[]> => {
  return entries.reduce((groups, entry) => {
    const date = entry.date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(entry);
    return groups;
  }, {} as Record<string, FormattedJournalEntry[]>);
};

/**
 * Filter journal entries by emotion
 */
export const filterJournalEntriesByEmotion = (
  entries: FormattedJournalEntry[],
  emotions: EmotionType[]
): FormattedJournalEntry[] => {
  if (emotions.length === 0) return entries;
  return entries.filter(entry => emotions.includes(entry.emotion));
};

/**
 * Filter journal entries by mood score range
 */
export const filterJournalEntriesByMoodRange = (
  entries: FormattedJournalEntry[],
  minMood: MoodScore,
  maxMood: MoodScore
): FormattedJournalEntry[] => {
  return entries.filter(entry => 
    entry.mood_score >= minMood && entry.mood_score <= maxMood
  );
};

/**
 * Search journal entries by text content
 */
export const searchJournalEntries = (
  entries: FormattedJournalEntry[],
  searchTerm: string
): FormattedJournalEntry[] => {
  if (!searchTerm.trim()) return entries;
  
  const term = searchTerm.toLowerCase();
  return entries.filter(entry =>
    entry.title.toLowerCase().includes(term) ||
    entry.content.toLowerCase().includes(term)
  );
};

/**
 * Calculate mood statistics from journal entries
 */
export const calculateMoodStats = (entries: FormattedJournalEntry[]) => {
  if (entries.length === 0) {
    return {
      average: 0,
      highest: 0,
      lowest: 0,
      total: 0,
      count: 0,
    };
  }

  const moodScores = entries.map(entry => entry.mood_score);
  const total = moodScores.reduce((sum, score) => sum + score, 0);
  const average = total / moodScores.length;
  const highest = Math.max(...moodScores);
  const lowest = Math.min(...moodScores);

  return {
    average: Math.round(average * 10) / 10, // Round to 1 decimal place
    highest,
    lowest,
    total,
    count: entries.length,
  };
};

/**
 * Get emotion frequency from journal entries
 */
export const getEmotionFrequency = (entries: FormattedJournalEntry[]): Record<EmotionType, number> => {
  const frequency: Record<string, number> = {};
  
  entries.forEach(entry => {
    frequency[entry.emotion] = (frequency[entry.emotion] || 0) + 1;
  });

  return frequency as Record<EmotionType, number>;
};

/**
 * Format journal entry for display (truncate content if needed)
 */
export const formatJournalEntryForDisplay = (
  entry: FormattedJournalEntry,
  maxContentLength: number = 150
): FormattedJournalEntry => {
  return {
    ...entry,
    content: entry.content.length > maxContentLength
      ? `${entry.content.substring(0, maxContentLength)}...`
      : entry.content,
  };
};

/**
 * Sanitize text content (remove potentially harmful content)
 */
export const sanitizeTextContent = (content: string): string => {
  // Remove HTML tags
  const withoutHtml = content.replace(/<[^>]*>/g, '');
  
  // Remove excessive whitespace
  const normalized = withoutHtml.replace(/\s+/g, ' ').trim();
  
  return normalized;
};

/**
 * Validate and transform emotion string to EmotionType
 */
export const validateEmotion = (emotion: string): EmotionType | null => {
  const validEmotions: EmotionType[] = [
    'happy', 'sad', 'angry', 'anxious', 'excited', 'calm', 'frustrated', 'grateful'
  ];
  
  return validEmotions.includes(emotion as EmotionType) ? emotion as EmotionType : null;
};

/**
 * Validate and transform mood score to MoodScore
 */
export const validateMoodScore = (score: number): MoodScore | null => {
  if (score >= 1 && score <= 10 && Number.isInteger(score)) {
    return score as MoodScore;
  }
  return null;
};

/**
 * Create a safe copy of journal entry (for optimistic updates)
 */
export const createJournalEntryCopy = (entry: FormattedJournalEntry): FormattedJournalEntry => {
  return {
    ...entry,
    // Ensure all string fields are properly copied
    title: String(entry.title),
    content: String(entry.content),
    emotion: entry.emotion,
    mood_score: entry.mood_score,
  };
};
