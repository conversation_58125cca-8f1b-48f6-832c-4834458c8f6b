/**
 * Mood Calendar Heatmap Component
 * Calendar heatmap showing daily mood scores
 */

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from 'lucide-react';
import { DailyMoodData } from '@/types';
import { format, parseISO, startOfMonth, endOfMonth, eachDayOfInterval, getDay } from 'date-fns';

interface MoodCalendarHeatmapProps {
  data?: DailyMoodData[];
  isLoading?: boolean;
}

/**
 * Get mood color intensity based on mood score
 */
const getMoodColor = (mood: number | null, hasEntry: boolean): string => {
  if (!hasEntry || mood === null) {
    return 'bg-gray-100 border-gray-200';
  }
  
  // Amber/orange color scheme with intensity based on mood
  if (mood >= 9) return 'bg-amber-500 border-amber-600';
  if (mood >= 8) return 'bg-amber-400 border-amber-500';
  if (mood >= 7) return 'bg-amber-300 border-amber-400';
  if (mood >= 6) return 'bg-orange-300 border-orange-400';
  if (mood >= 5) return 'bg-orange-200 border-orange-300';
  if (mood >= 4) return 'bg-orange-100 border-orange-200';
  if (mood >= 3) return 'bg-red-100 border-red-200';
  if (mood >= 2) return 'bg-red-200 border-red-300';
  return 'bg-red-300 border-red-400';
};

/**
 * Get mood description
 */
const getMoodDescription = (mood: number | null): string => {
  if (mood === null) return 'No entry';
  if (mood >= 9) return 'Excellent';
  if (mood >= 8) return 'Great';
  if (mood >= 7) return 'Good';
  if (mood >= 6) return 'Okay';
  if (mood >= 5) return 'Neutral';
  if (mood >= 4) return 'Not great';
  if (mood >= 3) return 'Poor';
  if (mood >= 2) return 'Bad';
  return 'Very bad';
};

/**
 * Emotion emoji mapping
 */
const EMOTION_EMOJIS: Record<string, string> = {
  joyful: '😊',
  calm: '😌',
  neutral: '😐',
  sad: '😢',
  anxious: '😰',
  excited: '🤩',
  grateful: '🙏',
  frustrated: '😤',
  hopeful: '🌟',
  overwhelmed: '😵',
};

/**
 * Day cell component
 */
const DayCell = ({ dayData, date }: { dayData?: DailyMoodData; date: Date }) => {
  const isToday = format(date, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');
  const colorClass = getMoodColor(dayData?.mood || null, dayData?.hasEntry || false);
  const moodDescription = getMoodDescription(dayData?.mood || null);
  const emoji = dayData?.emotion ? EMOTION_EMOJIS[dayData.emotion] : '';

  return (
    <div className="group relative">
      <div
        className={`
          w-8 h-8 border-2 rounded-lg flex items-center justify-center text-xs font-medium
          transition-all duration-200 hover:scale-110 cursor-pointer
          ${colorClass}
          ${isToday ? 'ring-2 ring-amber-500 ring-offset-1' : ''}
        `}
        title={`${format(date, 'MMM d, yyyy')} - ${moodDescription}${dayData?.mood ? ` (${dayData.mood}/10)` : ''}`}
      >
        {dayData?.hasEntry && (
          <span className="text-white font-bold">
            {format(date, 'd')}
          </span>
        )}
        {!dayData?.hasEntry && (
          <span className="text-gray-400">
            {format(date, 'd')}
          </span>
        )}
      </div>
      
      {/* Tooltip */}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap">
        <div className="font-medium">{format(date, 'MMM d, yyyy')}</div>
        {dayData?.hasEntry ? (
          <div className="space-y-1">
            <div>Mood: {dayData.mood}/10 ({moodDescription})</div>
            {dayData.emotion && (
              <div>Emotion: {emoji} {dayData.emotion}</div>
            )}
            {dayData.entryCount > 1 && (
              <div>{dayData.entryCount} entries</div>
            )}
          </div>
        ) : (
          <div>No journal entry</div>
        )}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
      </div>
    </div>
  );
};

/**
 * Calendar grid component
 */
const CalendarGrid = ({ data }: { data: DailyMoodData[] }) => {
  // Create a map for quick lookup
  const dataMap = new Map(data.map(item => [item.date, item]));
  
  // Get the date range (last 3 months for better visualization)
  const endDate = new Date();
  const startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 2, 1);
  
  const months = [];
  let currentMonth = startOfMonth(startDate);
  
  while (currentMonth <= endOfMonth(endDate)) {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });
    
    months.push({
      name: format(currentMonth, 'MMMM yyyy'),
      days: daysInMonth,
    });
    
    currentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1);
  }

  return (
    <div className="space-y-6">
      {months.map((month) => (
        <div key={month.name}>
          <h4 className="text-sm font-medium text-gray-700 mb-3">{month.name}</h4>
          <div className="grid grid-cols-7 gap-1">
            {/* Day headers */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="text-xs text-gray-500 text-center py-1 font-medium">
                {day}
              </div>
            ))}
            
            {/* Empty cells for days before month starts */}
            {Array.from({ length: getDay(month.days[0]) }).map((_, index) => (
              <div key={`empty-${index}`} className="w-8 h-8" />
            ))}
            
            {/* Month days */}
            {month.days.map((date) => {
              const dateStr = format(date, 'yyyy-MM-dd');
              const dayData = dataMap.get(dateStr);
              return (
                <DayCell key={dateStr} dayData={dayData} date={date} />
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Legend component
 */
const Legend = () => {
  const legendItems = [
    { color: 'bg-gray-100 border-gray-200', label: 'No entry' },
    { color: 'bg-red-300 border-red-400', label: 'Low mood (1-3)' },
    { color: 'bg-orange-200 border-orange-300', label: 'Neutral (4-6)' },
    { color: 'bg-amber-300 border-amber-400', label: 'Good mood (7-8)' },
    { color: 'bg-amber-500 border-amber-600', label: 'Great mood (9-10)' },
  ];

  return (
    <div className="flex flex-wrap gap-4 text-xs">
      <span className="text-gray-600 font-medium">Mood intensity:</span>
      {legendItems.map((item, index) => (
        <div key={index} className="flex items-center gap-1">
          <div className={`w-3 h-3 border rounded ${item.color}`} />
          <span className="text-gray-600">{item.label}</span>
        </div>
      ))}
    </div>
  );
};

export const MoodCalendarHeatmap = ({ data, isLoading = false }: MoodCalendarHeatmapProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-amber-600" />
            Mood Calendar
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="animate-pulse text-gray-500">Loading calendar...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-amber-600" />
            Mood Calendar
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No mood data available</p>
            <p className="text-sm mt-2">Start journaling to see your mood patterns!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate some stats
  const entriesWithMood = data.filter(d => d.hasEntry && d.mood !== null);
  const averageMood = entriesWithMood.length > 0
    ? entriesWithMood.reduce((sum, d) => sum + (d.mood || 0), 0) / entriesWithMood.length
    : 0;
  const totalEntries = data.filter(d => d.hasEntry).length;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-amber-600" />
            Mood Calendar
          </div>
          <div className="text-sm text-gray-600">
            {totalEntries} entries • Avg: {averageMood.toFixed(1)}/10
          </div>
        </CardTitle>
        <p className="text-sm text-gray-600">
          Visual representation of your daily mood patterns over time
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <CalendarGrid data={data} />
        <Legend />
      </CardContent>
    </Card>
  );
};
