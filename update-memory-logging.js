const fs = require('fs');
const path = require('path');

// Path to the memory extraction service file
const filePath = path.join(__dirname, 'src', 'services', 'memoryExtractionService.ts');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Replace the journal entry memory logging code
content = content.replace(
  /\/\/ Log the top relevant memories for debugging\s*const topMemories = relevantResult\.data\.slice\(0, 3\);\s*console\.log\('\ud83e\udde0 Top relevant memories:', topMemories\.map\(m =>\s*`\${m\.key}: \${m\.value} \(relevance: \${\(m\.relevance \* 100\)\.toFixed\(0\)}%, importance: \${m\.importance}\/10\)`\s*\)\);/g,
  `// Log all relevant memories for debugging
          console.log('🧠 All relevant memories:');
          relevantResult.data.forEach((memory, index) => {
            console.log(\`🧠 [\${index + 1}] \${memory.key}: \${memory.value} (relevance: \${(memory.relevance * 100).toFixed(0)}%, importance: \${memory.importance}/10)\`);
          });`
);

// Write the updated content back to the file
fs.writeFileSync(filePath, content, 'utf8');

console.log('Memory extraction service updated successfully!');
