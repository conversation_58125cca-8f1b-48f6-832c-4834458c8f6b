/**
 * Generic Pagination Hook
 * Reusable pagination hook that can be used across different data types
 * Consolidates pagination logic with React Query integration
 */

import { useReducer, useCallback, useMemo } from 'react';
import { useInfiniteQuery, UseInfiniteQueryOptions } from '@tanstack/react-query';
import {
  PaginationState,
  PaginationActions,
  PaginationOptions,
  paginationReducer,
  createInitialPaginationState,
  calculateRange,
  DEFAULT_PAGINATION_CONFIG,
} from '@/utils/paginationUtils';

/**
 * Pagination query function type
 */
export type PaginationQueryFn<T> = (params: {
  page: number;
  pageSize: number;
  range: [number, number];
}) => Promise<{
  data: T[];
  totalCount: number;
  hasMore: boolean;
}>;

/**
 * Pagination hook options
 */
export interface UsePaginationOptions<T> extends PaginationOptions {
  /** Query key for React Query */
  queryKey: any[];
  /** Function to fetch paginated data */
  queryFn: PaginationQueryFn<T>;
  /** Whether the query is enabled */
  enabled?: boolean;
  /** Additional React Query options */
  queryOptions?: Partial<UseInfiniteQueryOptions<any, Error>>;
}

/**
 * Pagination hook return type
 */
export interface UsePaginationReturn<T> extends PaginationState, PaginationActions {
  /** Paginated data */
  data: T[];
  /** All pages data (for infinite scroll) */
  allPages: T[][];
  /** Error from React Query */
  error: Error | null;
  /** Whether currently fetching */
  isFetching: boolean;
  /** Whether fetching next page */
  isFetchingNextPage: boolean;
  /** Whether can fetch next page */
  hasNextPage: boolean;
  /** Fetch next page function */
  fetchNextPage: () => void;
  /** Refetch current data */
  refetch: () => void;
  /** Invalidate and refetch */
  invalidate: () => void;
}

/**
 * Generic pagination hook with React Query integration
 */
export const usePagination = <T>(
  options: UsePaginationOptions<T>
): UsePaginationReturn<T> => {
  const {
    queryKey,
    queryFn,
    enabled = true,
    pageSize = DEFAULT_PAGINATION_CONFIG.pageSize,
    infiniteScroll = false,
    cacheTime = DEFAULT_PAGINATION_CONFIG.cacheTime,
    staleTime = DEFAULT_PAGINATION_CONFIG.staleTime,
    maxPages = DEFAULT_PAGINATION_CONFIG.maxPages,
    queryOptions = {},
  } = options;

  // Initialize pagination state
  const [state, dispatch] = useReducer(
    paginationReducer,
    createInitialPaginationState({ pageSize })
  );

  // React Query for infinite pagination
  const {
    data: queryData,
    error,
    isLoading,
    isFetching,
    isFetchingNextPage,
    hasNextPage: queryHasNextPage,
    fetchNextPage,
    refetch,
  } = useInfiniteQuery({
    queryKey,
    queryFn: async ({ pageParam = 0 }) => {
      const range = calculateRange(pageParam, pageSize);
      const result = await queryFn({
        page: pageParam,
        pageSize,
        range,
      });

      // Update total items on first load
      if (pageParam === 0) {
        dispatch({ type: 'SET_TOTAL_ITEMS', totalItems: result.totalCount });
      }

      return {
        ...result,
        page: pageParam,
      };
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.hasMore || allPages.length >= maxPages) {
        return undefined;
      }
      return lastPage.page + 1;
    },
    enabled,
    cacheTime,
    staleTime,
    refetchOnMount: 'always',
    ...queryOptions,
  });

  // Flatten data from all pages
  const data = useMemo(() => {
    if (!queryData?.pages) return [];
    return queryData.pages.flatMap(page => page.data);
  }, [queryData]);

  // All pages data
  const allPages = useMemo(() => {
    if (!queryData?.pages) return [];
    return queryData.pages.map(page => page.data);
  }, [queryData]);

  // Update loading state
  useEffect(() => {
    dispatch({ type: 'SET_LOADING', loading: isLoading });
  }, [isLoading]);

  // Update error state
  useEffect(() => {
    dispatch({ type: 'SET_ERROR', error: !!error });
  }, [error]);

  // Pagination actions
  const nextPage = useCallback(() => {
    if (infiniteScroll) {
      fetchNextPage();
    } else {
      dispatch({ type: 'NEXT_PAGE' });
    }
  }, [infiniteScroll, fetchNextPage]);

  const previousPage = useCallback(() => {
    if (!infiniteScroll) {
      dispatch({ type: 'PREVIOUS_PAGE' });
    }
  }, [infiniteScroll]);

  const goToPage = useCallback((page: number) => {
    if (!infiniteScroll) {
      dispatch({ type: 'SET_PAGE', page });
    }
  }, [infiniteScroll]);

  const reset = useCallback(() => {
    dispatch({ type: 'RESET' });
    refetch();
  }, [refetch]);

  const loadMore = useCallback(() => {
    if (queryHasNextPage) {
      fetchNextPage();
    }
  }, [queryHasNextPage, fetchNextPage]);

  const invalidate = useCallback(() => {
    // This would need to be implemented with queryClient.invalidateQueries
    // in the consuming component
    refetch();
  }, [refetch]);

  return {
    // State
    ...state,
    hasNextPage: infiniteScroll ? queryHasNextPage : state.hasNextPage,
    
    // Data
    data,
    allPages,
    
    // Query state
    error,
    isFetching,
    isFetchingNextPage,
    
    // Actions
    nextPage,
    previousPage,
    goToPage,
    reset,
    loadMore,
    fetchNextPage,
    refetch,
    invalidate,
  };
};

/**
 * Simple pagination hook without React Query (for local data)
 */
export const useLocalPagination = <T>(
  data: T[],
  options: PaginationOptions = {}
) => {
  const { pageSize = DEFAULT_PAGINATION_CONFIG.pageSize } = options;

  const [state, dispatch] = useReducer(
    paginationReducer,
    createInitialPaginationState({ pageSize })
  );

  // Update total items when data changes
  useMemo(() => {
    dispatch({ type: 'SET_TOTAL_ITEMS', totalItems: data.length });
  }, [data.length]);

  // Get current page data
  const currentPageData = useMemo(() => {
    const start = state.currentPage * state.pageSize;
    const end = start + state.pageSize;
    return data.slice(start, end);
  }, [data, state.currentPage, state.pageSize]);

  // Actions
  const nextPage = useCallback(() => {
    dispatch({ type: 'NEXT_PAGE' });
  }, []);

  const previousPage = useCallback(() => {
    dispatch({ type: 'PREVIOUS_PAGE' });
  }, []);

  const goToPage = useCallback((page: number) => {
    dispatch({ type: 'SET_PAGE', page });
  }, []);

  const reset = useCallback(() => {
    dispatch({ type: 'RESET' });
  }, []);

  return {
    ...state,
    data: currentPageData,
    nextPage,
    previousPage,
    goToPage,
    reset,
    loadMore: nextPage, // Alias for consistency
  };
};
