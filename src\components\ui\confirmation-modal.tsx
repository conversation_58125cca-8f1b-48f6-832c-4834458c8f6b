/**
 * Confirmation Modal Component
 * Reusable confirmation dialog with glass-effect theme and accessibility
 */

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Trash2, X } from 'lucide-react';
import { cn } from '@/utils/utils';

interface ConfirmationModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Function called when user confirms */
  onConfirm: () => void;
  /** Modal title */
  title: string;
  /** Modal description/message */
  description: string;
  /** Confirm button text */
  confirmText?: string;
  /** Cancel button text */
  cancelText?: string;
  /** Whether the action is destructive (affects styling) */
  variant?: 'default' | 'destructive';
  /** Whether the confirm action is loading */
  isLoading?: boolean;
  /** Icon to display */
  icon?: 'warning' | 'trash' | 'none';
  /** Additional CSS classes */
  className?: string;
}

export const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  isLoading = false,
  icon = 'warning',
  className,
}: ConfirmationModalProps) => {
  const handleConfirm = () => {
    if (!isLoading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const renderIcon = () => {
    if (icon === 'none') return null;

    const iconClasses = cn(
      'w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center',
      variant === 'destructive'
        ? 'bg-red-100 text-red-600'
        : 'bg-amber-100 text-amber-600'
    );

    const IconComponent = icon === 'trash' ? Trash2 : AlertTriangle;

    return (
      <div className={iconClasses}>
        <IconComponent className="w-6 h-6" />
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          'glass-effect border-white/30 bg-white/95 backdrop-blur-md shadow-2xl',
          'max-w-md mx-auto',
          className
        )}
        aria-describedby="confirmation-description"
      >
        <DialogHeader className="text-center">
          {renderIcon()}
          <DialogTitle className="text-xl font-semibold text-gray-900">
            {title}
          </DialogTitle>
          <DialogDescription
            id="confirmation-description"
            className="text-gray-600 mt-2 leading-relaxed"
          >
            {description}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 mt-6">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="flex-1 border-gray-300 hover:bg-gray-50"
          >
            {cancelText}
          </Button>
          <Button
            variant={variant === 'destructive' ? 'destructive' : 'default'}
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              'flex-1',
              variant === 'destructive'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-amber-500 hover:bg-amber-600 text-white'
            )}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                Processing...
              </>
            ) : (
              confirmText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
