/**
 * Memory Management Component
 * Component for managing user memories in the Settings page
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { format, formatDistanceToNow } from 'date-fns';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Brain, Plus, Edit, Trash2, Search, Filter, Merge } from 'lucide-react';
import { useMemoryContext } from '@/hooks/useMemoryContext';
import { UserMemory, MemoryCategory } from '@/types';
import { toast } from 'sonner';

/**
 * Memory management component
 */
export const MemoryManagement: React.FC = () => {
  const {
    memories,
    addMemory,
    removeMemory,
    updateMemory,
    getMemoriesByCategory,
    searchMemories,
    clearMemories,
    deduplicateMemories,
    stats,
  } = useMemoryContext();

  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<MemoryCategory | 'all'>('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingMemory, setEditingMemory] = useState<UserMemory | null>(null);
  const [isDeduplicating, setIsDeduplicating] = useState(false);
  const [newMemory, setNewMemory] = useState<Omit<UserMemory, 'key'> & { key: string }>({
    key: '',
    value: '',
    category: 'fact',
  });

  /**
   * Get category color for badges
   */
  const getCategoryColor = (category: MemoryCategory) => {
    const colors = {
      fact: 'bg-blue-100 text-blue-800 border-blue-200',
      emotion: 'bg-red-100 text-red-800 border-red-200',
      event: 'bg-green-100 text-green-800 border-green-200',
      goal: 'bg-purple-100 text-purple-800 border-purple-200',
      preference: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      identity: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  /**
   * Filter memories based on search and category
   */
  const filteredMemories = React.useMemo(() => {
    let filtered = memories;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = searchMemories(searchQuery);
    }

    // Apply category filter
    if (filterCategory !== 'all') {
      filtered = filtered.filter(memory => memory.category === filterCategory);
    }

    return filtered;
  }, [memories, searchQuery, filterCategory, searchMemories]);

  /**
   * Handle adding a new memory
   */
  const handleAddMemory = async () => {
    if (!newMemory.key.trim() || !newMemory.value.trim()) {
      toast.error('Please fill in both key and value fields');
      return;
    }

    try {
      await addMemory(newMemory);
      setNewMemory({ key: '', value: '', category: 'fact' });
      setIsAddDialogOpen(false);
      toast.success('Memory added successfully');
    } catch (error) {
      toast.error('Failed to add memory');
    }
  };

  /**
   * Handle editing a memory
   */
  const handleEditMemory = (memory: UserMemory) => {
    setEditingMemory(memory);
    setNewMemory(memory);
  };

  /**
   * Handle updating a memory
   */
  const handleUpdateMemory = async () => {
    if (!editingMemory || !newMemory.key.trim() || !newMemory.value.trim()) {
      toast.error('Please fill in both key and value fields');
      return;
    }

    try {
      await updateMemory(editingMemory.key, newMemory);
      setEditingMemory(null);
      setNewMemory({ key: '', value: '', category: 'fact' });
      toast.success('Memory updated successfully');
    } catch (error) {
      toast.error('Failed to update memory');
    }
  };

  /**
   * Handle deleting a memory
   */
  const handleDeleteMemory = async (key: string) => {
    try {
      await removeMemory(key);
      toast.success('Memory deleted successfully');
    } catch (error) {
      toast.error('Failed to delete memory');
    }
  };

  /**
   * Handle clearing all memories
   */
  const handleClearAllMemories = async () => {
    try {
      await clearMemories();
      toast.success('All memories cleared successfully');
    } catch (error) {
      toast.error('Failed to clear memories');
    }
  };

  /**
   * Handle deduplicating memories
   */
  const handleDeduplicateMemories = async () => {
    setIsDeduplicating(true);
    try {
      const result = await deduplicateMemories();
      if (result) {
        const { removedCount, mergedCount } = result;
        if (removedCount > 0 || mergedCount > 0) {
          toast.success(
            `Deduplication complete! Removed ${removedCount} duplicates and merged ${mergedCount} memories.`
          );
        } else {
          toast.success('No duplicate memories found.');
        }
      } else {
        toast.error('Failed to deduplicate memories');
      }
    } catch (error) {
      console.error('Error deduplicating memories:', error);
      toast.error('Failed to deduplicate memories');
    } finally {
      setIsDeduplicating(false);
    }
  };

  return (
    <Card className="glass-effect border-amber-200/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-amber-900">
          <Brain className="w-5 h-5" />
          Memory Management
        </CardTitle>
        <p className="text-amber-700/70 text-sm">
          Manage your personal memories that help Amber provide more personalized responses
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-amber-50 rounded-lg">
            <div className="text-2xl font-bold text-amber-600">{memories.length}</div>
            <div className="text-xs text-amber-700">Total Memories</div>
          </div>
          <div className="text-center p-3 bg-amber-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{stats.successfulExtractions}</div>
            <div className="text-xs text-amber-700">Extracted</div>
          </div>
          <div className="text-center p-3 bg-amber-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{stats.totalMemoriesExtracted}</div>
            <div className="text-xs text-amber-700">Auto-Found</div>
          </div>
          <div className="text-center p-3 bg-amber-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(stats.averageProcessingTime)}ms
            </div>
            <div className="text-xs text-amber-700">Avg Time</div>
          </div>
        </div>

        <Separator className="bg-amber-200/50" />

        {/* Search and Filter */}
        <div className="flex gap-4">
          <div className="flex-1">
            <Label htmlFor="search" className="text-amber-900 font-medium">
              Search Memories
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-amber-500" />
              <Input
                id="search"
                placeholder="Search by key or value..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="filter" className="text-amber-900 font-medium">
              Filter by Category
            </Label>
            <Select value={filterCategory} onValueChange={(value) => setFilterCategory(value as MemoryCategory | 'all')}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="fact">Facts</SelectItem>
                <SelectItem value="emotion">Emotions</SelectItem>
                <SelectItem value="event">Events</SelectItem>
                <SelectItem value="goal">Goals</SelectItem>
                <SelectItem value="preference">Preferences</SelectItem>
                <SelectItem value="identity">Identity</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Add Memory Button */}
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-amber-900">
            Your Memories ({filteredMemories.length})
          </h3>
          <div className="flex gap-2">
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Memory
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Memory</DialogTitle>
                  <DialogDescription>
                    Add a new memory that Amber should remember about you.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="memory-key">Key (Short Label)</Label>
                    <Input
                      id="memory-key"
                      placeholder="e.g., pet, hobby, goal"
                      value={newMemory.key}
                      onChange={(e) => setNewMemory(prev => ({ ...prev, key: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="memory-value">Value (Description)</Label>
                    <Textarea
                      id="memory-value"
                      placeholder="e.g., has a cat named Whiskers"
                      value={newMemory.value}
                      onChange={(e) => setNewMemory(prev => ({ ...prev, value: e.target.value }))}
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="memory-category">Category</Label>
                    <Select value={newMemory.category} onValueChange={(value) => setNewMemory(prev => ({ ...prev, category: value as MemoryCategory }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fact">Fact</SelectItem>
                        <SelectItem value="emotion">Emotion</SelectItem>
                        <SelectItem value="event">Event</SelectItem>
                        <SelectItem value="goal">Goal</SelectItem>
                        <SelectItem value="preference">Preference</SelectItem>
                        <SelectItem value="identity">Identity</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddMemory}>Add Memory</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {memories.length > 1 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeduplicateMemories}
                disabled={isDeduplicating}
                className="text-amber-600 border-amber-200 hover:bg-amber-50"
              >
                <Merge className="w-4 h-4 mr-2" />
                {isDeduplicating ? 'Deduplicating...' : 'Remove Duplicates'}
              </Button>
            )}

            {memories.length > 0 && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" size="sm" className="text-red-600 border-red-200 hover:bg-red-50">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear All
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Clear All Memories?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete all your memories. This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleClearAllMemories} className="bg-red-600 hover:bg-red-700">
                      Clear All
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        </div>

        {/* Memory List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredMemories.length === 0 ? (
            <div className="text-center py-8 text-amber-600">
              <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No memories found</p>
              <p className="text-sm opacity-75">
                {searchQuery || filterCategory !== 'all' 
                  ? 'Try adjusting your search or filter'
                  : 'Add your first memory or let Amber extract them from your journal entries'
                }
              </p>
            </div>
          ) : (
            filteredMemories.map((memory, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-amber-50 rounded-lg border border-amber-200">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <Badge className={getCategoryColor(memory.category)}>
                    {memory.category}
                  </Badge>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-center mb-1">
                      <span className="font-medium text-amber-900">{memory.key}:</span>
                      {(memory as any).updated_at && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <span className="text-xs text-amber-600">
                                {formatDistanceToNow(new Date((memory as any).updated_at), { addSuffix: true })}
                              </span>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Created: {(memory as any).created_at ? format(new Date((memory as any).created_at), 'PPpp') : 'Unknown'}</p>
                              <p>Updated: {format(new Date((memory as any).updated_at), 'PPpp')}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                    <span className="text-amber-800">{memory.value}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEditMemory(memory)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="ghost" size="sm" className="text-red-600 hover:bg-red-50">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Memory?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete the memory "{memory.key}"? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDeleteMemory(memory.key)} className="bg-red-600 hover:bg-red-700">
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Edit Memory Dialog */}
        <Dialog open={!!editingMemory} onOpenChange={(open) => !open && setEditingMemory(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Memory</DialogTitle>
              <DialogDescription>
                Update the memory information.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-memory-key">Key (Short Label)</Label>
                <Input
                  id="edit-memory-key"
                  value={newMemory.key}
                  onChange={(e) => setNewMemory(prev => ({ ...prev, key: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="edit-memory-value">Value (Description)</Label>
                <Textarea
                  id="edit-memory-value"
                  value={newMemory.value}
                  onChange={(e) => setNewMemory(prev => ({ ...prev, value: e.target.value }))}
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="edit-memory-category">Category</Label>
                <Select value={newMemory.category} onValueChange={(value) => setNewMemory(prev => ({ ...prev, category: value as MemoryCategory }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fact">Fact</SelectItem>
                    <SelectItem value="emotion">Emotion</SelectItem>
                    <SelectItem value="event">Event</SelectItem>
                    <SelectItem value="goal">Goal</SelectItem>
                    <SelectItem value="preference">Preference</SelectItem>
                    <SelectItem value="identity">Identity</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditingMemory(null)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateMemory}>Update Memory</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};
