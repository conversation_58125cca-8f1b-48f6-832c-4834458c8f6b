/**
 * Mood Analytics Hook
 * Custom hook for fetching and managing mood analytics data with React Query
 */

import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { MoodAnalyticsData, AnalyticsTimeRange, AnalyticsFilters, ApiResponse } from '@/types';
import { getMoodAnalytics } from '@/services/analyticsService';
import { queryKeys } from '@/config/queryClient.config';

interface UseMoodAnalyticsOptions extends Omit<UseQueryOptions<MoodAnalyticsData, Error>, 'queryKey' | 'queryFn'> {
  filters?: Partial<AnalyticsFilters>;
}

/**
 * Hook to fetch comprehensive mood analytics data
 */
export const useMoodAnalytics = (
  timeRange: AnalyticsTimeRange = '30d',
  options?: UseMoodAnalyticsOptions
) => {
  const { user } = useAuth();

  const filters: AnalyticsFilters = {
    timeRange,
    includeAIAnalysis: true,
    ...options?.filters,
  };

  return useQuery({
    queryKey: queryKeys.analytics.mood(user?.id || '', filters),
    queryFn: async (): Promise<MoodAnalyticsData> => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      const response = await getMoodAnalytics(user.id, filters);
      if (!response.success || !response.data) {
        throw new Error(response.error?.message || 'Failed to fetch mood analytics');
      }

      return response.data;
    },
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options,
  });
};

/**
 * Hook to fetch emotion distribution analytics
 */
export const useEmotionAnalytics = (
  timeRange: AnalyticsTimeRange = '30d',
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);
  
  return {
    ...analytics,
    data: analytics.data?.emotionDistribution,
  };
};

/**
 * Hook to fetch mood trend analytics
 */
export const useMoodTrendAnalytics = (
  timeRange: AnalyticsTimeRange = '30d',
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);
  
  return {
    ...analytics,
    data: analytics.data?.moodTrend,
  };
};

/**
 * Hook to fetch mood statistics
 */
export const useMoodStatsAnalytics = (
  timeRange: AnalyticsTimeRange = '30d',
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);
  
  return {
    ...analytics,
    data: analytics.data?.stats,
  };
};

/**
 * Hook to fetch daily mood data for calendar heatmap
 */
export const useDailyMoodAnalytics = (
  timeRange: AnalyticsTimeRange = '90d', // Default to 3 months for calendar
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);
  
  return {
    ...analytics,
    data: analytics.data?.dailyMoods,
  };
};

/**
 * Hook to fetch mood pattern analytics
 */
export const useMoodPatternAnalytics = (
  timeRange: AnalyticsTimeRange = '90d',
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);
  
  return {
    ...analytics,
    data: analytics.data?.patterns,
  };
};

/**
 * Hook to fetch mood-emotion correlation analytics
 */
export const useMoodEmotionCorrelationAnalytics = (
  timeRange: AnalyticsTimeRange = '90d',
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);
  
  return {
    ...analytics,
    data: analytics.data?.moodEmotionCorrelation,
  };
};

/**
 * Hook for analytics with custom filters
 */
export const useFilteredMoodAnalytics = (
  filters: AnalyticsFilters,
  options?: Omit<UseMoodAnalyticsOptions, 'filters'>
) => {
  return useMoodAnalytics(filters.timeRange, {
    ...options,
    filters,
  });
};

/**
 * Hook to get analytics insights and recommendations
 */
export const useMoodInsights = (
  timeRange: AnalyticsTimeRange = '30d',
  options?: UseMoodAnalyticsOptions
) => {
  const analytics = useMoodAnalytics(timeRange, options);

  // Generate insights based on analytics data
  const insights = analytics.data ? generateInsights(analytics.data) : [];

  return {
    ...analytics,
    insights,
  };
};

/**
 * Generate insights from analytics data
 */
const generateInsights = (data: MoodAnalyticsData): string[] => {
  const insights: string[] = [];
  const { stats, patterns } = data;

  // Mood trend insights
  if (stats.moodTrend === 'improving') {
    insights.push('🌟 Your mood has been improving over time - keep up the great work!');
  } else if (stats.moodTrend === 'declining') {
    insights.push('💙 Your mood has been declining. Consider reaching out for support if needed.');
  }

  // Streak insights
  if (stats.streakDays >= 30) {
    insights.push('🔥 Amazing! You\'ve maintained a 30+ day journaling streak!');
  } else if (stats.streakDays >= 7) {
    insights.push('📝 Great consistency! You\'ve been journaling for a week straight.');
  }

  // Mood level insights
  if (stats.averageMood >= 8) {
    insights.push('😊 You\'re maintaining excellent mood levels overall!');
  } else if (stats.averageMood >= 6) {
    insights.push('👍 Your mood levels are generally positive.');
  } else if (stats.averageMood < 4) {
    insights.push('💚 Remember that it\'s okay to have difficult days. Consider self-care activities.');
  }

  // Pattern insights
  if (patterns.bestDay && patterns.worstDay) {
    const bestDayName = patterns.bestDay.dayName;
    const worstDayName = patterns.worstDay.dayName;
    
    if (patterns.bestDay.averageMood - patterns.worstDay.averageMood > 2) {
      insights.push(`📊 You tend to feel best on ${bestDayName}s and struggle more on ${worstDayName}s.`);
    }
  }

  // Activity insights
  if (stats.entriesThisMonth > stats.entriesThisWeek * 4) {
    insights.push('📈 You\'ve been more active with journaling this month!');
  }

  // Emotion insights
  if (stats.mostCommonEmotion === 'grateful') {
    insights.push('🙏 Gratitude is your most common emotion - that\'s wonderful for wellbeing!');
  } else if (stats.mostCommonEmotion === 'anxious') {
    insights.push('🌸 You\'ve been experiencing anxiety frequently. Consider mindfulness or relaxation techniques.');
  }

  return insights.slice(0, 5); // Limit to 5 insights
};
