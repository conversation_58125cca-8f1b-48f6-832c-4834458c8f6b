/**
 * Navigation Button Component
 * Specialized button component for navigation with active state handling
 * Consolidates navigation button patterns with amber/orange branding
 */

import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface NavigationButtonProps extends BaseComponentProps {
  /** Button text */
  children: React.ReactNode;
  /** Navigation path */
  to?: string;
  /** Whether the button is in active state */
  isActive?: boolean;
  /** Click handler (for non-link buttons) */
  onClick?: () => void;
  /** Icon to display */
  icon?: React.ReactNode;
  /** Icon position */
  iconPosition?: 'left' | 'right';
  /** Button size */
  size?: 'sm' | 'default' | 'lg';
  /** Whether the button is disabled */
  disabled?: boolean;
  /** External link (opens in new tab) */
  external?: boolean;
}

/**
 * NavigationButton component with amber/orange active states
 */
export const NavigationButton: React.FC<NavigationButtonProps> = ({
  children,
  to,
  isActive = false,
  onClick,
  icon,
  iconPosition = 'left',
  size = 'default',
  disabled = false,
  external = false,
  className,
  testId,
  ...props
}) => {
  // Active and inactive styles
  const activeStyles = cn(
    'bg-amber-500 hover:bg-amber-600 text-white',
    'shadow-md'
  );

  const inactiveStyles = cn(
    'text-amber-700 hover:bg-amber-50 hover:text-amber-800',
    'hover:shadow-sm'
  );

  const buttonStyles = cn(
    'transition-all duration-200 ease-in-out',
    'focus:ring-amber-500 focus:ring-offset-2',
    isActive ? activeStyles : inactiveStyles,
    disabled && 'opacity-50 cursor-not-allowed',
    className
  );

  const buttonContent = (
    <div className={cn(
      'flex items-center gap-2',
      iconPosition === 'right' && 'flex-row-reverse'
    )}>
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span>{children}</span>
    </div>
  );

  // If it's a link button
  if (to) {
    if (external) {
      return (
        <a
          href={to}
          target="_blank"
          rel="noopener noreferrer"
          className={cn(
            'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
            size === 'sm' && 'h-9 rounded-md px-3',
            size === 'default' && 'h-10 px-4 py-2',
            size === 'lg' && 'h-11 rounded-md px-8',
            buttonStyles
          )}
          data-testid={testId}
          {...props}
        >
          {buttonContent}
        </a>
      );
    }

    return (
      <Link to={to} className="inline-block">
        <Button
          variant="ghost"
          size={size}
          className={buttonStyles}
          disabled={disabled}
          data-testid={testId}
          {...props}
        >
          {buttonContent}
        </Button>
      </Link>
    );
  }

  // Regular button
  return (
    <Button
      variant="ghost"
      size={size}
      onClick={onClick}
      className={buttonStyles}
      disabled={disabled}
      data-testid={testId}
      {...props}
    >
      {buttonContent}
    </Button>
  );
};

/**
 * NavigationButtonGroup component for grouping navigation buttons
 */
interface NavigationButtonGroupProps extends BaseComponentProps {
  /** Navigation buttons */
  children: React.ReactNode;
  /** Layout direction */
  direction?: 'horizontal' | 'vertical';
  /** Gap between buttons */
  gap?: 'sm' | 'md' | 'lg';
  /** Alignment */
  align?: 'start' | 'center' | 'end';
}

export const NavigationButtonGroup: React.FC<NavigationButtonGroupProps> = ({
  children,
  direction = 'horizontal',
  gap = 'md',
  align = 'start',
  className,
  testId,
  ...props
}) => {
  const gapClasses = {
    sm: 'gap-1',
    md: 'gap-2',
    lg: 'gap-3',
  };

  const alignClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
  };

  return (
    <div
      className={cn(
        'flex',
        direction === 'horizontal' ? 'flex-row' : 'flex-col',
        gapClasses[gap],
        alignClasses[align],
        className
      )}
      data-testid={testId}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * BreadcrumbButton component for breadcrumb navigation
 */
interface BreadcrumbButtonProps extends BaseComponentProps {
  /** Button text */
  children: React.ReactNode;
  /** Navigation path */
  to?: string;
  /** Whether this is the current page */
  isCurrent?: boolean;
  /** Click handler */
  onClick?: () => void;
}

export const BreadcrumbButton: React.FC<BreadcrumbButtonProps> = ({
  children,
  to,
  isCurrent = false,
  onClick,
  className,
  testId,
  ...props
}) => {
  const buttonStyles = cn(
    'text-sm font-medium transition-colors duration-200',
    isCurrent 
      ? 'text-amber-600 cursor-default' 
      : 'text-gray-500 hover:text-amber-600 cursor-pointer',
    className
  );

  if (to && !isCurrent) {
    return (
      <Link 
        to={to} 
        className={buttonStyles}
        data-testid={testId}
        {...props}
      >
        {children}
      </Link>
    );
  }

  if (onClick && !isCurrent) {
    return (
      <button
        onClick={onClick}
        className={buttonStyles}
        data-testid={testId}
        {...props}
      >
        {children}
      </button>
    );
  }

  return (
    <span 
      className={buttonStyles}
      data-testid={testId}
      {...props}
    >
      {children}
    </span>
  );
};
