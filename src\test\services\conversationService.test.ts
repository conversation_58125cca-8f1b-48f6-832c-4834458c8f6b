/**
 * Conversation Service Tests
 * Unit tests for conversation data access layer
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { supabase } from '@/integrations/supabase/client';
import {
  createReflectionConversation,
  getConversationByJournalEntry,
  getConversationMessages,
  createConversationMessage,
  deleteConversation,
  getUserConversationCount,
  bulkDeleteConversations,
} from '@/services/conversationService';
import {
  CreateConversationInput,
  CreateMessageInput,
  ReflectionConversation,
  ConversationMessage,
} from '@/types';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

const mockSupabaseFrom = vi.mocked(supabase.from);

describe('ConversationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('createReflectionConversation', () => {
    it('should create a new conversation successfully', async () => {
      const mockConversationData = {
        id: 'conv-123',
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockConversationData,
          error: null,
        }),
      };

      mockSupabaseFrom.mockReturnValue(mockChain as any);

      const input: CreateConversationInput = {
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
      };

      const result = await createReflectionConversation(input);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        id: 'conv-123',
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });
      expect(mockSupabaseFrom).toHaveBeenCalledWith('reflection_conversations');
    });

    it('should handle database errors', async () => {
      const mockChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database error' },
        }),
      };

      mockSupabaseFrom.mockReturnValue(mockChain as any);

      const input: CreateConversationInput = {
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
      };

      const result = await createReflectionConversation(input);

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Database error');
    });
  });

  describe('getConversationByJournalEntry', () => {
    it('should fetch conversation by journal entry ID', async () => {
      const mockConversationData = {
        id: 'conv-123',
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };

      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockConversationData,
          error: null,
        }),
      };

      mockSupabaseFrom.mockReturnValue(mockChain as any);

      const result = await getConversationByJournalEntry('entry-123', 'user-123');

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('conv-123');
      expect(mockChain.eq).toHaveBeenCalledWith('journal_entry_id', 'entry-123');
      expect(mockChain.eq).toHaveBeenCalledWith('user_id', 'user-123');
    });

    it('should return null when conversation not found', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { code: 'PGRST116' }, // Not found error
        }),
      };

      mockSupabaseFrom.mockReturnValue(mockChain as any);

      const result = await getConversationByJournalEntry('entry-123', 'user-123');

      expect(result.success).toBe(true);
      expect(result.data).toBe(null);
    });
  });

  describe('createConversationMessage', () => {
    it('should create a new message successfully', async () => {
      const mockMessageData = {
        id: 'msg-123',
        conversation_id: 'conv-123',
        sender_type: 'user',
        message_content: 'Hello Amber!',
        message_type: 'text',
        ai_metadata: null,
        created_at: '2024-01-01T00:00:00Z',
      };

      const mockMessageChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: mockMessageData,
          error: null,
        }),
      };

      const mockConversationChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      };

      mockSupabaseFrom
        .mockReturnValueOnce(mockMessageChain as any)
        .mockReturnValueOnce(mockConversationChain as any);

      const input: CreateMessageInput = {
        conversation_id: 'conv-123',
        sender_type: 'user',
        message_content: 'Hello Amber!',
        message_type: 'text',
      };

      const result = await createConversationMessage(input);

      expect(result.success).toBe(true);
      expect(result.data?.message_content).toBe('Hello Amber!');
      expect(mockSupabaseFrom).toHaveBeenCalledWith('reflection_conversation_messages');
      expect(mockSupabaseFrom).toHaveBeenCalledWith('reflection_conversations');
    });
  });

  describe('deleteConversation', () => {
    it('should delete conversation after verifying ownership', async () => {
      const mockVerifyChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { user_id: 'user-123' },
          error: null,
        }),
      };

      const mockDeleteChain = {
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          error: null,
        }),
      };

      mockSupabaseFrom
        .mockReturnValueOnce(mockVerifyChain as any)
        .mockReturnValueOnce(mockDeleteChain as any);

      const result = await deleteConversation('conv-123', 'user-123');

      expect(result.success).toBe(true);
      expect(mockDeleteChain.eq).toHaveBeenCalledWith('id', 'conv-123');
    });

    it('should reject deletion for unauthorized user', async () => {
      const mockVerifyChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { user_id: 'other-user' },
          error: null,
        }),
      };

      mockSupabaseFrom.mockReturnValue(mockVerifyChain as any);

      const result = await deleteConversation('conv-123', 'user-123');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Unauthorized');
    });
  });

  describe('getUserConversationCount', () => {
    it('should return conversation count for user', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue({
          count: 5,
          error: null,
        }),
      };

      mockSupabaseFrom.mockReturnValue(mockChain as any);

      const result = await getUserConversationCount('user-123');

      expect(result.success).toBe(true);
      expect(result.data).toBe(5);
      expect(mockChain.eq).toHaveBeenCalledWith('user_id', 'user-123');
    });
  });

  describe('bulkDeleteConversations', () => {
    it('should delete multiple conversations after verification', async () => {
      const conversationIds = ['conv-1', 'conv-2', 'conv-3'];
      
      const mockVerifyChain = {
        select: vi.fn().mockReturnThis(),
        in: vi.fn().mockResolvedValue({
          data: [
            { id: 'conv-1', user_id: 'user-123' },
            { id: 'conv-2', user_id: 'user-123' },
            { id: 'conv-3', user_id: 'user-123' },
          ],
          error: null,
        }),
      };

      const mockDeleteChain = {
        delete: vi.fn().mockReturnThis(),
        in: vi.fn().mockResolvedValue({
          count: 3,
          error: null,
        }),
      };

      mockSupabaseFrom
        .mockReturnValueOnce(mockVerifyChain as any)
        .mockReturnValueOnce(mockDeleteChain as any);

      const result = await bulkDeleteConversations(conversationIds, 'user-123');

      expect(result.success).toBe(true);
      expect(result.data?.deletedCount).toBe(3);
      expect(mockDeleteChain.in).toHaveBeenCalledWith('id', conversationIds);
    });

    it('should handle empty conversation list', async () => {
      const result = await bulkDeleteConversations([], 'user-123');

      expect(result.success).toBe(true);
      expect(result.data?.deletedCount).toBe(0);
      expect(mockSupabaseFrom).not.toHaveBeenCalled();
    });
  });
});
