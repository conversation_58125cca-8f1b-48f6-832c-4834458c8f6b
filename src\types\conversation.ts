/**
 * Conversation System Types
 * TypeScript interfaces for AI reflection conversations and messaging
 */

import { Json } from '@/integrations/supabase/types';

/**
 * Sender type for conversation messages
 */
export type MessageSenderType = 'user' | 'ai';

/**
 * Message type classification
 */
export type MessageType = 'text' | 'reflection_question' | 'follow_up';

/**
 * AI metadata for conversation messages
 */
export interface AIMessageMetadata {
  /** Detected emotion from the message */
  emotion?: string;
  /** AI confidence score (0-1) */
  confidence?: number;
  /** Response generation time in milliseconds */
  processingTime?: number;
  /** AI model version used */
  modelVersion?: string;
  /** Additional context used for generation */
  context?: {
    /** Previous messages considered */
    messageHistory?: number;
    /** Journal entry context used */
    journalContext?: boolean;
    /** User mood score at time of generation */
    moodScore?: number;
  };
}

/**
 * Core conversation message interface
 */
export interface ConversationMessage {
  /** Unique identifier for the message */
  id: string;
  /** ID of the conversation this message belongs to */
  conversation_id: string;
  /** Who sent the message */
  sender_type: MessageSenderType;
  /** The actual message content */
  message_content: string;
  /** Type of message */
  message_type: MessageType;
  /** AI-specific metadata (only for AI messages) */
  ai_metadata?: AIMessageMetadata | null;
  /** Message creation timestamp */
  created_at: string;
}

/**
 * Conversation thread interface
 */
export interface ReflectionConversation {
  /** Unique identifier for the conversation */
  id: string;
  /** ID of the journal entry this conversation is about */
  journal_entry_id: string;
  /** User who owns this conversation */
  user_id: string;
  /** Conversation creation timestamp */
  created_at: string;
  /** Last update timestamp */
  updated_at: string;
  /** Messages in this conversation (populated via joins) */
  messages?: ConversationMessage[];
}

/**
 * Input for creating a new conversation message
 */
export interface CreateMessageInput {
  /** ID of the conversation */
  conversation_id: string;
  /** Who is sending the message */
  sender_type: MessageSenderType;
  /** The message content */
  message_content: string;
  /** Type of message being sent */
  message_type?: MessageType;
  /** AI metadata (for AI messages) */
  ai_metadata?: AIMessageMetadata;
}

/**
 * Input for creating a new conversation
 */
export interface CreateConversationInput {
  /** ID of the journal entry */
  journal_entry_id: string;
  /** User ID */
  user_id: string;
  /** Optional initial message */
  initial_message?: Omit<CreateMessageInput, 'conversation_id'>;
}

/**
 * AI conversation response structure
 */
export interface AIConversationResponse {
  /** The AI's response message */
  message: string;
  /** Type of response */
  message_type: MessageType;
  /** AI metadata for this response */
  metadata: AIMessageMetadata;
  /** Whether this response was successful */
  success: boolean;
}

/**
 * Input for generating AI conversation responses
 */
export interface AIConversationInput {
  /** The user's message */
  user_message: string;
  /** Conversation history for context */
  conversation_history: ConversationMessage[];
  /** Original journal entry for context */
  journal_entry: {
    title: string;
    content: string;
    emotion: string;
    mood_score: number;
  };
  /** Previous AI reflection data for context */
  initial_reflection?: {
    summary: string;
    emotion: string;
    encouragement: string;
    reflection_question: string;
  };
}

/**
 * Conversation state for UI components
 */
export interface ConversationState {
  /** Current conversation data */
  conversation: ReflectionConversation | null;
  /** All messages in the conversation */
  messages: ConversationMessage[];
  /** Whether messages are currently loading */
  isLoading: boolean;
  /** Whether AI is currently generating a response */
  isGenerating: boolean;
  /** Any error that occurred */
  error: string | null;
  /** Whether the conversation has been initialized */
  isInitialized: boolean;
}

/**
 * Conversation UI actions
 */
export interface ConversationActions {
  /** Send a user message */
  sendMessage: (message: string) => Promise<void>;
  /** Start a new conversation */
  startConversation: (journalEntryId: string) => Promise<void>;
  /** Load conversation history */
  loadConversation: (conversationId: string) => Promise<void>;
  /** Clear current conversation */
  clearConversation: () => void;
  /** Retry last AI response */
  retryLastResponse: () => Promise<void>;
}

/**
 * Formatted conversation message for UI display
 */
export interface FormattedConversationMessage extends ConversationMessage {
  /** Formatted timestamp for display */
  formatted_time: string;
  /** Whether this message is from the current user */
  is_own_message: boolean;
  /** Whether this message is currently being generated */
  is_generating?: boolean;
}

/**
 * Conversation pagination parameters
 */
export interface ConversationPagination {
  /** Number of messages to load per page */
  limit: number;
  /** Offset for pagination */
  offset: number;
  /** Whether there are more messages to load */
  hasMore: boolean;
}

/**
 * Conversation analytics data
 */
export interface ConversationAnalytics {
  /** Total number of messages in conversation */
  total_messages: number;
  /** Number of user messages */
  user_messages: number;
  /** Number of AI messages */
  ai_messages: number;
  /** Average response time for AI messages */
  avg_ai_response_time: number;
  /** Conversation duration in minutes */
  duration_minutes: number;
  /** Most recent activity timestamp */
  last_activity: string;
}
