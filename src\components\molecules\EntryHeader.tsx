/**
 * Entry Header Molecule
 * Component that combines date, emotion badge, and title for journal entries
 */

import { Calendar } from 'lucide-react';
import { EmotionBadge } from '@/components/atoms/EmotionBadge';
import { cn } from '@/utils/utils';
import { formatDateForDisplay } from '@/utils/dateUtils';
import { BaseComponentProps } from '@/types';

interface EntryHeaderProps extends BaseComponentProps {
  /** Entry title */
  title: string;
  /** Entry date */
  date: string;
  /** Entry emotion - can be undefined or any string for defensive programming */
  emotion?: string | null;
  /** Header size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show the full date format */
  fullDate?: boolean;
}

const titleSizeClasses = {
  sm: 'text-lg',
  md: 'text-xl',
  lg: 'text-2xl',
};

const dateSizeClasses = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
};

export const EntryHeader = ({
  title,
  date,
  emotion,
  size = 'md',
  fullDate = true,
  className,
  testId,
}: EntryHeaderProps) => {


  return (
    <div className={cn('space-y-3', className)} data-testid={testId}>
      {/* Date and Emotion Row */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Calendar className="w-4 h-4 text-amber-500" />
          <span className={cn('text-muted-foreground', dateSizeClasses[size])}>
            {formatDateForDisplay(date, fullDate)}
          </span>
        </div>
        <EmotionBadge
          emotion={emotion}
          size={size === 'lg' ? 'md' : 'sm'}
          showEmoji={size === 'lg'}
        />
      </div>

      {/* Title */}
      <h2 className={cn('font-semibold', titleSizeClasses[size])}>{title}</h2>
    </div>
  );
};
