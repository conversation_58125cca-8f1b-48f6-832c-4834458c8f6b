/**
 * Authentication Service
 * Centralized service for all authentication operations
 */

import { supabase } from '@/integrations/supabase/client';
import {
  AuthResponse,
  AuthError,
  AuthErrorType,
  SignUpFormData,
  SignInFormData,
  PasswordResetFormData,
  PasswordUpdateFormData,
  UserProfile,
  ApiResponse,
} from '@/types';
import { User, Session, AuthError as SupabaseAuthError } from '@supabase/supabase-js';

/**
 * Maps Supabase auth errors to our standardized error types
 */
const mapAuthError = (error: SupabaseAuthError | Error): AuthError => {
  const message = error.message.toLowerCase();

  if (
    message.includes('invalid login credentials') ||
    message.includes('invalid email or password')
  ) {
    return {
      type: 'invalid_credentials',
      message: 'Invalid email or password. Please check your credentials and try again.',
      details: error.message,
    };
  }

  if (message.includes('email not confirmed')) {
    return {
      type: 'email_not_confirmed',
      message: 'Please check your email and click the confirmation link before signing in.',
      details: error.message,
    };
  }

  if (message.includes('user not found')) {
    return {
      type: 'user_not_found',
      message: 'No account found with this email address.',
      details: error.message,
    };
  }

  if (message.includes('password') && (message.includes('weak') || message.includes('short'))) {
    return {
      type: 'weak_password',
      message:
        'Password must be at least 8 characters long and contain a mix of letters and numbers.',
      details: error.message,
    };
  }

  if (message.includes('already registered') || message.includes('already exists')) {
    return {
      type: 'email_already_exists',
      message: 'An account with this email already exists. Try signing in instead.',
      details: error.message,
    };
  }

  if (message.includes('network') || message.includes('fetch')) {
    return {
      type: 'network_error',
      message: 'Network error. Please check your connection and try again.',
      details: error.message,
    };
  }

  return {
    type: 'unknown_error',
    message: 'An unexpected error occurred. Please try again.',
    details: error.message,
  };
};

/**
 * Sign up a new user with email and password
 */
export const signUp = async (formData: SignUpFormData): Promise<AuthResponse<User>> => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email: formData.email,
      password: formData.password,
      options: {
        emailRedirectTo: `${window.location.origin}/`,
        data: {
          full_name: formData.fullName || '',
        },
      },
    });

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    if (!data.user) {
      return {
        success: false,
        error: {
          type: 'unknown_error',
          message: 'Failed to create user account.',
        },
      };
    }

    return {
      success: true,
      data: data.user,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Sign in user with email and password
 */
export const signIn = async (
  formData: SignInFormData
): Promise<AuthResponse<{ user: User; session: Session }>> => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: formData.email,
      password: formData.password,
    });

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    if (!data.user || !data.session) {
      return {
        success: false,
        error: {
          type: 'unknown_error',
          message: 'Failed to sign in.',
        },
      };
    }

    return {
      success: true,
      data: {
        user: data.user,
        session: data.session,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Sign in with Google OAuth
 */
export const signInWithGoogle = async (): Promise<AuthResponse<void>> => {
  try {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/`,
      },
    });

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Sign out current user
 */
export const signOut = async (): Promise<AuthResponse<void>> => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Reset password for user
 */
export const resetPassword = async (
  formData: PasswordResetFormData
): Promise<AuthResponse<void>> => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(formData.email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Update user password
 */
export const updatePassword = async (
  formData: PasswordUpdateFormData
): Promise<AuthResponse<User>> => {
  try {
    const { data, error } = await supabase.auth.updateUser({
      password: formData.newPassword,
    });

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    if (!data.user) {
      return {
        success: false,
        error: {
          type: 'unknown_error',
          message: 'Failed to update password.',
        },
      };
    }

    return {
      success: true,
      data: data.user,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Get current session
 */
export const getCurrentSession = async (): Promise<AuthResponse<Session>> => {
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    if (!data.session) {
      return {
        success: false,
        error: {
          type: 'user_not_found',
          message: 'No active session found.',
        },
      };
    }

    return {
      success: true,
      data: data.session,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};

/**
 * Get current user
 */
export const getCurrentUser = async (): Promise<AuthResponse<User>> => {
  try {
    const { data, error } = await supabase.auth.getUser();

    if (error) {
      return {
        success: false,
        error: mapAuthError(error),
      };
    }

    if (!data.user) {
      return {
        success: false,
        error: {
          type: 'user_not_found',
          message: 'No authenticated user found.',
        },
      };
    }

    return {
      success: true,
      data: data.user,
    };
  } catch (error) {
    return {
      success: false,
      error: mapAuthError(error as Error),
    };
  }
};
