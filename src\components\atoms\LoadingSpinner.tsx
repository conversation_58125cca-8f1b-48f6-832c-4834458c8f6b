/**
 * Loading Spinner Atom
 * Reusable loading spinner component
 */

import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface LoadingSpinnerProps extends BaseComponentProps {
  /** Size of the spinner */
  size?: 'sm' | 'md' | 'lg';
  /** Color variant */
  variant?: 'primary' | 'secondary' | 'accent';
  /** Loading message */
  message?: string;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-8 w-8',
  lg: 'h-12 w-12',
};

const variantClasses = {
  primary: 'border-amber-500',
  secondary: 'border-gray-500',
  accent: 'border-blue-500',
};

export const LoadingSpinner = ({
  size = 'md',
  variant = 'primary',
  message,
  className,
  testId,
}: LoadingSpinnerProps) => {
  return (
    <div
      className={cn('flex flex-col items-center justify-center', className)}
      data-testid={testId}
    >
      <div
        className={cn(
          'animate-spin rounded-full border-b-2',
          sizeClasses[size],
          variantClasses[variant]
        )}
      />
      {message && <p className="mt-2 text-sm text-muted-foreground">{message}</p>}
    </div>
  );
};
