/**
 * Environment Configuration Types
 * Centralized type definitions for environment variables and configuration
 */

/**
 * Environment types
 */
export type Environment = 'development' | 'staging' | 'production';

/**
 * Environment variable configuration
 */
export interface EnvironmentVariables {
  // Supabase Configuration
  VITE_SUPABASE_URL: string;
  VITE_SUPABASE_ANON_KEY: string;

  // AI Configuration
  VITE_GEMINI_API_KEY: string;
  VITE_LOCAL_LLM_ENDPOINT?: string;

  // Application Configuration
  VITE_APP_NAME?: string;
  VITE_APP_VERSION?: string;
  VITE_APP_ENVIRONMENT?: Environment;

  // Feature Flags
  VITE_ENABLE_AI_FEATURES?: string;
  VITE_ENABLE_ANALYTICS?: string;
  VITE_ENABLE_DEBUG_MODE?: string;
  VITE_ENABLE_SERVICE_WORKER?: string;

  // API Configuration
  VITE_API_TIMEOUT?: string;
  VITE_API_RETRY_ATTEMPTS?: string;

  // Security Configuration
  VITE_ENABLE_HTTPS_ONLY?: string;
  VITE_ENABLE_STRICT_CSP?: string;
}

/**
 * Parsed and validated environment configuration
 */
export interface EnvironmentConfig {
  // Environment info
  environment: Environment;
  isDevelopment: boolean;
  isProduction: boolean;
  isStaging: boolean;

  // Application info
  app: {
    name: string;
    version: string;
  };

  // Supabase configuration
  supabase: {
    url: string;
    anonKey: string;
  };

  // AI configuration
  ai: {
    geminiApiKey: string;
    enabled: boolean;
    timeout: number;
    retryAttempts: number;
  };

  // Feature flags
  features: {
    aiEnabled: boolean;
    analyticsEnabled: boolean;
    debugMode: boolean;
    serviceWorkerEnabled: boolean;
  };

  // Security settings
  security: {
    httpsOnly: boolean;
    strictCSP: boolean;
  };
}

/**
 * Environment validation result
 */
export interface EnvironmentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingRequired: string[];
  missingOptional: string[];
}

/**
 * Environment variable definition for validation
 */
export interface EnvironmentVariableDefinition {
  key: keyof EnvironmentVariables;
  required: boolean;
  type: 'string' | 'number' | 'boolean' | 'url' | 'email';
  description: string;
  defaultValue?: string;
  validator?: (value: string) => boolean;
  transformer?: (value: string) => any;
}

/**
 * Configuration error types
 */
export type ConfigurationErrorType =
  | 'MISSING_REQUIRED_VAR'
  | 'INVALID_FORMAT'
  | 'INVALID_VALUE'
  | 'VALIDATION_FAILED'
  | 'ENVIRONMENT_MISMATCH';

/**
 * Configuration error interface
 */
export interface ConfigurationError {
  type: ConfigurationErrorType;
  variable: string;
  message: string;
  suggestion?: string;
}
