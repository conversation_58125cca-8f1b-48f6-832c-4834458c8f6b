/**
 * Journal Entry Card Organism
 * Complete journal entry display component using atomic design principles
 * Optimized with React.memo and performance monitoring
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EntryHeader } from '@/components/molecules/EntryHeader';
import { EntryContent } from '@/components/molecules/EntryContent';
import { AIReflection } from '@/components/features/AIReflection';
import { cn } from '@/utils/utils';
import { FormattedJournalEntry, BaseComponentProps } from '@/types';
import { useStableCallback, useRenderCount } from '@/utils/performance.utils';

interface JournalEntryCardProps extends BaseComponentProps {
  /** Journal entry data */
  entry: FormattedJournalEntry;
  /** Whether to show action buttons */
  showActions?: boolean;
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show AI reflection */
  showAIReflection?: boolean;
  /** Maximum lines for content preview */
  contentMaxLines?: number;
  /** Action handlers */
  onEdit?: (entryId: string) => void;
  onDelete?: (entryId: string) => void;
  onView?: (entryId: string) => void;
}

const JournalEntryCardComponent = ({
  entry,
  showActions = false,
  size = 'md',
  showAIReflection = true,
  contentMaxLines,
  onEdit,
  onDelete,
  onView,
  className,
  testId,
}: JournalEntryCardProps) => {
  // Performance monitoring in development - re-enabled for testing
  useRenderCount('JournalEntryCard');

  // Memoize computed values
  const hasAIReflection = React.useMemo(
    () => showAIReflection && (entry.ai_summary || entry.ai_encouragement || entry.ai_reflection),
    [showAIReflection, entry.ai_summary, entry.ai_encouragement, entry.ai_reflection]
  );

  // Stable callback handlers to prevent unnecessary re-renders
  const handleEdit = useStableCallback(
    () => {
      if (onEdit) onEdit(entry.id);
    },
    [onEdit, entry.id],
    'JournalEntryCard.handleEdit'
  );

  const handleDelete = useStableCallback(
    () => {
      if (onDelete) onDelete(entry.id);
    },
    [onDelete, entry.id],
    'JournalEntryCard.handleDelete'
  );

  const handleView = useStableCallback(
    () => {
      if (onView) onView(entry.id);
    },
    [onView, entry.id],
    'JournalEntryCard.handleView'
  );

  return (
    <Card
      className={cn(
        'glass-effect hover:warm-glow transition-all duration-300',
        onView && 'cursor-pointer',
        className
      )}
      data-testid={testId}
      onClick={onView ? handleView : undefined}
    >
      <CardHeader>
        <EntryHeader title={entry.title} date={entry.date} emotion={entry.emotion} size={size} />
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Entry Content */}
        <EntryContent
          content={entry.content}
          moodScore={entry.mood_score}
          size={size}
          maxLines={contentMaxLines}
          expandable={!!contentMaxLines}
        />

        {/* AI Reflection */}
        {hasAIReflection && (
          <div className="pt-4 border-t">
            <AIReflection
              summary={entry.ai_summary}
              emotion={entry.ai_emotion}
              encouragement={entry.ai_encouragement}
              reflection_question={entry.ai_reflection_question}
              reflection={entry.ai_reflection}
              journalEntry={entry}
              showConversation={true}
              className="border-0 bg-gradient-to-br from-amber-25 to-orange-25"
            />
          </div>
        )}

        {/* Action Buttons */}
        {showActions && (onEdit || onDelete) && (
          <div className="flex gap-2 pt-4 border-t">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={e => {
                  e.stopPropagation();
                  handleEdit();
                }}
              >
                Edit
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={e => {
                  e.stopPropagation();
                  handleDelete();
                }}
                className="text-red-600 hover:text-red-700 hover:border-red-300"
              >
                Delete
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Memoized component with custom comparison
export const JournalEntryCard = React.memo(JournalEntryCardComponent, (prevProps, nextProps) => {
  // Custom comparison for better performance
  return (
    prevProps.entry.id === nextProps.entry.id &&
    prevProps.entry.updated_at === nextProps.entry.updated_at &&
    prevProps.showActions === nextProps.showActions &&
    prevProps.size === nextProps.size &&
    prevProps.showAIReflection === nextProps.showAIReflection &&
    prevProps.contentMaxLines === nextProps.contentMaxLines &&
    prevProps.className === nextProps.className
  );
});
