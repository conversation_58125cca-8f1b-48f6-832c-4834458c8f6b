/**
 * Journal Entry Card Organism
 * Complete journal entry display component using atomic design principles
 * Optimized with React.memo and performance monitoring
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Edit3, Trash2 } from 'lucide-react';
import { EntryHeader } from '@/components/molecules/EntryHeader';
import { EntryContent } from '@/components/molecules/EntryContent';
import { AIReflection } from '@/components/features/AIReflection';
import { cn } from '@/utils/utils';
import { FormattedJournalEntry, BaseComponentProps } from '@/types';
import { useStableCallback, useRenderCount } from '@/utils/performance.utils';

interface JournalEntryCardProps extends BaseComponentProps {
  /** Journal entry data */
  entry: FormattedJournalEntry;
  /** Whether to show action buttons */
  showActions?: boolean;
  /** Size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show AI reflection */
  showAIReflection?: boolean;
  /** Maximum lines for content preview */
  contentMaxLines?: number;
  /** Action handlers */
  onEdit?: (entryId: string) => void;
  onDelete?: (entryId: string) => void;
  onView?: (entryId: string) => void;
}

const JournalEntryCardComponent = ({
  entry,
  showActions = false,
  size = 'md',
  showAIReflection = true,
  contentMaxLines,
  onEdit,
  onDelete,
  onView,
  className,
  testId,
}: JournalEntryCardProps) => {
  // Performance monitoring in development - re-enabled for testing
  useRenderCount('JournalEntryCard');

  // Memoize computed values
  const hasAIReflection = React.useMemo(
    () => showAIReflection && (entry.ai_summary || entry.ai_encouragement || entry.ai_reflection),
    [showAIReflection, entry.ai_summary, entry.ai_encouragement, entry.ai_reflection]
  );

  // Stable callback handlers to prevent unnecessary re-renders
  const handleEdit = useStableCallback(
    () => {
      if (onEdit) onEdit(entry.id);
    },
    [onEdit, entry.id],
    'JournalEntryCard.handleEdit'
  );

  const handleDelete = useStableCallback(
    () => {
      if (onDelete) onDelete(entry.id);
    },
    [onDelete, entry.id],
    'JournalEntryCard.handleDelete'
  );

  const handleView = useStableCallback(
    () => {
      if (onView) onView(entry.id);
    },
    [onView, entry.id],
    'JournalEntryCard.handleView'
  );

  return (
    <Card
      className={cn(
        'card-modern hover-lift hover-glow transition-all duration-300 ease-out',
        'border-elegant bg-white/90 backdrop-blur-md',
        onView && 'cursor-pointer hover:border-amber-300/60',
        className
      )}
      data-testid={testId}
      onClick={onView ? handleView : undefined}
    >
      <CardHeader className="section-padding pb-4">
        <EntryHeader title={entry.title} date={entry.date} emotion={entry.emotion} size={size} />
      </CardHeader>

      <CardContent className="section-padding pt-0 content-spacing">
        {/* Entry Content */}
        <EntryContent
          content={entry.content}
          moodScore={entry.mood_score}
          size={size}
          maxLines={contentMaxLines}
          expandable={!!contentMaxLines}
        />

        {/* AI Reflection */}
        {hasAIReflection && (
          <div className="pt-6 border-t border-amber-200/30">
            <AIReflection
              summary={entry.ai_summary}
              emotion={entry.ai_emotion}
              encouragement={entry.ai_encouragement}
              reflection_question={entry.ai_reflection_question}
              reflection={entry.ai_reflection}
              journalEntry={entry}
              showConversation={true}
              className="border-0 bg-gradient-to-br from-amber-25 to-orange-25 rounded-xl"
            />
          </div>
        )}

        {/* Action Buttons */}
        {showActions && (onEdit || onDelete) && (
          <div className="flex gap-3 pt-6 border-t border-amber-200/30">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={e => {
                  e.stopPropagation();
                  handleEdit();
                }}
                className="flex-1 btn-modern text-amber-700 border-amber-200/60 hover:bg-amber-50 hover:border-amber-300 focus-modern"
              >
                <Edit3 className="w-3 h-3 mr-2" />
                Edit
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={e => {
                  e.stopPropagation();
                  handleDelete();
                }}
                className="flex-1 btn-modern text-red-600 border-red-200/60 hover:bg-red-50 hover:border-red-300 focus-modern"
              >
                <Trash2 className="w-3 h-3 mr-2" />
                Delete
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Memoized component with custom comparison
export const JournalEntryCard = React.memo(JournalEntryCardComponent, (prevProps, nextProps) => {
  // Custom comparison for better performance
  return (
    prevProps.entry.id === nextProps.entry.id &&
    prevProps.entry.updated_at === nextProps.entry.updated_at &&
    prevProps.showActions === nextProps.showActions &&
    prevProps.size === nextProps.size &&
    prevProps.showAIReflection === nextProps.showAIReflection &&
    prevProps.contentMaxLines === nextProps.contentMaxLines &&
    prevProps.className === nextProps.className
  );
});
