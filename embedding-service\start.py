#!/usr/bin/env python3
"""
AmberGlow Embedding Service Startup Script

This script provides a convenient way to start the embedding service with
proper environment setup and error handling.
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def check_virtual_environment():
    """Check if virtual environment is activated."""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment is activated")
        return True
    else:
        print("⚠️  Warning: Virtual environment not detected")
        print("It's recommended to run this service in a virtual environment")
        return True  # Don't block, just warn

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ['flask', 'flask_cors', 'sentence_transformers']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    if missing_packages:
        print("\n📦 Install missing dependencies with:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def start_service():
    """Start the embedding service."""
    print("\n🚀 Starting AmberGlow Embedding Service...")
    print("Press Ctrl+C to stop the service")
    print("-" * 50)
    
    try:
        # Import and run the Flask app
        from app import app, initialize_service
        
        # Initialize the service
        if not initialize_service():
            print("❌ Failed to initialize service")
            return False
        
        # Start the Flask app
        host = os.getenv('FLASK_HOST', '0.0.0.0')  # Bind to all interfaces
        port = int(os.getenv('FLASK_PORT', 5005))
        debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        print(f"🌐 Service URL: http://{host}:{port}")
        print(f"🔍 Health check: http://{host}:{port}/health")
        print(f"📡 Embed endpoint: http://{host}:{port}/embed")
        
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 Service stopped by user")
        return True
    except Exception as e:
        print(f"\n❌ Error starting service: {e}")
        return False

def main():
    """Main startup function."""
    print("🔧 AmberGlow Embedding Service Startup")
    print("=" * 40)
    
    # Check system requirements
    if not check_python_version():
        sys.exit(1)
    
    check_virtual_environment()
    
    if not check_dependencies():
        sys.exit(1)
    
    # Start the service
    success = start_service()
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
