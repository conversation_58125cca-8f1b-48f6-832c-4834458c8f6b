/**
 * User Profile Service
 * Centralized service for user profile operations
 */

import { supabase } from '@/integrations/supabase/client';
import {
  UserProfile,
  ProfileUpdateFormData,
  ApiResponse,
  ApiError,
  supabaseToUserProfile,
  userProfileToSupabaseUpdate,
} from '@/types';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * Maps Supabase database errors to our standardized error types
 */
const mapDatabaseError = (error: PostgrestError | Error): ApiError => {
  const message = error.message.toLowerCase();

  if (message.includes('permission') || message.includes('unauthorized')) {
    return {
      code: 'PERMISSION_DENIED',
      message: 'You do not have permission to perform this action.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('not found') || message.includes('no rows')) {
    return {
      code: 'NOT_FOUND',
      message: 'User profile not found.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('unique') || message.includes('duplicate')) {
    return {
      code: 'DUPLICATE_ERROR',
      message: 'This information is already in use.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('validation') || message.includes('invalid')) {
    return {
      code: 'VALIDATION_ERROR',
      message: 'Invalid data provided. Please check your input.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('network') || message.includes('timeout')) {
    return {
      code: 'NETWORK_ERROR',
      message: 'Network error. Please check your connection and try again.',
      details: error.message,
      retryable: true,
    };
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: 'An unexpected error occurred. Please try again.',
    details: error.message,
    retryable: true,
  };
};

/**
 * Get user profile by user ID
 */
export const getUserProfile = async (userId: string): Promise<ApiResponse<UserProfile>> => {
  try {
    const { data, error } = await supabase.from('profiles').select('*').eq('id', userId).single();

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    if (!data) {
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'User profile not found.',
          retryable: false,
        },
      };
    }

    const userProfile = supabaseToUserProfile(data);

    return {
      success: true,
      data: userProfile,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Create user profile
 */
export const createUserProfile = async (
  userId: string,
  profileData: Partial<UserProfile>
): Promise<ApiResponse<UserProfile>> => {
  try {
    const insertData = {
      id: userId,
      email: profileData.email || '',
      full_name: profileData.full_name || null,
      avatar_url: profileData.avatar_url || null,
      timezone: profileData.timezone || null,
      language: profileData.language || null,
    };

    const { data, error } = await supabase.from('profiles').insert(insertData).select().single();

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    if (!data) {
      return {
        success: false,
        error: {
          code: 'CREATE_FAILED',
          message: 'Failed to create user profile.',
          retryable: true,
        },
      };
    }

    const userProfile = supabaseToUserProfile(data);

    return {
      success: true,
      data: userProfile,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (
  userId: string,
  updateData: ProfileUpdateFormData
): Promise<ApiResponse<UserProfile>> => {
  try {
    const supabaseUpdateData = userProfileToSupabaseUpdate(updateData);

    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...supabaseUpdateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    if (!data) {
      return {
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: 'Failed to update user profile.',
          retryable: true,
        },
      };
    }

    const userProfile = supabaseToUserProfile(data);

    return {
      success: true,
      data: userProfile,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Delete user profile
 */
export const deleteUserProfile = async (userId: string): Promise<ApiResponse<void>> => {
  try {
    const { error } = await supabase.from('profiles').delete().eq('id', userId);

    if (error) {
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    return {
      success: true,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Update user avatar
 */
export const updateUserAvatar = async (
  userId: string,
  avatarFile: File
): Promise<ApiResponse<{ avatarUrl: string }>> => {
  try {
    // Upload avatar to Supabase storage
    const fileExt = avatarFile.name.split('.').pop();
    const fileName = `${userId}-${Date.now()}.${fileExt}`;
    const filePath = `avatars/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(filePath, avatarFile);

    if (uploadError) {
      return {
        success: false,
        error: {
          code: 'UPLOAD_FAILED',
          message: 'Failed to upload avatar image.',
          details: uploadError.message,
          retryable: true,
        },
      };
    }

    // Get public URL for the uploaded file
    const { data: urlData } = supabase.storage.from('avatars').getPublicUrl(filePath);

    const avatarUrl = urlData.publicUrl;

    // Update user profile with new avatar URL
    const profileUpdateResult = await updateUserProfile(userId, {
      avatar_url: avatarUrl,
    });

    if (!profileUpdateResult.success) {
      return {
        success: false,
        error: profileUpdateResult.error,
      };
    }

    return {
      success: true,
      data: { avatarUrl },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};

/**
 * Check if user profile exists
 */
export const checkUserProfileExists = async (userId: string): Promise<ApiResponse<boolean>> => {
  try {
    const { data, error } = await supabase.from('profiles').select('id').eq('id', userId).single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116 is "not found" error
      return {
        success: false,
        error: mapDatabaseError(error),
      };
    }

    return {
      success: true,
      data: !!data,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: mapDatabaseError(error as Error),
    };
  }
};
