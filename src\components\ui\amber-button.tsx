/**
 * Amber <PERSON> Component
 * Specialized button component with consistent amber/orange branding
 * Consolidates the repetitive amber button styling patterns
 */

import React from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { Save } from 'lucide-react';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface AmberButtonProps extends Omit<ButtonProps, 'variant'>, BaseComponentProps {
  /** Button variant with amber/orange styling */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  /** Whether the button is in loading state */
  isLoading?: boolean;
  /** Loading text to display */
  loadingText?: string;
  /** Icon to display before text */
  icon?: React.ReactNode;
  /** Icon position */
  iconPosition?: 'left' | 'right';
  /** Whether to show full width */
  fullWidth?: boolean;
}

/**
 * AmberButton component with consistent amber/orange branding
 */
export const AmberButton: React.FC<AmberButtonProps> = ({
  variant = 'primary',
  isLoading = false,
  loadingText = 'Loading...',
  icon,
  iconPosition = 'left',
  fullWidth = false,
  children,
  className,
  disabled,
  testId,
  ...props
}) => {
  // Amber/orange variant styles
  const amberVariantStyles = {
    primary: cn(
      'bg-amber-500 hover:bg-amber-600 text-white',
      'focus:ring-amber-500 focus:ring-offset-2',
      'disabled:bg-amber-300 disabled:hover:bg-amber-300'
    ),
    secondary: cn(
      'bg-amber-100 hover:bg-amber-200 text-amber-800',
      'focus:ring-amber-500 focus:ring-offset-2',
      'disabled:bg-amber-50 disabled:text-amber-400'
    ),
    outline: cn(
      'border-amber-200 text-amber-700 hover:bg-amber-50 hover:text-amber-800',
      'focus:ring-amber-500 focus:border-amber-500',
      'disabled:border-amber-100 disabled:text-amber-400'
    ),
    ghost: cn(
      'text-amber-700 hover:bg-amber-50 hover:text-amber-800',
      'focus:ring-amber-500',
      'disabled:text-amber-400'
    ),
    destructive: cn(
      'bg-red-600 hover:bg-red-700 text-white',
      'focus:ring-red-500 focus:ring-offset-2',
      'disabled:bg-red-300 disabled:hover:bg-red-300'
    ),
  };

  // Loading spinner component
  const LoadingSpinner = () => (
    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
  );

  // Render icon with proper spacing
  const renderIcon = () => {
    if (isLoading) {
      return <LoadingSpinner />;
    }
    return icon;
  };

  const buttonContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center gap-2">
          <LoadingSpinner />
          <span>{loadingText}</span>
        </div>
      );
    }

    if (icon && children) {
      return (
        <div className={cn(
          'flex items-center gap-2',
          iconPosition === 'right' && 'flex-row-reverse'
        )}>
          {renderIcon()}
          <span>{children}</span>
        </div>
      );
    }

    if (icon && !children) {
      return renderIcon();
    }

    return children;
  };

  return (
    <Button
      className={cn(
        amberVariantStyles[variant],
        fullWidth && 'w-full',
        'transition-all duration-200 ease-in-out',
        'hover:shadow-md hover:scale-[1.02]',
        'active:scale-[0.98]',
        className
      )}
      disabled={disabled || isLoading}
      data-testid={testId}
      {...props}
    >
      {buttonContent()}
    </Button>
  );
};

/**
 * IconButton component for icon-only buttons with amber styling
 */
interface IconButtonProps extends Omit<AmberButtonProps, 'children' | 'icon'> {
  /** Icon to display */
  icon: React.ReactNode;
  /** Accessible label for screen readers */
  'aria-label': string;
}

export const AmberIconButton: React.FC<IconButtonProps> = ({
  icon,
  size = 'icon',
  variant = 'ghost',
  ...props
}) => {
  return (
    <AmberButton
      size={size}
      variant={variant}
      icon={icon}
      {...props}
    />
  );
};

/**
 * LoadingButton component that automatically shows loading state
 */
interface LoadingButtonProps extends Omit<AmberButtonProps, 'isLoading'> {
  /** Whether the button is in loading state */
  loading: boolean;
}

export const AmberLoadingButton: React.FC<LoadingButtonProps> = ({
  loading,
  ...props
}) => {
  return (
    <AmberButton
      isLoading={loading}
      {...props}
    />
  );
};

/**
 * ButtonPair component for Cancel + Action button combinations
 */
interface ButtonPairProps extends BaseComponentProps {
  /** Cancel button text */
  cancelText?: string;
  /** Action button text */
  actionText: string;
  /** Cancel button click handler */
  onCancel: () => void;
  /** Action button click handler */
  onAction: () => void;
  /** Whether the action is in loading state */
  isLoading?: boolean;
  /** Action button variant */
  actionVariant?: AmberButtonProps['variant'];
  /** Whether buttons should be equal width */
  equalWidth?: boolean;
  /** Gap between buttons */
  gap?: 'sm' | 'md' | 'lg';
  /** Whether to reverse button order (action first) */
  reverse?: boolean;
  /** Button size variant */
  size?: 'sm' | 'default' | 'lg';
  /** Custom styling variant for form-like appearance */
  variant?: 'default' | 'form';
}

export const AmberButtonPair: React.FC<ButtonPairProps> = ({
  cancelText = 'Cancel',
  actionText,
  onCancel,
  onAction,
  isLoading = false,
  actionVariant = 'primary',
  equalWidth = true,
  gap = 'md',
  reverse = false,
  size = 'default',
  variant = 'default',
  className,
  testId,
  ...props
}) => {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-3',
    lg: 'gap-4',
  };

  // Form variant uses custom styling to match JournalEntryForm
  if (variant === 'form') {
    const buttons = [
      <button
        key="cancel"
        type="button"
        onClick={onCancel}
        disabled={isLoading}
        className={cn(
          'flex-1 px-4 py-2 border border-gray-300 rounded-l-md border-r-0',
          'text-gray-700 bg-white hover:bg-gray-50',
          'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'transition-all duration-200 ease-in-out',
          'hover:shadow-md hover:scale-[1.02]',
          'active:scale-[0.98]'
        )}
        data-testid={testId ? `${testId}-cancel` : undefined}
      >
        {cancelText}
      </button>,
      <button
        key="action"
        type="submit"
        onClick={onAction}
        disabled={isLoading}
        className={cn(
          'flex-1 px-4 py-2 rounded-r-md border border-amber-500',
          'bg-amber-500 hover:bg-amber-600 text-white',
          'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'transition-all duration-200 ease-in-out',
          'hover:shadow-md hover:scale-[1.02]',
          'active:scale-[0.98]'
        )}
        data-testid={testId ? `${testId}-action` : undefined}
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
            Saving...
          </div>
        ) : (
          <div className="flex items-center justify-center gap-2">
            <Save className="w-4 h-4" />
            <span>{actionText}</span>
          </div>
        )}
      </button>,
    ];

    return (
      <div
        className={cn(
          'flex w-full', // Remove gap and ensure full width
          className
        )}
        data-testid={testId}
        {...props}
      >
        {reverse ? buttons.reverse() : buttons}
      </div>
    );
  }

  // Default variant uses AmberButton components
  const buttons = [
    <AmberButton
      key="cancel"
      variant="outline"
      size={size}
      onClick={onCancel}
      disabled={isLoading}
      className={equalWidth ? 'flex-1' : undefined}
      {...(testId && { testId: `${testId}-cancel` })}
    >
      {cancelText}
    </AmberButton>,
    <AmberButton
      key="action"
      variant={actionVariant}
      size={size}
      onClick={onAction}
      isLoading={isLoading}
      className={equalWidth ? 'flex-1' : undefined}
      {...(testId && { testId: `${testId}-action` })}
    >
      {actionText}
    </AmberButton>,
  ];

  return (
    <div 
      className={cn(
        'flex',
        gapClasses[gap],
        className
      )}
      data-testid={testId}
      {...props}
    >
      {reverse ? buttons.reverse() : buttons}
    </div>
  );
};
