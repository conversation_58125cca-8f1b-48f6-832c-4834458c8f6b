/**
 * Conversation Hooks Tests
 * Unit tests for conversation-related React hooks
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import {
  useConversationByJournalEntry,
  useConversationMessages,
  useCreateConversation,
  useSendMessage,
  useConversationManager,
} from '@/hooks/useConversation';
import { AuthContext } from '@/contexts/AuthContext';
import { JournalEntry, ReflectionConversation, ConversationMessage } from '@/types';

// Mock services
vi.mock('@/services/conversationService', () => ({
  getConversationByJournalEntry: vi.fn(),
  getConversationMessages: vi.fn(),
  createReflectionConversation: vi.fn(),
  createConversationMessage: vi.fn(),
}));

vi.mock('@/services/aiConversationService', () => ({
  generateAIConversationResponse: vi.fn(),
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  aud: 'authenticated',
  role: 'authenticated',
  email_confirmed_at: '2024-01-01T00:00:00Z',
  phone: '',
  confirmed_at: '2024-01-01T00:00:00Z',
  last_sign_in_at: '2024-01-01T00:00:00Z',
  app_metadata: {},
  user_metadata: {},
  identities: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockJournalEntry: JournalEntry = {
  id: 'entry-123',
  user_id: 'user-123',
  title: 'Test Entry',
  content: 'This is a test journal entry.',
  emotion: 'happy',
  mood_score: 7,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockConversation: ReflectionConversation = {
  id: 'conv-123',
  journal_entry_id: 'entry-123',
  user_id: 'user-123',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockMessages: ConversationMessage[] = [
  {
    id: 'msg-1',
    conversation_id: 'conv-123',
    sender_type: 'ai',
    message_content: 'How are you feeling?',
    message_type: 'reflection_question',
    ai_metadata: null,
    created_at: '2024-01-01T00:00:00Z',
  },
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  const authContextValue = {
    user: mockUser,
    loading: false,
    signIn: vi.fn(),
    signUp: vi.fn(),
    signOut: vi.fn(),
    resetPassword: vi.fn(),
    updatePassword: vi.fn(),
    updateProfile: vi.fn(),
  };

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <AuthContext.Provider value={authContextValue}>
        {children}
      </AuthContext.Provider>
    </QueryClientProvider>
  );
};

describe('useConversation hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('useConversationByJournalEntry', () => {
    it('should fetch conversation by journal entry ID', async () => {
      const { getConversationByJournalEntry } = require('@/services/conversationService');
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: true,
        data: mockConversation,
      });

      const { result } = renderHook(
        () => useConversationByJournalEntry('entry-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockConversation);
      expect(getConversationByJournalEntry).toHaveBeenCalledWith('entry-123', 'user-123');
    });

    it('should handle conversation not found', async () => {
      const { getConversationByJournalEntry } = require('@/services/conversationService');
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: true,
        data: null,
      });

      const { result } = renderHook(
        () => useConversationByJournalEntry('entry-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBe(null);
    });

    it('should handle service errors', async () => {
      const { getConversationByJournalEntry } = require('@/services/conversationService');
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: false,
        error: { message: 'Service error', code: 'SERVICE_ERROR' },
      });

      const { result } = renderHook(
        () => useConversationByJournalEntry('entry-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toContain('Service error');
    });
  });

  describe('useConversationMessages', () => {
    it('should fetch messages for a conversation', async () => {
      const { getConversationMessages } = require('@/services/conversationService');
      vi.mocked(getConversationMessages).mockResolvedValue({
        success: true,
        data: mockMessages,
      });

      const { result } = renderHook(
        () => useConversationMessages('conv-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockMessages);
      expect(getConversationMessages).toHaveBeenCalledWith('conv-123', {
        limit: 50,
        offset: 0,
        orderBy: 'created_at',
        ascending: true,
      });
    });

    it('should not fetch when conversation ID is null', () => {
      const { getConversationMessages } = require('@/services/conversationService');

      const { result } = renderHook(
        () => useConversationMessages(null),
        { wrapper: createWrapper() }
      );

      expect(result.current.data).toEqual([]);
      expect(getConversationMessages).not.toHaveBeenCalled();
    });

    it('should respect custom options', async () => {
      const { getConversationMessages } = require('@/services/conversationService');
      vi.mocked(getConversationMessages).mockResolvedValue({
        success: true,
        data: mockMessages,
      });

      const { result } = renderHook(
        () => useConversationMessages('conv-123', { limit: 20, offset: 10 }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(getConversationMessages).toHaveBeenCalledWith('conv-123', {
        limit: 20,
        offset: 10,
        orderBy: 'created_at',
        ascending: true,
      });
    });
  });

  describe('useCreateConversation', () => {
    it('should create a new conversation', async () => {
      const { createReflectionConversation } = require('@/services/conversationService');
      vi.mocked(createReflectionConversation).mockResolvedValue({
        success: true,
        data: mockConversation,
      });

      const { result } = renderHook(
        () => useCreateConversation(),
        { wrapper: createWrapper() }
      );

      const input = {
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
      };

      await result.current.mutateAsync(input);

      expect(createReflectionConversation).toHaveBeenCalledWith(input);
    });

    it('should handle creation errors', async () => {
      const { createReflectionConversation } = require('@/services/conversationService');
      vi.mocked(createReflectionConversation).mockResolvedValue({
        success: false,
        error: { message: 'Creation failed', code: 'CREATE_ERROR' },
      });

      const { result } = renderHook(
        () => useCreateConversation(),
        { wrapper: createWrapper() }
      );

      const input = {
        journal_entry_id: 'entry-123',
        user_id: 'user-123',
      };

      await expect(result.current.mutateAsync(input)).rejects.toThrow();
    });
  });

  describe('useSendMessage', () => {
    it('should send a message with optimistic updates', async () => {
      const { createConversationMessage } = require('@/services/conversationService');
      vi.mocked(createConversationMessage).mockResolvedValue({
        success: true,
        data: {
          id: 'msg-new',
          conversation_id: 'conv-123',
          sender_type: 'user',
          message_content: 'Test message',
          message_type: 'text',
          ai_metadata: null,
          created_at: '2024-01-01T00:02:00Z',
        },
      });

      const { result } = renderHook(
        () => useSendMessage(),
        { wrapper: createWrapper() }
      );

      const input = {
        conversation_id: 'conv-123',
        sender_type: 'user' as const,
        message_content: 'Test message',
        message_type: 'text' as const,
      };

      await result.current.mutateAsync(input);

      expect(createConversationMessage).toHaveBeenCalledWith(input);
    });
  });

  describe('useConversationManager', () => {
    it('should provide complete conversation state and actions', async () => {
      const { getConversationByJournalEntry, getConversationMessages } = require('@/services/conversationService');
      
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: true,
        data: mockConversation,
      });
      
      vi.mocked(getConversationMessages).mockResolvedValue({
        success: true,
        data: mockMessages,
      });

      const { result } = renderHook(
        () => useConversationManager('entry-123', mockJournalEntry),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.state.isInitialized).toBe(true);
      });

      // Check state
      expect(result.current.state.conversation).toEqual(mockConversation);
      expect(result.current.state.messages).toEqual(mockMessages);
      expect(result.current.state.isLoading).toBe(false);
      expect(result.current.state.isGenerating).toBe(false);
      expect(result.current.state.error).toBe(null);

      // Check actions are available
      expect(typeof result.current.actions.sendMessage).toBe('function');
      expect(typeof result.current.actions.startConversation).toBe('function');
      expect(typeof result.current.actions.loadConversation).toBe('function');
      expect(typeof result.current.actions.clearConversation).toBe('function');
      expect(typeof result.current.actions.retryLastResponse).toBe('function');

      // Check mutations are available
      expect(result.current.mutations.createConversation).toBeDefined();
      expect(result.current.mutations.sendMessage).toBeDefined();
      expect(result.current.mutations.generateAIResponse).toBeDefined();
      expect(result.current.mutations.deleteConversation).toBeDefined();
    });

    it('should handle no conversation state', async () => {
      const { getConversationByJournalEntry } = require('@/services/conversationService');
      
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: true,
        data: null,
      });

      const { result } = renderHook(
        () => useConversationManager('entry-123', mockJournalEntry),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.state.isInitialized).toBe(true);
      });

      expect(result.current.state.conversation).toBe(null);
      expect(result.current.state.messages).toEqual([]);
    });

    it('should handle loading states', () => {
      const { getConversationByJournalEntry } = require('@/services/conversationService');
      
      // Mock a pending promise
      vi.mocked(getConversationByJournalEntry).mockReturnValue(
        new Promise(() => {}) // Never resolves
      );

      const { result } = renderHook(
        () => useConversationManager('entry-123', mockJournalEntry),
        { wrapper: createWrapper() }
      );

      expect(result.current.state.isLoading).toBe(true);
      expect(result.current.state.isInitialized).toBe(false);
    });

    it('should handle error states', async () => {
      const { getConversationByJournalEntry } = require('@/services/conversationService');
      
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: false,
        error: { message: 'Failed to load', code: 'LOAD_ERROR' },
      });

      const { result } = renderHook(
        () => useConversationManager('entry-123', mockJournalEntry),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.state.error).toBeTruthy();
      });

      expect(result.current.state.error).toContain('Failed to load');
    });

    it('should auto-initialize conversation when autoInitialize is true', async () => {
      const { getConversationByJournalEntry, createReflectionConversation } = require('@/services/conversationService');

      // Mock no existing conversation
      vi.mocked(getConversationByJournalEntry).mockResolvedValue({
        success: true,
        data: null,
      });

      // Mock successful conversation creation
      vi.mocked(createReflectionConversation).mockResolvedValue({
        success: true,
        data: mockConversation,
      });

      const { result } = renderHook(
        () => useConversationManager('entry-123', mockJournalEntry, true), // autoInitialize = true
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.state.isInitialized).toBe(true);
      });

      // Should have attempted to create a conversation
      await waitFor(() => {
        expect(createReflectionConversation).toHaveBeenCalledWith({
          journal_entry_id: 'entry-123',
          user_id: 'user-123',
        });
      });
    });
  });
});
