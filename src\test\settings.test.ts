/**
 * Settings Service Tests
 * Unit tests for settings functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  validateUserSettings,
  getUserSettings,
  updateUserSettings,
  deleteAllJournalEntries,
} from '@/services/settings.service';
import { UserSettingsUpdatePayload } from '@/types';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(),
          })),
        })),
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(),
      })),
    })),
  },
}));

describe('Settings Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateUserSettings', () => {
    it('should validate valid user settings', () => {
      const validData: UserSettingsUpdatePayload = {
        full_name: 'John Doe',
      };

      const result = validateUserSettings(validData);

      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });

    it('should reject empty full name', () => {
      const invalidData: UserSettingsUpdatePayload = {
        full_name: '',
      };

      const result = validateUserSettings(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors.full_name).toBe('Full name is required.');
    });

    it('should reject full name that is too short', () => {
      const invalidData: UserSettingsUpdatePayload = {
        full_name: 'A',
      };

      const result = validateUserSettings(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors.full_name).toBe('Full name must be at least 2 characters long.');
    });

    it('should reject full name that is too long', () => {
      const invalidData: UserSettingsUpdatePayload = {
        full_name: 'A'.repeat(101),
      };

      const result = validateUserSettings(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors.full_name).toBe('Full name must be less than 100 characters.');
    });

    it('should handle whitespace-only full name', () => {
      const invalidData: UserSettingsUpdatePayload = {
        full_name: '   ',
      };

      const result = validateUserSettings(invalidData);

      expect(result.isValid).toBe(false);
      expect(result.errors.full_name).toBe('Full name is required.');
    });
  });

  describe('Service Integration', () => {
    it('should have proper function exports', () => {
      expect(typeof getUserSettings).toBe('function');
      expect(typeof updateUserSettings).toBe('function');
      expect(typeof deleteAllJournalEntries).toBe('function');
      expect(typeof validateUserSettings).toBe('function');
    });

    it('should handle profile updates correctly', async () => {
      const validUpdate: UserSettingsUpdatePayload = {
        full_name: 'Updated Name',
      };

      const validation = validateUserSettings(validUpdate);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toEqual({});
    });
  });
});
