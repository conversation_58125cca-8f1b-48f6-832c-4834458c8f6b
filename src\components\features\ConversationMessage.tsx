/**
 * Conversation Message Component
 * Individual message bubble for conversation threads
 */

import React from 'react';
import { <PERSON>, User, Clock, Sparkles } from 'lucide-react';
import { ConversationMessage, MessageSenderType, MessageType } from '@/types';
import { formatDistanceToNow } from 'date-fns';

interface ConversationMessageProps {
  /** The message data */
  message: ConversationMessage;
  /** Whether this message is from the current user */
  isOwnMessage?: boolean;
  /** Whether this message is currently being generated */
  isGenerating?: boolean;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Get message type icon
 */
const getMessageTypeIcon = (messageType: MessageType, senderType: MessageSenderType) => {
  if (senderType === 'user') {
    return <User className="w-3 h-3" />;
  }

  switch (messageType) {
    case 'reflection_question':
      return <Sparkles className="w-3 h-3" />;
    case 'follow_up':
      return <Brain className="w-3 h-3" />;
    default:
      return <Brain className="w-3 h-3" />;
  }
};

/**
 * Get message bubble styling based on sender
 */
const getMessageStyling = (senderType: MessageSenderType, isGenerating: boolean = false) => {
  if (senderType === 'user') {
    return {
      container: 'ml-auto max-w-[80%]',
      bubble: `bg-gradient-to-br from-amber-500 to-orange-500 text-white ${
        isGenerating ? 'opacity-70' : ''
      }`,
      avatar: 'bg-amber-100 text-amber-600',
    };
  }

  return {
    container: 'mr-auto max-w-[80%]',
    bubble: `glass-effect bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200 text-amber-900 ${
      isGenerating ? 'animate-pulse' : ''
    }`,
    avatar: 'bg-amber-100 text-amber-600',
  };
};

/**
 * Typing indicator component
 */
const TypingIndicator = () => (
  <div className="flex items-center gap-1 px-3 py-2">
    <div className="flex gap-1">
      <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
      <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
      <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
    </div>
    <span className="text-xs text-amber-600 ml-2">Amber is thinking...</span>
  </div>
);

export const ConversationMessage: React.FC<ConversationMessageProps> = ({
  message,
  isOwnMessage = false,
  isGenerating = false,
  className = '',
}) => {
  const styling = getMessageStyling(message.sender_type, isGenerating);
  const messageIcon = getMessageTypeIcon(message.message_type, message.sender_type);
  
  // Format timestamp
  const timeAgo = formatDistanceToNow(new Date(message.created_at), { addSuffix: true });

  return (
    <div className={`flex items-start gap-3 mb-4 ${styling.container} ${className}`}>
      {/* Avatar (only for AI messages) */}
      {message.sender_type === 'ai' && (
        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${styling.avatar}`}>
          {messageIcon}
        </div>
      )}

      {/* Message bubble */}
      <div className="flex-1 min-w-0">
        {/* Message header */}
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs font-medium text-amber-700">
            {message.sender_type === 'ai' ? 'Amber' : 'You'}
          </span>
          <div className="flex items-center gap-1 text-xs text-amber-600">
            <Clock className="w-3 h-3" />
            <span>{timeAgo}</span>
          </div>
          {message.message_type === 'reflection_question' && (
            <span className="text-xs bg-amber-100 text-amber-700 px-2 py-0.5 rounded-full">
              Reflection
            </span>
          )}
        </div>

        {/* Message content */}
        <div className={`rounded-lg px-4 py-3 shadow-sm ${styling.bubble}`}>
          {isGenerating ? (
            <TypingIndicator />
          ) : (
            <p className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.message_content}
            </p>
          )}
        </div>

        {/* AI metadata (for debugging in development) */}
        {process.env.NODE_ENV === 'development' && 
         message.sender_type === 'ai' && 
         message.ai_metadata && (
          <div className="mt-1 text-xs text-amber-500 opacity-70">
            Confidence: {Math.round((message.ai_metadata.confidence || 0) * 100)}% | 
            Emotion: {message.ai_metadata.emotion} | 
            Time: {message.ai_metadata.processingTime}ms
          </div>
        )}
      </div>

      {/* Avatar (only for user messages) */}
      {message.sender_type === 'user' && (
        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${styling.avatar}`}>
          {messageIcon}
        </div>
      )}
    </div>
  );
};

/**
 * Message list component for displaying multiple messages
 */
interface ConversationMessageListProps {
  /** Array of messages to display */
  messages: ConversationMessage[];
  /** Whether AI is currently generating a response */
  isGenerating?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Test ID for testing */
  testId?: string;
}

export const ConversationMessageList: React.FC<ConversationMessageListProps> = ({
  messages,
  isGenerating = false,
  className = '',
  testId,
}) => {
  const scrollRef = React.useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  React.useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages, isGenerating]);

  return (
    <div
      ref={scrollRef}
      className={`flex-1 overflow-y-auto p-4 space-y-2 ${className}`}
      data-testid={testId}
    >
      {messages.length === 0 ? (
        <div className="flex items-center justify-center h-full text-center">
          <div className="max-w-sm">
            <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Brain className="w-8 h-8 text-amber-600" />
            </div>
            <h3 className="text-lg font-semibold text-amber-800 mb-2">
              Start a conversation with Amber
            </h3>
            <p className="text-amber-600 text-sm">
              Ask Amber about your journal entry or share your thoughts to begin a meaningful conversation.
            </p>
          </div>
        </div>
      ) : (
        <>
          {messages.map((message) => (
            <ConversationMessage
              key={message.id}
              message={message}
              isOwnMessage={message.sender_type === 'user'}
            />
          ))}
          
          {/* Show typing indicator when AI is generating */}
          {isGenerating && (
            <div className="flex items-start gap-3 mb-4 mr-auto max-w-[80%]">
              <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Brain className="w-3 h-3 text-amber-600" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-xs font-medium text-amber-700">Amber</span>
                  <span className="text-xs text-amber-600">is typing...</span>
                </div>
                <div className="glass-effect bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200 rounded-lg px-4 py-3 shadow-sm">
                  <TypingIndicator />
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ConversationMessage;
