-- Add embedding column to memory table for semantic similarity search
-- Migration: 20250725000001_add_memory_embeddings.sql

-- Add embedding column to store 384-dimensional vectors as JSONB
-- Using JSONB instead of pgvector for broader compatibility
ALTER TABLE public.memory 
ADD COLUMN embedding JSONB DEFAULT NULL;

-- Add comment to document the embedding field
COMMENT ON COLUMN public.memory.embedding IS 'Semantic embedding vector (384 dimensions) stored as JSON array for similarity search';

-- Create GIN index for efficient JSONB operations on embeddings
CREATE INDEX IF NOT EXISTS idx_memory_embedding ON public.memory USING gin (embedding);

-- Create composite index for user-specific embedding queries
CREATE INDEX IF NOT EXISTS idx_memory_user_embedding ON public.memory(user_id) WHERE embedding IS NOT NULL;

-- <PERSON>reate function to validate embedding format
CREATE OR REPLACE FUNCTION validate_memory_embedding()
RETURNS TRIGGER AS $$
BEGIN
    -- If embedding is provided, validate it's a proper 384-dimensional array
    IF NEW.embedding IS NOT NULL THEN
        -- Check if it's a JSON array
        IF jsonb_typeof(NEW.embedding) != 'array' THEN
            RAISE EXCEPTION 'Embedding must be a JSON array';
        END IF;
        
        -- Check if it has exactly 384 dimensions
        IF jsonb_array_length(NEW.embedding) != 384 THEN
            RAISE EXCEPTION 'Embedding must have exactly 384 dimensions, got %', jsonb_array_length(NEW.embedding);
        END IF;
        
        -- Validate that all elements are numbers
        IF EXISTS (
            SELECT 1 
            FROM jsonb_array_elements(NEW.embedding) AS elem 
            WHERE jsonb_typeof(elem) != 'number'
        ) THEN
            RAISE EXCEPTION 'All embedding elements must be numbers';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to validate embeddings on insert/update
CREATE TRIGGER trigger_validate_memory_embedding
    BEFORE INSERT OR UPDATE ON public.memory
    FOR EACH ROW
    EXECUTE FUNCTION validate_memory_embedding();

-- Create function to calculate cosine similarity between embeddings
CREATE OR REPLACE FUNCTION cosine_similarity(embedding1 JSONB, embedding2 JSONB)
RETURNS FLOAT AS $$
DECLARE
    dot_product FLOAT := 0;
    norm1 FLOAT := 0;
    norm2 FLOAT := 0;
    i INTEGER;
    val1 FLOAT;
    val2 FLOAT;
BEGIN
    -- Validate inputs
    IF embedding1 IS NULL OR embedding2 IS NULL THEN
        RETURN 0;
    END IF;
    
    IF jsonb_array_length(embedding1) != jsonb_array_length(embedding2) THEN
        RETURN 0;
    END IF;
    
    -- Calculate dot product and norms
    FOR i IN 0..jsonb_array_length(embedding1)-1 LOOP
        val1 := (embedding1->i)::FLOAT;
        val2 := (embedding2->i)::FLOAT;
        
        dot_product := dot_product + (val1 * val2);
        norm1 := norm1 + (val1 * val1);
        norm2 := norm2 + (val2 * val2);
    END LOOP;
    
    -- Handle edge cases
    IF norm1 = 0 OR norm2 = 0 THEN
        RETURN 0;
    END IF;
    
    -- Return cosine similarity
    RETURN dot_product / (sqrt(norm1) * sqrt(norm2));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to find similar memories using embeddings
CREATE OR REPLACE FUNCTION find_similar_memories(
    query_embedding JSONB,
    user_id_param UUID,
    similarity_threshold FLOAT DEFAULT 0.7,
    max_results INTEGER DEFAULT 10
)
RETURNS TABLE(
    memory_id UUID,
    memory_key TEXT,
    memory_value TEXT,
    memory_category TEXT,
    memory_importance INTEGER,
    similarity_score FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.key,
        m.value,
        m.category,
        m.importance,
        cosine_similarity(query_embedding, m.embedding) as similarity
    FROM public.memory m
    WHERE 
        m.user_id = user_id_param 
        AND m.embedding IS NOT NULL
        AND cosine_similarity(query_embedding, m.embedding) >= similarity_threshold
    ORDER BY similarity DESC
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION validate_memory_embedding TO authenticated;
GRANT EXECUTE ON FUNCTION cosine_similarity TO authenticated;
GRANT EXECUTE ON FUNCTION find_similar_memories TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION cosine_similarity IS 'Calculate cosine similarity between two embedding vectors stored as JSONB arrays';
COMMENT ON FUNCTION find_similar_memories IS 'Find memories similar to a query embedding using cosine similarity';
COMMENT ON FUNCTION validate_memory_embedding IS 'Validate that memory embeddings are properly formatted 384-dimensional arrays';

-- Create index for similarity searches (composite index for performance)
CREATE INDEX IF NOT EXISTS idx_memory_similarity_search 
ON public.memory(user_id, importance DESC) 
WHERE embedding IS NOT NULL;

-- Update the existing updated_at trigger to handle embedding changes
CREATE OR REPLACE FUNCTION update_memory_updated_at_with_embedding()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Replace the existing updated_at trigger
DROP TRIGGER IF EXISTS trigger_update_memory_updated_at ON public.memory;
CREATE TRIGGER trigger_update_memory_updated_at_with_embedding
    BEFORE UPDATE ON public.memory
    FOR EACH ROW
    EXECUTE FUNCTION update_memory_updated_at_with_embedding();

-- Grant execute permission for the new trigger function
GRANT EXECUTE ON FUNCTION update_memory_updated_at_with_embedding TO authenticated;
