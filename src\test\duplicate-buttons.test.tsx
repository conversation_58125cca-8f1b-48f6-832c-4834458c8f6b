/**
 * Duplicate Buttons Fix Tests
 * Tests to verify that duplicate action buttons are fixed in EditJournalEntryModal
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { JournalEntryForm } from '@/components/forms/JournalEntryForm';
import { EditJournalEntryModal } from '@/components/features/EditJournalEntryModal';
import { FormModal } from '@/components/ui/form-modal';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock the AI reflection service
vi.mock('@/services/aiReflectionService', () => ({
  generateAIReflection: vi.fn(),
}));

// Mock the auth context
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user', email: '<EMAIL>' },
  }),
}));

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
  },
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Duplicate Buttons Fix', () => {
  const mockFormData = {
    title: 'Test Entry',
    content: 'Test content',
    emotion: 'joyful' as const,
    moodScore: 7 as const,
  };

  const mockEntry = {
    id: 'test-id',
    title: 'Test Entry',
    content: 'Test content',
    emotion: 'joyful' as const,
    mood_score: 7 as const,
    date: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    user_id: 'test-user',
    ai_summary: null,
    ai_emotion: null,
    ai_encouragement: null,
    ai_reflection_question: null,
    ai_reflection: null,
  };

  describe('JournalEntryForm standalone', () => {
    it('shows action buttons when hideActions is false (default)', () => {
      render(
        <JournalEntryForm
          data={mockFormData}
          onChange={vi.fn()}
          onSubmit={vi.fn()}
          onCancel={vi.fn()}
        />
      );

      // Should show both Cancel and Save buttons
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /save entry/i })).toBeInTheDocument();
    });

    it('hides action buttons when hideActions is true', () => {
      render(
        <JournalEntryForm
          data={mockFormData}
          onChange={vi.fn()}
          onSubmit={vi.fn()}
          onCancel={vi.fn()}
          hideActions={true}
        />
      );

      // Should not show any action buttons
      expect(screen.queryByRole('button', { name: /cancel/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /save entry/i })).not.toBeInTheDocument();
    });
  });

  describe('FormModal with JournalEntryForm', () => {
    it('shows only FormModal buttons when JournalEntryForm has hideActions=true', () => {
      render(
        <TestWrapper>
          <FormModal
            isOpen={true}
            onClose={vi.fn()}
            onSubmit={vi.fn()}
            title="Test Modal"
            submitText="Save"
            cancelText="Cancel"
          >
            <JournalEntryForm
              data={mockFormData}
              onChange={vi.fn()}
              onSubmit={vi.fn()}
              onCancel={vi.fn()}
              hideActions={true}
            />
          </FormModal>
        </TestWrapper>
      );

      // Should only show FormModal's buttons (in the footer)
      const cancelButtons = screen.getAllByRole('button', { name: /cancel/i });
      const saveButtons = screen.getAllByRole('button', { name: /save/i });

      // Should have exactly one Cancel and one Save button (from FormModal footer)
      expect(cancelButtons).toHaveLength(1);
      expect(saveButtons).toHaveLength(1);
    });
  });

  describe('EditJournalEntryModal integration', () => {
    it('shows only one set of action buttons', () => {
      render(
        <TestWrapper>
          <EditJournalEntryModal
            isOpen={true}
            onClose={vi.fn()}
            onSave={vi.fn()}
            entry={mockEntry}
          />
        </TestWrapper>
      );

      // Should only show one set of buttons
      const cancelButtons = screen.getAllByRole('button', { name: /cancel/i });
      const updateButtons = screen.getAllByRole('button', { name: /update entry/i });

      // Should have exactly one Cancel and one Update Entry button
      expect(cancelButtons).toHaveLength(1);
      expect(updateButtons).toHaveLength(1);
    });

    it('does not show duplicate buttons', () => {
      render(
        <TestWrapper>
          <EditJournalEntryModal
            isOpen={true}
            onClose={vi.fn()}
            onSave={vi.fn()}
            entry={mockEntry}
          />
        </TestWrapper>
      );

      // Get all buttons and check for duplicates
      const allButtons = screen.getAllByRole('button');
      const buttonTexts = allButtons.map(button => button.textContent?.toLowerCase() || '');
      
      // Count occurrences of cancel and update/save buttons
      const cancelCount = buttonTexts.filter(text => text.includes('cancel')).length;
      const updateCount = buttonTexts.filter(text => 
        text.includes('update') || text.includes('save')
      ).length;

      // Should have exactly one of each
      expect(cancelCount).toBe(1);
      expect(updateCount).toBe(1);
    });
  });
});
