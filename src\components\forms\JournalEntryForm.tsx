/**
 * Journal Entry Form Component
 * Reusable form component for creating and editing journal entries
 * Consolidates the repetitive journal entry form pattern
 */

import React from 'react';
import { <PERSON><PERSON>ield } from '@/components/ui/form-field';
import { FormSection, FormFieldGroup, FormActions } from '@/components/ui/form-section';
import { AmberButtonPair } from '@/components/ui/amber-button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { EmotionPicker } from '@/components/features/EmotionPicker';
import { cn } from '@/utils/utils';
import { BaseComponentProps, EmotionType, MoodScore } from '@/types';

interface JournalEntryFormData {
  title: string;
  content: string;
  emotion: EmotionType | '';
  moodScore: MoodScore;
}

interface JournalEntryFormProps extends BaseComponentProps {
  /** Form data */
  data: JournalEntryFormData;
  /** Form data change handler */
  onChange: (data: JournalEntryFormData) => void;
  /** Form submission handler */
  onSubmit: () => void;
  /** Cancel handler */
  onCancel: () => void;
  /** Whether the form is in loading state */
  isLoading?: boolean;
  /** Form validation errors */
  errors?: Partial<Record<keyof JournalEntryFormData, string>>;
  /** Submit button text */
  submitText?: string;
  /** Cancel button text */
  cancelText?: string;
  /** Whether to show the mood and emotion section */
  showMoodSection?: boolean;
  /** Additional content to render after the main form fields */
  additionalContent?: React.ReactNode;
  /** Whether to hide the form action buttons (useful when used inside modals) */
  hideActions?: boolean;
}

/**
 * JournalEntryForm component with consistent styling and validation
 */
export const JournalEntryForm: React.FC<JournalEntryFormProps> = ({
  data,
  onChange,
  onSubmit,
  onCancel,
  isLoading = false,
  errors = {},
  submitText = 'Save Entry',
  cancelText = 'Cancel',
  showMoodSection = true,
  additionalContent,
  hideActions = false,
  className,
  testId,
  ...props
}) => {
  const handleFieldChange = (field: keyof JournalEntryFormData) => (value: string | number) => {
    onChange({
      ...data,
      [field]: value,
    });
  };

  const handleEmotionChange = (emotion: EmotionType) => {
    onChange({
      ...data,
      emotion,
    });
  };

  const handleMoodScoreChange = (moodScore: [MoodScore]) => {
    onChange({
      ...data,
      moodScore: moodScore[0],
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={cn('form-spacing', className)}
      data-testid={testId}
      {...props}
    >
      {/* Title Field */}
      <FormField
        label="What's on your mind today?"
        type="text"
        placeholder="Give your entry a meaningful title..."
        value={data.title}
        onChange={handleFieldChange('title')}
        required
        disabled={isLoading}
        error={errors.title}
        inputClassName="text-lg font-lora focus-elegant border-modern"
        testId={testId ? `${testId}-title` : undefined}
      />

      {/* Content Field */}
      <FormField
        label="Share your thoughts and feelings"
        type="textarea"
        placeholder="Share your thoughts, feelings, and experiences..."
        value={data.content}
        onChange={handleFieldChange('content')}
        required
        disabled={isLoading}
        error={errors.content}
        minHeight="200px"
        inputClassName="font-lora leading-relaxed text-elegant focus-elegant border-modern"
        testId={testId ? `${testId}-content` : undefined}
      />

      {/* Mood and Emotion Section */}
      {showMoodSection && (
        <FormSection
          layout="vertical"
          gap="lg"
          className="card-modern section-padding"
        >
          {/* Mood Score Slider */}
          <div className="space-y-6">
            <Label className="text-base font-medium heading-modern">
              Overall mood (1-10): <span className="text-amber-600 font-semibold">{data.moodScore}</span>
            </Label>
            <div className="px-4">
              <Slider
                value={[data.moodScore]}
                onValueChange={handleMoodScoreChange}
                max={10}
                min={1}
                step={1}
                className="w-full focus-elegant"
                disabled={isLoading}
              />
              <div className="flex justify-between text-sm text-muted-foreground mt-3">
                <span className="text-modern">Low</span>
                <span className="text-modern">High</span>
              </div>
            </div>
          </div>

          {/* Emotion Picker */}
          <div className="space-y-6">
            <Label className="text-base font-medium heading-modern">How are you feeling?</Label>
            <EmotionPicker
              selectedEmotion={data.emotion}
              onEmotionSelect={handleEmotionChange}
            />
            {errors.emotion && (
              <p className="text-sm font-medium text-red-600" role="alert">
                {errors.emotion}
              </p>
            )}
          </div>
        </FormSection>
      )}

      {/* Additional Content */}
      {additionalContent}

      {/* Form Actions - Only show if not hidden */}
      {!hideActions && (
        <FormActions align="between" className="pt-6">
          <AmberButtonPair
            cancelText={cancelText}
            actionText={submitText}
            onCancel={onCancel}
            onAction={onSubmit}
            isLoading={isLoading}
            actionVariant="primary"
            variant="form"
            className="btn-elegant hover-lift"
            testId={testId ? `${testId}-actions` : undefined}
          />
        </FormActions>
      )}
    </form>
  );
};

/**
 * Hook for managing journal entry form state and validation
 */
export const useJournalEntryForm = (initialData?: Partial<JournalEntryFormData>) => {
  const [data, setData] = React.useState<JournalEntryFormData>({
    title: '',
    content: '',
    emotion: '',
    moodScore: 5,
    ...initialData,
  });

  const [errors, setErrors] = React.useState<Partial<Record<keyof JournalEntryFormData, string>>>({});

  const validate = React.useCallback((): boolean => {
    const newErrors: Partial<Record<keyof JournalEntryFormData, string>> = {};

    if (!data.title.trim()) {
      newErrors.title = 'Please enter a title for your entry';
    }

    if (!data.content.trim()) {
      newErrors.content = 'Please share your thoughts and feelings';
    }

    if (!data.emotion) {
      newErrors.emotion = 'Please select how you\'re feeling';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [data.title, data.content, data.emotion]);

  const reset = React.useCallback((newData?: Partial<JournalEntryFormData>) => {
    setData({
      title: '',
      content: '',
      emotion: '',
      moodScore: 5,
      ...newData,
    });
    setErrors({});
  }, []);

  return {
    data,
    setData,
    errors,
    setErrors,
    validate,
    reset,
  };
};
