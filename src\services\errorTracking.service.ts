/**
 * Error Tracking Service
 * Comprehensive error tracking and reporting for production monitoring
 */

import { getEnvironmentConfig } from '@/config/environment.config';
import { handleError } from '@/utils/errorHandler';

/**
 * Error tracking data structure
 */
export interface TrackedError {
  id: string;
  message: string;
  stack?: string;
  url: string;
  lineNumber?: number;
  columnNumber?: number;
  timestamp: number;
  userAgent: string;
  userId?: string;
  sessionId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
  breadcrumbs?: ErrorBreadcrumb[];
}

/**
 * Error breadcrumb for tracking user actions leading to error
 */
export interface ErrorBreadcrumb {
  timestamp: number;
  category: 'navigation' | 'user' | 'console' | 'network' | 'dom';
  message: string;
  level: 'info' | 'warning' | 'error';
  data?: Record<string, any>;
}

/**
 * Error tracking configuration
 */
interface ErrorTrackingConfig {
  enabled: boolean;
  maxBreadcrumbs: number;
  reportingEndpoint?: string;
  sampleRate: number;
  ignoreErrors: string[];
  ignoreUrls: string[];
}

/**
 * Error tracking service
 */
class ErrorTrackingService {
  private config: ErrorTrackingConfig;
  private errors: TrackedError[] = [];
  private breadcrumbs: ErrorBreadcrumb[] = [];
  private sessionId: string;

  constructor() {
    const env = getEnvironmentConfig();
    
    this.config = {
      enabled: env.isProduction || env.features.debugMode,
      maxBreadcrumbs: 50,
      sampleRate: env.isProduction ? 0.1 : 1.0, // 10% in prod, 100% in dev
      ignoreErrors: [
        'Script error.',
        'Non-Error promise rejection captured',
        'ResizeObserver loop limit exceeded',
      ],
      ignoreUrls: [
        '/favicon.ico',
        'chrome-extension://',
        'moz-extension://',
      ],
    };

    this.sessionId = this.generateSessionId();

    if (this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * Initialize error tracking
   */
  private initialize(): void {
    console.log('[ErrorTracking] Initializing error tracking...');

    // Global error handler
    window.addEventListener('error', this.handleGlobalError.bind(this));

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));

    // Console error tracking
    this.wrapConsoleError();

    // Network error tracking
    this.trackNetworkErrors();

    console.log('[ErrorTracking] Error tracking initialized');
  }

  /**
   * Handle global JavaScript errors
   */
  private handleGlobalError(event: ErrorEvent): void {
    const error: TrackedError = {
      id: this.generateErrorId(),
      message: event.message,
      stack: event.error?.stack,
      url: event.filename || window.location.href,
      lineNumber: event.lineno,
      columnNumber: event.colno,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      sessionId: this.sessionId,
      severity: this.determineSeverity(event.error),
      breadcrumbs: [...this.breadcrumbs],
    };

    this.trackError(error);
  }

  /**
   * Handle unhandled promise rejections
   */
  private handleUnhandledRejection(event: PromiseRejectionEvent): void {
    const error: TrackedError = {
      id: this.generateErrorId(),
      message: `Unhandled Promise Rejection: ${event.reason}`,
      stack: event.reason?.stack,
      url: window.location.href,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      sessionId: this.sessionId,
      severity: 'medium',
      breadcrumbs: [...this.breadcrumbs],
    };

    this.trackError(error);
  }

  /**
   * Wrap console.error to track console errors
   */
  private wrapConsoleError(): void {
    const originalError = console.error;
    
    console.error = (...args: any[]) => {
      this.addBreadcrumb({
        timestamp: Date.now(),
        category: 'console',
        message: args.join(' '),
        level: 'error',
      });

      originalError.apply(console, args);
    };
  }

  /**
   * Track network errors
   */
  private trackNetworkErrors(): void {
    // Track fetch errors
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        
        if (!response.ok) {
          this.addBreadcrumb({
            timestamp: Date.now(),
            category: 'network',
            message: `HTTP ${response.status}: ${args[0]}`,
            level: 'error',
            data: { status: response.status, url: args[0] },
          });
        }
        
        return response;
      } catch (error) {
        this.addBreadcrumb({
          timestamp: Date.now(),
          category: 'network',
          message: `Network error: ${args[0]}`,
          level: 'error',
          data: { error: (error as Error).message, url: args[0] },
        });
        
        throw error;
      }
    };
  }

  /**
   * Track a custom error
   */
  public trackError(error: TrackedError | Error, context?: Record<string, any>): void {
    if (!this.config.enabled || !this.shouldTrackError(error)) {
      return;
    }

    let trackedError: TrackedError;

    if (error instanceof Error) {
      trackedError = {
        id: this.generateErrorId(),
        message: error.message,
        stack: error.stack,
        url: window.location.href,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        sessionId: this.sessionId,
        severity: this.determineSeverity(error),
        context,
        breadcrumbs: [...this.breadcrumbs],
      };
    } else {
      trackedError = error;
    }

    // Store error
    this.errors.push(trackedError);

    // Log in development
    if (getEnvironmentConfig().isDevelopment) {
      console.error('[ErrorTracking] Tracked error:', trackedError);
    }

    // Report error
    this.reportError(trackedError);

    // Limit stored errors
    if (this.errors.length > 100) {
      this.errors.splice(0, this.errors.length - 100);
    }
  }

  /**
   * Add breadcrumb
   */
  public addBreadcrumb(breadcrumb: ErrorBreadcrumb): void {
    this.breadcrumbs.push(breadcrumb);

    // Limit breadcrumbs
    if (this.breadcrumbs.length > this.config.maxBreadcrumbs) {
      this.breadcrumbs.splice(0, this.breadcrumbs.length - this.config.maxBreadcrumbs);
    }
  }

  /**
   * Report error to external service
   */
  private reportError(error: TrackedError): void {
    if (this.config.reportingEndpoint) {
      fetch(this.config.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      }).catch(reportError => {
        console.warn('[ErrorTracking] Failed to report error:', reportError);
      });
    }

    // Store in localStorage for debugging
    if (getEnvironmentConfig().features.debugMode) {
      const storedErrors = JSON.parse(localStorage.getItem('trackedErrors') || '[]');
      storedErrors.push(error);
      
      if (storedErrors.length > 50) {
        storedErrors.splice(0, storedErrors.length - 50);
      }
      
      localStorage.setItem('trackedErrors', JSON.stringify(storedErrors));
    }
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    if (!error) return 'low';

    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'medium';
    }
    
    if (message.includes('chunk') || message.includes('loading')) {
      return 'high';
    }
    
    if (message.includes('security') || message.includes('permission')) {
      return 'critical';
    }
    
    return 'medium';
  }

  /**
   * Check if error should be tracked
   */
  private shouldTrackError(error: TrackedError | Error): boolean {
    const message = error instanceof Error ? error.message : error.message;
    
    // Check ignore list
    if (this.config.ignoreErrors.some(ignored => message.includes(ignored))) {
      return false;
    }

    // Sample rate check
    return Math.random() < this.config.sampleRate;
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get tracked errors
   */
  public getErrors(): TrackedError[] {
    return [...this.errors];
  }

  /**
   * Get breadcrumbs
   */
  public getBreadcrumbs(): ErrorBreadcrumb[] {
    return [...this.breadcrumbs];
  }

  /**
   * Clear tracked data
   */
  public clear(): void {
    this.errors = [];
    this.breadcrumbs = [];
    localStorage.removeItem('trackedErrors');
  }

  /**
   * Set reporting endpoint
   */
  public setReportingEndpoint(endpoint: string): void {
    this.config.reportingEndpoint = endpoint;
  }
}

// Export singleton instance
export const errorTrackingService = new ErrorTrackingService();

// Export utility functions
export const trackError = (error: Error, context?: Record<string, any>) => 
  errorTrackingService.trackError(error, context);

export const addErrorBreadcrumb = (breadcrumb: ErrorBreadcrumb) => 
  errorTrackingService.addBreadcrumb(breadcrumb);
