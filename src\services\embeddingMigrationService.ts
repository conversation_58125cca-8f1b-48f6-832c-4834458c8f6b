/**
 * Embedding Migration Service
 * Service for generating embeddings for existing memories
 * Provides background migration functionality for semantic search
 */

import { supabase } from '@/integrations/supabase/client';
import { generateEmbedding, isEmbeddingServiceAvailable } from './embeddingService';
import { ApiResponse } from '@/types';

/**
 * Migration status interface
 */
interface MigrationStatus {
  totalMemories: number;
  processedMemories: number;
  successfulEmbeddings: number;
  failedEmbeddings: number;
  isComplete: boolean;
  startTime: Date;
  endTime?: Date;
  errors: string[];
}

/**
 * Memory record for migration
 */
interface MemoryRecord {
  id: string;
  key: string;
  value: string;
  embedding: string | null;
}

/**
 * Generate embeddings for all memories that don't have them
 */
export const migrateMemoryEmbeddings = async (
  onProgress?: (status: MigrationStatus) => void
): Promise<ApiResponse<MigrationStatus>> => {
  const startTime = new Date();
  const status: MigrationStatus = {
    totalMemories: 0,
    processedMemories: 0,
    successfulEmbeddings: 0,
    failedEmbeddings: 0,
    isComplete: false,
    startTime,
    errors: [],
  };

  try {
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTH_ERROR'
        }
      };
    }

    // Check if embedding service is available
    const serviceAvailable = await isEmbeddingServiceAvailable();
    if (!serviceAvailable) {
      return {
        success: false,
        error: {
          message: 'Embedding service is not available',
          code: 'SERVICE_UNAVAILABLE'
        }
      };
    }

    console.log('🧠 [MIGRATION] Starting embedding migration for user:', user.id);

    // Get all memories without embeddings
    const { data: memories, error: fetchError } = await supabase
      .from('memory')
      .select('id, key, value, embedding')
      .eq('user_id', user.id)
      .is('embedding', null) as { 
        data: MemoryRecord[] | null; 
        error: { message: string; code: string } | null 
      };

    if (fetchError) {
      console.error('🧠 [MIGRATION] Error fetching memories:', fetchError);
      return {
        success: false,
        error: {
          message: fetchError.message,
          code: fetchError.code
        }
      };
    }

    if (!memories || memories.length === 0) {
      console.log('🧠 [MIGRATION] No memories need embedding generation');
      status.isComplete = true;
      status.endTime = new Date();
      return {
        success: true,
        data: status
      };
    }

    status.totalMemories = memories.length;
    console.log(`🧠 [MIGRATION] Found ${memories.length} memories to process`);

    // Process memories in batches to avoid overwhelming the service
    const batchSize = 5;
    const delay = 200; // 200ms delay between requests

    for (let i = 0; i < memories.length; i += batchSize) {
      const batch = memories.slice(i, i + batchSize);
      
      // Process batch in parallel
      const batchPromises = batch.map(async (memory) => {
        try {
          const embeddingText = `${memory.key} ${memory.value}`;
          const embeddingResult = await generateEmbedding(embeddingText);

          if (embeddingResult.success) {
            // Update memory with embedding
            const { error: updateError } = await supabase
              .from('memory')
              .update({ embedding: JSON.stringify(embeddingResult.data) })
              .eq('id', memory.id);

            if (updateError) {
              console.error(`🧠 [MIGRATION] Failed to update memory ${memory.key}:`, updateError);
              status.failedEmbeddings++;
              status.errors.push(`Failed to update memory "${memory.key}": ${updateError.message}`);
            } else {
              console.log(`🧠 [MIGRATION] Generated embedding for memory: ${memory.key}`);
              status.successfulEmbeddings++;
            }
          } else {
            console.warn(`🧠 [MIGRATION] Failed to generate embedding for memory ${memory.key}:`, embeddingResult.error);
            status.failedEmbeddings++;
            status.errors.push(`Failed to generate embedding for "${memory.key}": ${embeddingResult.error?.message || 'Unknown error'}`);
          }

          status.processedMemories++;
          
          // Call progress callback if provided
          if (onProgress) {
            onProgress({ ...status });
          }

        } catch (error) {
          console.error(`🧠 [MIGRATION] Unexpected error processing memory ${memory.key}:`, error);
          status.failedEmbeddings++;
          status.processedMemories++;
          status.errors.push(`Unexpected error for "${memory.key}": ${error instanceof Error ? error.message : 'Unknown error'}`);
          
          if (onProgress) {
            onProgress({ ...status });
          }
        }
      });

      // Wait for batch to complete
      await Promise.all(batchPromises);

      // Add delay between batches to avoid overwhelming the service
      if (i + batchSize < memories.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    status.isComplete = true;
    status.endTime = new Date();

    console.log(`🧠 [MIGRATION] Migration completed:`, {
      total: status.totalMemories,
      successful: status.successfulEmbeddings,
      failed: status.failedEmbeddings,
      duration: status.endTime.getTime() - status.startTime.getTime()
    });

    return {
      success: true,
      data: status
    };

  } catch (error) {
    console.error('🧠 [MIGRATION] Migration failed:', error);
    status.isComplete = true;
    status.endTime = new Date();
    status.errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);

    return {
      success: false,
      error: {
        message: 'Migration failed',
        code: 'MIGRATION_ERROR'
      },
      data: status
    };
  }
};

/**
 * Check how many memories need embedding generation
 */
export const checkMigrationStatus = async (): Promise<ApiResponse<{ needsMigration: number; hasEmbeddings: number }>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTH_ERROR'
        }
      };
    }

    // Count memories without embeddings
    const { count: needsMigration, error: countError1 } = await supabase
      .from('memory')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .is('embedding', null);

    // Count memories with embeddings
    const { count: hasEmbeddings, error: countError2 } = await supabase
      .from('memory')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .not('embedding', 'is', null);

    if (countError1 || countError2) {
      return {
        success: false,
        error: {
          message: 'Failed to check migration status',
          code: 'COUNT_ERROR'
        }
      };
    }

    return {
      success: true,
      data: {
        needsMigration: needsMigration || 0,
        hasEmbeddings: hasEmbeddings || 0
      }
    };

  } catch (error) {
    console.error('Error checking migration status:', error);
    return {
      success: false,
      error: {
        message: 'Failed to check migration status',
        code: 'UNKNOWN_ERROR'
      }
    };
  }
};

/**
 * Force regenerate embeddings for all memories (useful for model updates)
 */
export const regenerateAllEmbeddings = async (
  onProgress?: (status: MigrationStatus) => void
): Promise<ApiResponse<MigrationStatus>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTH_ERROR'
        }
      };
    }

    // Clear all existing embeddings first
    const { error: clearError } = await supabase
      .from('memory')
      .update({ embedding: null })
      .eq('user_id', user.id);

    if (clearError) {
      return {
        success: false,
        error: {
          message: 'Failed to clear existing embeddings',
          code: 'CLEAR_ERROR'
        }
      };
    }

    console.log('🧠 [MIGRATION] Cleared all existing embeddings, starting regeneration...');

    // Now run the migration to regenerate all embeddings
    return migrateMemoryEmbeddings(onProgress);

  } catch (error) {
    console.error('Error regenerating embeddings:', error);
    return {
      success: false,
      error: {
        message: 'Failed to regenerate embeddings',
        code: 'REGENERATION_ERROR'
      }
    };
  }
};

export type { MigrationStatus };
