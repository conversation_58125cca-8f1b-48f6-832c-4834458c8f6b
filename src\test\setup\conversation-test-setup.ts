/**
 * Conversation Test Setup
 * Common test utilities and setup for conversation system tests
 */

import { vi } from 'vitest';
import { QueryClient } from '@tanstack/react-query';
import React from 'react';
import { AuthContext } from '@/contexts/AuthContext';
import { JournalEntry, ConversationMessage, ReflectionConversation, User } from '@/types';

/**
 * Mock user for testing
 */
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 'user-123',
  email: '<EMAIL>',
  aud: 'authenticated',
  role: 'authenticated',
  email_confirmed_at: '2024-01-01T00:00:00Z',
  phone: '',
  confirmed_at: '2024-01-01T00:00:00Z',
  last_sign_in_at: '2024-01-01T00:00:00Z',
  app_metadata: {},
  user_metadata: {},
  identities: [],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

/**
 * Mock journal entry for testing
 */
export const createMockJournalEntry = (overrides: Partial<JournalEntry> = {}): JournalEntry => ({
  id: 'entry-123',
  user_id: 'user-123',
  title: 'Test Journal Entry',
  content: 'This is a test journal entry content.',
  emotion: 'happy',
  mood_score: 7,
  ai_summary: 'A positive test entry',
  ai_emotion: 'joy',
  ai_encouragement: 'Great job on your reflection!',
  ai_reflection_question: 'What made this moment special?',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

/**
 * Mock conversation for testing
 */
export const createMockConversation = (overrides: Partial<ReflectionConversation> = {}): ReflectionConversation => ({
  id: 'conv-123',
  journal_entry_id: 'entry-123',
  user_id: 'user-123',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

/**
 * Mock conversation message for testing
 */
export const createMockMessage = (overrides: Partial<ConversationMessage> = {}): ConversationMessage => ({
  id: 'msg-123',
  conversation_id: 'conv-123',
  sender_type: 'user',
  message_content: 'Test message content',
  message_type: 'text',
  ai_metadata: null,
  created_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

/**
 * Create a sequence of mock messages for testing conversation flow
 */
export const createMockConversationFlow = (conversationId: string = 'conv-123'): ConversationMessage[] => [
  createMockMessage({
    id: 'msg-1',
    conversation_id: conversationId,
    sender_type: 'ai',
    message_content: 'How are you feeling about your journal entry?',
    message_type: 'reflection_question',
    ai_metadata: {
      emotion: 'curious',
      confidence: 0.9,
      processingTime: 150,
    },
    created_at: '2024-01-01T00:00:00Z',
  }),
  createMockMessage({
    id: 'msg-2',
    conversation_id: conversationId,
    sender_type: 'user',
    message_content: 'I feel really good about it. It was a meaningful day.',
    message_type: 'text',
    created_at: '2024-01-01T00:01:00Z',
  }),
  createMockMessage({
    id: 'msg-3',
    conversation_id: conversationId,
    sender_type: 'ai',
    message_content: 'That\'s wonderful to hear! What made it particularly meaningful for you?',
    message_type: 'follow_up',
    ai_metadata: {
      emotion: 'supportive',
      confidence: 0.85,
      processingTime: 200,
    },
    created_at: '2024-01-01T00:02:00Z',
  }),
];

/**
 * Mock conversation service responses
 */
export const createMockConversationServiceResponses = () => ({
  createReflectionConversation: vi.fn(),
  getConversationByJournalEntry: vi.fn(),
  getConversationMessages: vi.fn(),
  createConversationMessage: vi.fn(),
  deleteConversation: vi.fn(),
  getUserConversationCount: vi.fn(),
  bulkDeleteConversations: vi.fn(),
  getConversationAnalytics: vi.fn(),
});

/**
 * Mock AI conversation service responses
 */
export const createMockAIServiceResponses = () => ({
  generateAIConversationResponse: vi.fn(),
});

/**
 * Create a test query client with disabled retries
 */
export const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

/**
 * Create auth context value for testing
 */
export const createMockAuthContext = (user: User | null = createMockUser()) => ({
  user,
  loading: false,
  signIn: vi.fn(),
  signUp: vi.fn(),
  signOut: vi.fn(),
  resetPassword: vi.fn(),
  updatePassword: vi.fn(),
  updateProfile: vi.fn(),
});

/**
 * Test wrapper component factory
 */
export const createTestWrapper = (
  user: User | null = createMockUser(),
  queryClient: QueryClient = createTestQueryClient()
) => {
  const authContextValue = createMockAuthContext(user);

  return ({ children }: { children: React.ReactNode }) => (
    React.createElement(QueryClient.Provider, { client: queryClient },
      React.createElement(AuthContext.Provider, { value: authContextValue },
        children
      )
    )
  );
};

/**
 * Setup conversation service mocks with default successful responses
 */
export const setupConversationServiceMocks = () => {
  const mocks = createMockConversationServiceResponses();
  
  // Default successful responses
  mocks.createReflectionConversation.mockResolvedValue({
    success: true,
    data: createMockConversation(),
  });

  mocks.getConversationByJournalEntry.mockResolvedValue({
    success: true,
    data: createMockConversation(),
  });

  mocks.getConversationMessages.mockResolvedValue({
    success: true,
    data: createMockConversationFlow(),
  });

  mocks.createConversationMessage.mockResolvedValue({
    success: true,
    data: createMockMessage(),
  });

  mocks.deleteConversation.mockResolvedValue({
    success: true,
    data: undefined,
  });

  mocks.getUserConversationCount.mockResolvedValue({
    success: true,
    data: 5,
  });

  mocks.bulkDeleteConversations.mockResolvedValue({
    success: true,
    data: { deletedCount: 3 },
  });

  mocks.getConversationAnalytics.mockResolvedValue({
    success: true,
    data: {
      totalConversations: 10,
      totalMessages: 45,
      averageMessagesPerConversation: 4.5,
      mostActiveDay: '2024-01-01',
      conversationsByMonth: [
        { month: '2024-01', count: 10 },
      ],
    },
  });

  return mocks;
};

/**
 * Setup AI service mocks with default successful responses
 */
export const setupAIServiceMocks = () => {
  const mocks = createMockAIServiceResponses();

  mocks.generateAIConversationResponse.mockResolvedValue({
    success: true,
    data: {
      message: 'This is a test AI response.',
      message_type: 'follow_up',
      metadata: {
        emotion: 'supportive',
        confidence: 0.9,
        processingTime: 150,
        modelVersion: 'gemini-pro',
        context: {
          messageHistory: 2,
          journalContext: true,
          moodScore: 7,
        },
      },
      success: true,
    },
  });

  return mocks;
};

/**
 * Setup all conversation-related mocks
 */
export const setupAllConversationMocks = () => {
  const conversationMocks = setupConversationServiceMocks();
  const aiMocks = setupAIServiceMocks();

  // Mock the actual modules
  vi.doMock('@/services/conversationService', () => conversationMocks);
  vi.doMock('@/services/aiConversationService', () => aiMocks);

  // Mock toast notifications
  vi.doMock('sonner', () => ({
    toast: {
      success: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
      warning: vi.fn(),
    },
  }));

  return {
    conversationMocks,
    aiMocks,
  };
};

/**
 * Cleanup function for tests
 */
export const cleanupConversationMocks = () => {
  vi.clearAllMocks();
  vi.resetAllMocks();
};

/**
 * Wait for async operations in tests
 */
export const waitForAsync = (ms: number = 0) => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Assert that a mock was called with specific parameters
 */
export const expectMockCalledWith = (mockFn: any, expectedArgs: any[]) => {
  expect(mockFn).toHaveBeenCalledWith(...expectedArgs);
};

/**
 * Assert that a mock was called a specific number of times
 */
export const expectMockCalledTimes = (mockFn: any, times: number) => {
  expect(mockFn).toHaveBeenCalledTimes(times);
};

/**
 * Create a mock error response
 */
export const createMockErrorResponse = (message: string, code: string = 'TEST_ERROR') => ({
  success: false,
  error: {
    message,
    code,
  },
});

/**
 * Create a mock success response
 */
export const createMockSuccessResponse = <T>(data: T) => ({
  success: true,
  data,
  meta: {
    timestamp: new Date().toISOString(),
  },
});
