/**
 * Memory Extraction Component
 * Component for testing and demonstrating memory extraction functionality
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Brain, Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { useMemoryContext } from '@/hooks/useMemoryContext';
import { UserMemory } from '@/types';

/**
 * Memory extraction test component
 */
export const MemoryExtraction: React.FC = () => {
  const {
    memories,
    isExtracting,
    extractionError,
    stats,
    extractFromJournalEntry,
    extractFromConversation,
    addMemory,
    removeMemory,
    getMemoryContext,
    clearMemories,
  } = useMemoryContext();

  const [testContent, setTestContent] = useState('');
  const [sourceType, setSourceType] = useState<'journal_entry' | 'conversation'>('journal_entry');
  const [lastExtractionResult, setLastExtractionResult] = useState<UserMemory[] | null>(null);

  /**
   * Handle memory extraction test
   */
  const handleExtractMemories = async () => {
    if (!testContent.trim()) return;

    try {
      let response;
      
      if (sourceType === 'journal_entry') {
        response = await extractFromJournalEntry({
          title: 'Test Entry',
          content: testContent,
          emotion: 'neutral',
          moodScore: 5,
        });
      } else {
        // Parse conversation format for testing
        const messages = testContent.split('\n').filter(line => line.trim()).map(line => {
          const isUser = line.toLowerCase().startsWith('user:') || line.toLowerCase().startsWith('me:');
          const content = line.replace(/^(user:|me:|amber:|ai:)/i, '').trim();
          return {
            sender_type: isUser ? 'user' as const : 'ai' as const,
            message_content: content,
          };
        });

        response = await extractFromConversation({ messages });
      }

      setLastExtractionResult(response.memories);
    } catch (error) {
      console.error('Memory extraction failed:', error);
    }
  };

  /**
   * Get category color for badges
   */
  const getCategoryColor = (category: UserMemory['category']) => {
    const colors = {
      fact: 'bg-blue-100 text-blue-800',
      emotion: 'bg-red-100 text-red-800',
      event: 'bg-green-100 text-green-800',
      goal: 'bg-purple-100 text-purple-800',
      preference: 'bg-yellow-100 text-yellow-800',
      identity: 'bg-indigo-100 text-indigo-800',
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Memory Extraction Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-amber-600" />
            Memory Extraction Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Source Type Selection */}
          <div className="flex gap-2">
            <Button
              variant={sourceType === 'journal_entry' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSourceType('journal_entry')}
            >
              Journal Entry
            </Button>
            <Button
              variant={sourceType === 'conversation' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSourceType('conversation')}
            >
              Conversation
            </Button>
          </div>

          {/* Test Content Input */}
          <Textarea
            placeholder={
              sourceType === 'journal_entry'
                ? 'Enter a journal entry to extract memories from...'
                : 'Enter a conversation (format: "User: message" and "Amber: response" on separate lines)...'
            }
            value={testContent}
            onChange={(e) => setTestContent(e.target.value)}
            rows={6}
            className="resize-none"
          />

          {/* Extract Button */}
          <Button
            onClick={handleExtractMemories}
            disabled={!testContent.trim() || isExtracting}
            className="w-full"
          >
            {isExtracting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Extracting Memories...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-2" />
                Extract Memories
              </>
            )}
          </Button>

          {/* Extraction Error */}
          {extractionError && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-700">{extractionError}</span>
            </div>
          )}

          {/* Last Extraction Result */}
          {lastExtractionResult && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Last Extraction Result:</h4>
              {lastExtractionResult.length === 0 ? (
                <p className="text-sm text-gray-500">No memories extracted</p>
              ) : (
                <div className="space-y-2">
                  {lastExtractionResult.map((memory, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <Badge className={getCategoryColor(memory.category)}>
                        {memory.category}
                      </Badge>
                      <span className="font-medium text-sm">{memory.key}:</span>
                      <span className="text-sm">{memory.value}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Memories */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Current Memories ({memories.length})</span>
            {memories.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearMemories}
              >
                Clear All
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {memories.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No memories stored yet</p>
          ) : (
            <div className="space-y-3">
              {memories.map((memory, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge className={getCategoryColor(memory.category)}>
                      {memory.category}
                    </Badge>
                    <div>
                      <span className="font-medium">{memory.key}:</span>
                      <span className="ml-2">{memory.value}</span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMemory(memory.key)}
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Memory Context Preview */}
      {memories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Memory Context for AI</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-gray-50 p-3 rounded-lg whitespace-pre-wrap">
              {getMemoryContext()}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Extraction Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600">{stats.totalExtractions}</div>
              <div className="text-sm text-gray-500">Total Extractions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.successfulExtractions}</div>
              <div className="text-sm text-gray-500">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.totalMemoriesExtracted}</div>
              <div className="text-sm text-gray-500">Memories Found</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(stats.averageProcessingTime)}ms
              </div>
              <div className="text-sm text-gray-500">Avg Time</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
