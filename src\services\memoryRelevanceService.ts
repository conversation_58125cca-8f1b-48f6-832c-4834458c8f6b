/**
 * Memory Relevance Service
 * Service for retrieving memories based on relevance and importance
 */

import { supabase } from '@/integrations/supabase/client';
import { UserMemory, MemoryCategory, ApiResponse } from '@/types';
import { generateEmbedding, calculateCosineSimilarity } from './embeddingService';

/**
 * Relevance-based memory retrieval options
 */
export interface RelevanceRetrievalOptions {
  /** Maximum number of memories to return (default: 10) */
  limit?: number;
  /** Minimum importance score to include (default: 4) */
  minImportance?: number;
  /** Minimum relevance score to include (default: 0.2) */
  minRelevance?: number;
  /** Optional category filter */
  category?: MemoryCategory;
  /** Whether to factor in recency when calculating relevance (default: true) */
  includeRecency?: boolean;
  /** Relevance weight in combined score calculation (default: 0.6) */
  relevanceWeight?: number;
  /** Importance weight in combined score calculation (default: 0.4) */
  importanceWeight?: number;
}

/**
 * Database memory type for internal use
 * This is needed because the memory table is not defined in the Supabase types
 */
interface DatabaseMemory {
  id: string;
  user_id: string;
  key: string;
  value: string;
  category: MemoryCategory;
  importance: number;
  embedding?: string | null; // JSON string of embedding vector
  created_at: string;
  updated_at: string;
}

/**
 * Type guard to check if an object is a valid DatabaseMemory
 */
function isDatabaseMemory(obj: any): obj is DatabaseMemory {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.key === 'string' &&
    typeof obj.value === 'string' &&
    typeof obj.category === 'string' &&
    typeof obj.importance === 'number'
  );
}

/**
 * Memory with relevance score
 */
export interface RelevantMemory extends UserMemory {
  relevance: number;
  combinedScore: number;
}

/**
 * Calculate relevance score between query and memory using semantic similarity
 * Falls back to word-matching if embeddings are not available
 * @returns Score between 0-1 where 1 is most relevant
 */
const calculateRelevanceScore = async (
  query: string,
  memory: UserMemory & { embedding?: number[] | null },
  context?: { category?: MemoryCategory, recency?: number }
): Promise<number> => {
  let relevance = 0;

  // Try semantic similarity first if embeddings are available
  if (memory.embedding && Array.isArray(memory.embedding)) {
    try {
      // Generate embedding for the query
      const queryEmbeddingResult = await generateEmbedding(query);

      if (queryEmbeddingResult.success) {
        // Calculate cosine similarity between query and memory embeddings
        const similarity = calculateCosineSimilarity(queryEmbeddingResult.data, memory.embedding);

        // Convert similarity to relevance score (cosine similarity is already 0-1)
        relevance = Math.max(0, similarity);

        console.log(`🧠 [DEBUG] Semantic similarity for "${memory.key}": ${relevance.toFixed(3)}`);
      } else {
        console.warn('🧠 [DEBUG] Failed to generate query embedding, falling back to word matching');
        relevance = calculateWordMatchingRelevance(query, memory);
      }
    } catch (error) {
      console.warn('🧠 [DEBUG] Error in semantic similarity calculation, falling back to word matching:', error);
      relevance = calculateWordMatchingRelevance(query, memory);
    }
  } else {
    // Fall back to word-matching if no embedding available
    console.log(`🧠 [DEBUG] No embedding available for "${memory.key}", using word matching`);
    relevance = calculateWordMatchingRelevance(query, memory);
  }

  // Boost relevance for category matches
  if (context?.category && memory.category === context.category) {
    relevance *= 1.25; // 25% boost for matching category
  }

  // Apply recency factor if provided
  if (context?.recency !== undefined) {
    // Recency is normalized 0-1 where 1 is most recent
    relevance = (relevance * 0.7) + (context.recency * 0.3);
  }

  return Math.min(1, relevance); // Cap at 1.0
};

/**
 * Fallback word-matching relevance calculation
 * @returns Score between 0-1 where 1 is most relevant
 */
const calculateWordMatchingRelevance = (
  query: string,
  memory: UserMemory
): number => {
  // Normalize text for comparison
  const normalizeText = (text: string) => text.toLowerCase().trim();
  const queryText = normalizeText(query);
  const memoryKey = normalizeText(memory.key);
  const memoryValue = normalizeText(memory.value);

  // Calculate text similarity (TF-IDF style approach)
  const queryWords = queryText.split(/\s+/).filter(w => w.length > 2);
  const memoryWords = [...memoryKey.split(/\s+/), ...memoryValue.split(/\s+/)].filter(w => w.length > 2);

  // Count matching words
  let matchCount = 0;
  for (const word of queryWords) {
    if (memoryWords.some(mw => mw.includes(word) || word.includes(mw))) {
      matchCount++;
    }
  }

  // Base relevance on word matches
  return queryWords.length > 0 ? matchCount / queryWords.length : 0;
};

/**
 * Get memories based on relevance to query and importance
 */
export const getRelevantMemories = async (
  query: string,
  options: RelevanceRetrievalOptions = {}
): Promise<ApiResponse<RelevantMemory[]>> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: { 
          message: 'User not authenticated',
          code: 'AUTH_ERROR'
        },
      };
    }

    const {
      limit = 10,
      minImportance = 4,
      minRelevance = 0.2,
      category,
      includeRecency = true,
      relevanceWeight = 0.6,
      importanceWeight = 0.4,
    } = options;

    // Since the memory table is not properly typed in Supabase schema,
    // we'll use a direct fetch with any type and validate the results
    const { data, error } = await supabase
      .from('memory')
      .select('*')
      .eq('user_id', user.id)
      .gte('importance', minImportance)
      .eq(category ? 'category' : 'user_id', category || user.id) as { 
        data: any[] | null; 
        error: { message: string; code: string } | null 
      };

    if (error) {
      console.error('Error fetching memories for relevance calculation:', error);
      return {
        success: false,
        error: { 
          message: error.message,
          code: error.code
        },
      };
    }

    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // Convert to UserMemory format and calculate relevance scores
    const memoriesWithRelevance: RelevantMemory[] = [];

    if (data && Array.isArray(data)) {
      for (const dbMemory of data.filter(isDatabaseMemory)) {
        // Parse embedding from JSON string if available
        let embedding: number[] | null = null;
        if (dbMemory.embedding) {
          try {
            const parsed = JSON.parse(dbMemory.embedding);
            if (Array.isArray(parsed) && parsed.length === 384) {
              embedding = parsed;
            }
          } catch (error) {
            console.warn('🧠 [DEBUG] Failed to parse embedding for memory:', dbMemory.key, error);
          }
        }

        const memory: UserMemory & { embedding?: number[] | null } = {
          key: dbMemory.key,
          value: dbMemory.value,
          category: dbMemory.category,
          importance: dbMemory.importance,
          embedding,
        };

        // Calculate recency factor if needed (0-1 scale)
        let recency: number | undefined;
        if (includeRecency && dbMemory.updated_at) {
          const updatedAt = new Date(dbMemory.updated_at);
          const now = new Date();
          const ageInDays = (now.getTime() - updatedAt.getTime()) / (1000 * 60 * 60 * 24);
          recency = Math.max(0, Math.min(1, 1 - (ageInDays / 30))); // Scale: 0 days = 1.0, 30+ days = 0.0
        }

        // Calculate relevance score with optional category
        const contextParams: { category?: MemoryCategory; recency?: number } = {};
        if (category) contextParams.category = category;
        if (recency !== undefined) contextParams.recency = recency;

        const relevance = await calculateRelevanceScore(query, memory, contextParams);

        memoriesWithRelevance.push({
          key: memory.key,
          value: memory.value,
          category: memory.category,
          importance: memory.importance,
          relevance,
          combinedScore: 0
        } as RelevantMemory);
      }
    }

    // Filter by minimum relevance
    const relevantMemories = memoriesWithRelevance.filter((m: RelevantMemory) => m.relevance >= minRelevance);
    
    // Calculate combined score (importance + relevance)
    const scoredMemories = relevantMemories.map((memory: RelevantMemory) => {
      // Normalize importance to 0-1 scale
      const importanceScore = (memory.importance || 5) / 10;
      
      // Combined score: weighted combination of relevance and importance
      const combinedScore = (memory.relevance * relevanceWeight) + (importanceScore * importanceWeight);
      
      return { ...memory, combinedScore };
    });
    
    // Sort by combined score and take top results
    const sortedMemories = scoredMemories
      .sort((a: RelevantMemory, b: RelevantMemory) => b.combinedScore - a.combinedScore)
      .slice(0, limit);
    
    console.log('🧠 [DEBUG] Retrieved relevant memories:', {
      query,
      total: sortedMemories.length,
      options,
      topScores: sortedMemories.slice(0, 3).map((m: RelevantMemory) => ({
        key: m.key,
        relevance: m.relevance,
        importance: m.importance,
        combinedScore: m.combinedScore
      }))
    });

    return {
      success: true,
      data: sortedMemories,
    };
  } catch (error: any) {
    console.error('Unexpected error fetching relevant memories:', error);
    return {
      success: false,
      error: { 
        message: error.message || 'Failed to fetch relevant memories',
        code: error.code || 'UNKNOWN_ERROR'
      },
    };
  }
};

/**
 * Get memories related to a specific context (e.g., journal entry or conversation)
 */
export const getContextRelevantMemories = async (
  context: string,
  options: Omit<RelevanceRetrievalOptions, 'minRelevance'> = {}
): Promise<ApiResponse<RelevantMemory[]>> => {
  // Use a lower relevance threshold for context-based retrieval
  return getRelevantMemories(context, {
    ...options,
    minRelevance: 0.15, // Lower threshold for context relevance
  });
};

// Helper functions can be added here if needed
