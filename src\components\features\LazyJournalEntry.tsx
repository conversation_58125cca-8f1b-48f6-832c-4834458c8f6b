/**
 * Lazy Journal Entry Component
 * Lazy-loaded version of JournalEntry for better performance
 */

import { createLazyComponent } from '@/utils/performance.utils';
import { LoadingSpinner } from '@/components/atoms/LoadingSpinner';

// Lazy load the JournalEntry component
export const LazyJournalEntry = createLazyComponent(
  () => import('./JournalEntry').then(module => ({ default: module.JournalEntry })),
  () => (
    <div className="flex items-center justify-center p-8">
      <LoadingSpinner size="lg" message="Loading journal entry form..." />
    </div>
  ),
  ({ error }) => (
    <div className="flex items-center justify-center p-8 text-red-600">
      <div className="text-center">
        <p className="text-lg font-semibold mb-2">Failed to load journal entry form</p>
        <p className="text-sm">{error.message}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    </div>
  )
);
