# AmberGlow Semantic Embedding Service

A Python Flask microservice that provides semantic embeddings for the AmberGlow journal application using the `sentence-transformers` library with the `all-MiniLM-L6-v2` model.

## Features

- **Semantic Embeddings**: Generate high-quality semantic embeddings using the `all-MiniLM-L6-v2` model
- **REST API**: Simple POST endpoint for embedding generation
- **CORS Support**: Configured for React frontend integration
- **Error Handling**: Comprehensive error handling and validation
- **Logging**: Detailed logging for debugging and monitoring
- **Health Check**: Service health monitoring endpoint

## Requirements

- Python 3.8 or higher
- 2GB+ RAM (for model loading)
- Internet connection (for initial model download)

## Installation

### 1. Create Python Virtual Environment

```bash
# Navigate to the embedding-service directory
cd embedding-service

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 2. Install Dependencies

```bash
# Install required packages
pip install -r requirements.txt
```

**Note**: The first time you run the service, it will download the `all-MiniLM-L6-v2` model (~90MB) from Hugging Face. This is a one-time download.

### 3. Verify Installation

```bash
# Test the installation
python -c "from sentence_transformers import SentenceTransformer; print('Installation successful!')"
```

## Usage

### Starting the Service

```bash
# Make sure you're in the embedding-service directory with venv activated
python app.py
```

The service will start on `http://localhost:5005` by default.

### Environment Variables

You can customize the service using environment variables:

```bash
# Custom host and port
export FLASK_HOST=0.0.0.0
export FLASK_PORT=5005
export FLASK_DEBUG=False

# Then start the service
python app.py
```

### Service Endpoints

#### Health Check
```
GET /health
```

Response:
```json
{
  "status": "healthy",
  "service": "AmberGlow Embedding Service",
  "version": "1.0.0",
  "model": "all-MiniLM-L6-v2",
  "model_status": "loaded",
  "embedding_dimension": 384
}
```

#### Generate Embedding
```
POST /embed
Content-Type: application/json

{
  "text": "The journal entry or memory text to embed"
}
```

Success Response (200):
```json
{
  "embedding": [0.123, -0.456, 0.789, ...]
}
```

Error Response (400):
```json
{
  "error": "Missing required 'text' field in request body"
}
```

## Testing

### Using curl

```bash
# Health check
curl http://localhost:5005/health

# Generate embedding
curl -X POST http://localhost:5005/embed \
  -H "Content-Type: application/json" \
  -d '{"text": "I had a wonderful day at the park with my dog."}'
```

### Using JavaScript (for frontend integration)

```javascript
// Health check
const healthResponse = await fetch('http://localhost:5005/health');
const healthData = await healthResponse.json();
console.log('Service status:', healthData);

// Generate embedding
const embeddingResponse = await fetch('http://localhost:5005/embed', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    text: 'I had a wonderful day at the park with my dog.'
  })
});

const embeddingData = await embeddingResponse.json();
console.log('Embedding:', embeddingData.embedding);
```

## Integration with AmberGlow

This service is designed to replace the current word-matching relevance calculation in the AmberGlow memory system. The semantic embeddings will enable better similarity matching based on meaning rather than simple text matching.

### Frontend Integration

The service is configured with CORS to allow requests from:
- `http://localhost:5173` (Vite dev server)
- `http://localhost:3000` (Alternative React dev server)
- `http://127.0.0.1:5173` (Alternative localhost)

## Troubleshooting

### Common Issues

1. **Model Download Fails**
   - Ensure internet connection
   - Check firewall settings
   - Try running: `python -c "from sentence_transformers import SentenceTransformer; SentenceTransformer('all-MiniLM-L6-v2')"`

2. **Port Already in Use**
   - Change the port: `export FLASK_PORT=5006`
   - Or kill the process using port 5005

3. **Memory Issues**
   - Ensure at least 2GB RAM available
   - Close other applications if needed

4. **CORS Issues**
   - Verify your frontend is running on an allowed origin
   - Check browser console for CORS errors

### Logs

The service logs to both console and `embedding-service.log` file. Check the logs for detailed error information.

## Performance Notes

- **Model Loading**: The model is loaded once at startup (~2-3 seconds)
- **Embedding Generation**: ~10-50ms per text depending on length
- **Memory Usage**: ~500MB for the loaded model
- **Concurrency**: Flask runs in threaded mode for better performance

## Development

### Running in Development Mode

```bash
export FLASK_DEBUG=True
python app.py
```

### Adding Features

The service is designed to be easily extensible. You can:
- Add new endpoints
- Implement different embedding models
- Add caching for frequently requested embeddings
- Implement batch processing for multiple texts
