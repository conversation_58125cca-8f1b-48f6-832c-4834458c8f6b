import { ReactNode } from 'react';
import { Navigation } from './Navigation';

type PageLayoutProps = {
  children: ReactNode;
  /** Remove default padding for full-height sections like Hero */
  fullHeight?: boolean;
};

export default function PageLayout({ children, fullHeight = false }: PageLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50">
      <Navigation />
      <main className={fullHeight ? "" : "container mx-auto px-4 pt-8 pb-8"}>
        {children}
      </main>
    </div>
  );
}
