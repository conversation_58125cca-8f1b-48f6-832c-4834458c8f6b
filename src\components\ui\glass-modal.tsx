/**
 * Glass Modal Component
 * Reusable modal component with consistent glass-effect styling
 * Consolidates the repetitive modal patterns with amber/orange branding
 */

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/utils/utils';
import { BaseComponentProps } from '@/types';

interface GlassModalProps extends BaseComponentProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Modal title */
  title?: string;
  /** Modal description */
  description?: string;
  /** Modal content */
  children: React.ReactNode;
  /** Modal size */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** Whether to show close button */
  showCloseButton?: boolean;
  /** Whether clicking outside closes modal */
  closeOnOutsideClick?: boolean;
  /** Icon to display in header */
  icon?: React.ReactNode;
  /** Icon background color */
  iconBgColor?: string;
  /** Footer content */
  footer?: React.ReactNode;
  /** Whether the modal content should scroll */
  scrollable?: boolean;
  /** Custom content styling */
  contentClassName?: string;
}

/**
 * GlassModal component with consistent glass-effect styling
 */
export const GlassModal: React.FC<GlassModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOutsideClick = true,
  icon,
  iconBgColor = 'bg-amber-100',
  footer,
  scrollable = true,
  contentClassName,
  className,
  testId,
  ...props
}) => {
  // Size classes mapping
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl',
  };

  // Glass effect styling
  const glassEffectStyles = cn(
    'glass-effect border-white/30 bg-white/95 backdrop-blur-md shadow-2xl',
    scrollable && 'max-h-[90vh] overflow-y-auto',
    sizeClasses[size],
    contentClassName
  );

  const handleOpenChange = (open: boolean) => {
    if (!open && closeOnOutsideClick) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent
        className={glassEffectStyles}
        aria-describedby={description ? 'modal-description' : undefined}
        data-testid={testId}
        {...props}
      >
        {/* Header */}
        {(title || description || icon) && (
          <DialogHeader>
            {(title || icon) && (
              <div className="flex items-center gap-3">
                {icon && (
                  <div className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center',
                    iconBgColor
                  )}>
                    {icon}
                  </div>
                )}
                {title && (
                  <DialogTitle className="text-2xl text-gradient">
                    {title}
                  </DialogTitle>
                )}
              </div>
            )}
            {description && (
              <DialogDescription 
                id="modal-description"
                className="text-muted-foreground"
              >
                {description}
              </DialogDescription>
            )}
          </DialogHeader>
        )}

        {/* Content */}
        <div className={cn(
          'space-y-6',
          (title || description || icon) && 'mt-6',
          className
        )}>
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-3 mt-6">
            {footer}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

/**
 * ConfirmationDialog component for confirmation modals
 */
interface ConfirmationDialogProps extends BaseComponentProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Function called when user confirms */
  onConfirm: () => void;
  /** Modal title */
  title: string;
  /** Modal description/message */
  description: string;
  /** Confirm button text */
  confirmText?: string;
  /** Cancel button text */
  cancelText?: string;
  /** Whether the action is destructive (affects styling) */
  variant?: 'default' | 'destructive';
  /** Whether the confirm action is loading */
  isLoading?: boolean;
  /** Icon to display */
  icon?: 'warning' | 'trash' | 'info' | React.ReactNode;
}

export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  isLoading = false,
  icon = 'warning',
  testId,
  ...props
}) => {
  // Icon mapping
  const iconMap = {
    warning: (
      <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    ),
    trash: (
      <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
      </svg>
    ),
    info: (
      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  };

  const renderIcon = () => {
    if (typeof icon === 'string') {
      return iconMap[icon as keyof typeof iconMap];
    }
    return icon;
  };

  const handleConfirm = () => {
    if (!isLoading) {
      onConfirm();
    }
  };

  const handleCancel = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const footer = (
    <>
      <button
        type="button"
        onClick={handleCancel}
        disabled={isLoading}
        className={cn(
          'flex-1 px-4 py-2 border border-gray-300 rounded-md',
          'text-gray-700 bg-white hover:bg-gray-50',
          'focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'transition-colors duration-200'
        )}
      >
        {cancelText}
      </button>
      <button
        type="button"
        onClick={handleConfirm}
        disabled={isLoading}
        className={cn(
          'flex-1 px-4 py-2 rounded-md',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'transition-colors duration-200',
          variant === 'destructive'
            ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
            : 'bg-amber-500 hover:bg-amber-600 text-white focus:ring-amber-500'
        )}
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
            Processing...
          </div>
        ) : (
          confirmText
        )}
      </button>
    </>
  );

  return (
    <GlassModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      description={description}
      size="sm"
      icon={renderIcon()}
      iconBgColor={variant === 'destructive' ? 'bg-red-100' : 'bg-amber-100'}
      footer={footer}
      testId={testId}
      {...props}
    />
  );
};
