/**
 * Local LLM Service with Ollama Integration
 * Service for interacting with local LLM using OpenAI-compatible API
 */

import { LLMMessage, LocalLLMResponse, AIServiceConfig } from '@/types';
import { handleApiError } from '@/utils/errorHandler';

/**
 * Default configuration for local LLM service
 */
const DEFAULT_LOCAL_LLM_CONFIG = {
  endpoint: 'http://localhost:11434/v1/chat/completions',
  model: 'llama3.1:8b',
  maxRetries: 3,
  baseDelay: 1000,
  timeout: 30000,
} as const;

/**
 * Error types specific to local LLM
 */
export type LocalLLMErrorType =
  | 'service_unavailable'
  | 'invalid_response'
  | 'timeout'
  | 'rate_limit_exceeded'
  | 'model_not_found'
  | 'service_error'
  | 'unknown_error';

/**
 * Local LLM service error
 */
export class LocalLLMError extends Error {
  constructor(
    public type: LocalLLMErrorType,
    message: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'LocalLLMError';
  }
}

/**
 * Retry function with exponential backoff
 */
const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = DEFAULT_LOCAL_LLM_CONFIG.maxRetries,
  baseDelay: number = DEFAULT_LOCAL_LLM_CONFIG.baseDelay
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

/**
 * Validate local LLM response structure
 */
const validateLLMResponse = (response: any): LocalLLMResponse => {
  if (!response || typeof response !== 'object') {
    throw new LocalLLMError('invalid_response', 'Response is not a valid object');
  }

  const required = ['summary', 'emotion', 'encouragement', 'reflection_question'];
  const missing = required.filter(field => !response[field] || typeof response[field] !== 'string');
  
  if (missing.length > 0) {
    throw new LocalLLMError(
      'invalid_response', 
      `Missing or invalid required fields: ${missing.join(', ')}`
    );
  }

  return {
    summary: response.summary.trim(),
    emotion: response.emotion.trim(),
    encouragement: response.encouragement.trim(),
    reflection_question: response.reflection_question.trim(),
  };
};

/**
 * Call local LLM with OpenAI-compatible API
 */
export const callLocalLLM = async (
  messages: LLMMessage[],
  config?: Partial<AIServiceConfig>
): Promise<string> => {
  const llmConfig = {
    ...DEFAULT_LOCAL_LLM_CONFIG,
    endpoint: config?.localLLMEndpoint || DEFAULT_LOCAL_LLM_CONFIG.endpoint,
    model: config?.model || DEFAULT_LOCAL_LLM_CONFIG.model,
    maxRetries: config?.maxRetries || DEFAULT_LOCAL_LLM_CONFIG.maxRetries,
    baseDelay: config?.baseDelay || DEFAULT_LOCAL_LLM_CONFIG.baseDelay,
    timeout: config?.timeout || DEFAULT_LOCAL_LLM_CONFIG.timeout,
  };

  return retryWithBackoff(async () => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), llmConfig.timeout);

    try {
      const response = await fetch(llmConfig.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: llmConfig.model,
          messages: messages,
          temperature: 0.7,
          max_tokens: 2000,
          stream: false,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 404) {
          throw new LocalLLMError('model_not_found', `Model ${llmConfig.model} not found`);
        }
        if (response.status === 429) {
          throw new LocalLLMError('rate_limit_exceeded', 'Rate limit exceeded');
        }
        if (response.status >= 500) {
          throw new LocalLLMError('service_unavailable', 'Local LLM service unavailable');
        }
        throw new LocalLLMError('unknown_error', `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new LocalLLMError('invalid_response', 'Invalid response format from local LLM');
      }

      return data.choices[0].message.content;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof LocalLLMError) {
        throw error;
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new LocalLLMError('timeout', 'Request timed out', error);
        }
        if (error.message.includes('fetch')) {
          throw new LocalLLMError('service_unavailable', 'Cannot connect to local LLM service', error);
        }
      }
      
      throw new LocalLLMError('unknown_error', 'Unknown error occurred', error as Error);
    }
  }, llmConfig.maxRetries, llmConfig.baseDelay);
};

/**
 * Generate journal reflection using local LLM
 */
export const generateLocalLLMReflection = async (
  input: { title: string; content: string; emotion: string; moodScore: number },
  config?: Partial<AIServiceConfig>
): Promise<LocalLLMResponse> => {
  const systemMessage: LLMMessage = {
    role: 'system',
    content: `You are Amber, a warm and supportive AI friend who helps people reflect on their journal entries. 
    
Your responses should be:
- Conversational and friend-like (not clinical or therapeutic)
- Empathetic and validating
- Encouraging and supportive
- Focused on helping the person understand their feelings

Always respond with a JSON object containing exactly these fields:
- summary: A brief, empathetic summary of the main themes (1-2 sentences)
- emotion: The primary emotion you sense (single word or short phrase)
- encouragement: A warm, supportive message that validates their feelings (2-3 sentences)
- reflection_question: A gentle, open-ended question to help them reflect further (1 sentence)

Keep the tone conversational and avoid clinical language.

IMPORTANT: Respond ONLY with valid JSON. No other text before or after.`,
  };

  const userMessage: LLMMessage = {
    role: 'user',
    content: `Here is a journal entry I'd like you to reflect on:

Title: "${input.title}"
Content: "${input.content}"

The person selected "${input.emotion}" as their emotion and rated their mood as ${input.moodScore}/10.

Please provide a thoughtful reflection in the JSON format specified.`,
  };

  try {
    const response = await callLocalLLM([systemMessage, userMessage], config);
    console.log('🔍 Raw LLM reflection response:', response);
    
    // Enhanced cleaning and parsing of the response
    let cleanedResponse = response.trim();
    
    // Remove markdown code blocks and language indicators
    cleanedResponse = cleanedResponse
      .replace(/```json\s*/gi, '')
      .replace(/```\s*/g, '')
      .replace(/^json\s*/gi, '')
      .trim();
    
    // Handle cases where the response might have extra text before/after JSON
    if (cleanedResponse.includes('{') && cleanedResponse.includes('}')) {
      // Extract just the JSON part
      cleanedResponse = cleanedResponse
        .replace(/^[^{]*/, '') // Remove everything before first {
        .replace(/[^}]*$/, ''); // Remove everything after last }
      
      // Check if JSON is complete (has matching braces)
      const openBraces = (cleanedResponse.match(/\{/g) || []).length;
      const closeBraces = (cleanedResponse.match(/\}/g) || []).length;
      if (openBraces > closeBraces) {
        // Try to complete the JSON if it's missing closing braces
        cleanedResponse += '}'.repeat(openBraces - closeBraces);
      }
      
      // Fix common JSON formatting issues
      cleanedResponse = cleanedResponse
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Quote unquoted keys
        .replace(/:\s*'([^']*)'/g, ': "$1"') // Replace single quotes with double quotes
        .trim();
    }
    
    console.log('🔍 Cleaned response for parsing:', cleanedResponse);
    
    let parsed: any;
    try {
      parsed = JSON.parse(cleanedResponse);
      console.log('✅ Successfully parsed JSON response');
    } catch (parseError) {
      console.error('❌ Failed to parse local LLM response:', parseError);
      console.error('Raw response:', response);
      console.error('Cleaned response:', cleanedResponse);
      
      // Try to extract a reflective response from the text
      console.log('🔄 Attempting to extract reflective response from text');
      
      // Create a fallback response based on the text content
      const extractReflection = (text: string): LocalLLMResponse => {
        // Default values
        let reflection: LocalLLMResponse = {
          summary: "Here's a reflective response:",
          emotion: input.emotion || "reflective",
          encouragement: "I understand what you're sharing and appreciate your openness.",
          reflection_question: "What else would you like to explore about this?"
        };
        
        // Try to extract parts that look like they could be the summary, emotion, etc.
        const summaryMatch = text.match(/(?:summary|theme|feeling)[:\s]+([^\n.]+[.!?])/i);
        if (summaryMatch && summaryMatch[1]) {
          reflection.summary = summaryMatch[1].trim();
        }
        
        const emotionMatch = text.match(/(?:emotion|feeling|mood)[:\s]+([^\n.,;]+)/i);
        if (emotionMatch && emotionMatch[1]) {
          reflection.emotion = emotionMatch[1].trim();
        }
        
        const encouragementMatch = text.match(/(?:encouragement|support|message)[:\s]+([^\n]+\. [^\n]+[.!?])/i);
        if (encouragementMatch && encouragementMatch[1]) {
          reflection.encouragement = encouragementMatch[1].trim();
        }
        
        const questionMatch = text.match(/(?:question|reflect|wonder|curious)[^\n]*[?]/i);
        if (questionMatch && questionMatch[0]) {
          reflection.reflection_question = questionMatch[0].trim();
        }
        
        return reflection;
      };
      
      // Try to extract a reflection from the text
      const extractedReflection = extractReflection(response);
      console.log('🔄 Extracted reflection from text:', extractedReflection);
      
      return extractedReflection;
    }

    // Validate and ensure all required fields are present
    try {
      return validateLLMResponse(parsed);
    } catch (validationError) {
      console.warn('⚠️ Validation failed, attempting to fix response structure:', validationError);
      
      // Try to fix the response structure
      const fixedResponse: LocalLLMResponse = {
        summary: parsed.summary || parsed.theme || parsed.message || "I've reflected on what you shared.",
        emotion: parsed.emotion || parsed.feeling || input.emotion || "reflective",
        encouragement: parsed.encouragement || parsed.support || parsed.message || "Thank you for sharing your thoughts.",
        reflection_question: parsed.reflection_question || parsed.question || "What else would you like to explore about this?"
      };
      
      console.log('🔄 Fixed response structure:', fixedResponse);
      return fixedResponse;
    }
  } catch (error) {
    console.error('❌ Local LLM reflection generation failed:', error);
    throw error;
  }
};

/**
 * Generate conversation response using local LLM
 */
export const generateLocalLLMConversation = async (
  userMessage: string,
  conversationHistory: LLMMessage[],
  journalContext: { title: string; content: string; emotion: string; moodScore: number },
  config?: Partial<AIServiceConfig>,
  memoryContext?: string
): Promise<{ message: string; message_type: 'follow_up' | 'reflection_question' }> => {
  const systemMessage: LLMMessage = {
    role: 'system',
    content: `You are Amber, a warm and supportive AI friend having a conversation about a journal entry. You have the knowledge and expertise of an expert therapist, but you communicate as a caring, understanding friend rather than a clinical professional.

Context about the journal entry:
- Title: "${journalContext.title}"
- Content: "${journalContext.content}"
- Emotion: "${journalContext.emotion}"
- Mood Score: ${journalContext.moodScore}/10

${memoryContext ? memoryContext : ''}

Your responses should be:
- Conversational and friend-like (never clinical or formal)
- Empathetic, validating, and deeply supportive
- Thoughtful and substantive (aim for 2-4 sentences, 100-200 words)
- Focused on helping them explore and understand their feelings
- Natural, engaging, and emotionally intelligent
- Use therapeutic insights but express them as a caring friend would
- Ask meaningful follow-up questions that encourage deeper reflection
- Acknowledge their emotions and validate their experiences
- Offer gentle perspectives or insights when appropriate
- Be genuinely curious about their inner world

Communication style:
- Use "I" statements to share understanding ("I can sense that...", "I imagine that might feel...")
- Ask open-ended questions that invite exploration
- Reflect back what you're hearing to show deep listening
- Offer gentle observations about patterns or connections
- Share warmth and genuine care in your tone
- Avoid giving direct advice; instead, help them discover their own insights

CRITICAL: You must respond with ONLY a valid JSON object. No other text before or after.
Use this exact format: {"message": "your response here", "message_type": "follow_up"}

DO NOT respond with plain text. DO NOT add explanations. ONLY JSON.

Example: {"message": "I can really feel the weight of what you're going through in your words. It sounds like there's so much happening beneath the surface, and I'm wondering - when you think about that feeling you described, where do you notice it most in your body? Sometimes our physical sensations can tell us a lot about what our emotions are trying to communicate.", "message_type": "follow_up"}`,
  };

  const messages: LLMMessage[] = [
    systemMessage,
    ...conversationHistory,
    { role: 'user', content: userMessage },
  ];

  try {
    const response = await callLocalLLM(messages, config);
    console.log('🔍 Raw LLM conversation response:', response);

    if (!response || typeof response !== 'string') {
      console.error('Invalid response from callLocalLLM:', response);
      throw new LocalLLMError('invalid_response', 'Empty or invalid response from local LLM');
    }

    // Enhanced cleaning and preparation of the response
    let cleanedResponse = response.trim();

    // Remove markdown code blocks and language indicators
    cleanedResponse = cleanedResponse
      .replace(/```json\s*/gi, '')
      .replace(/```\s*/g, '')
      .replace(/^json\s*/gi, '')
      .trim();
    
    console.log('🔍 After removing markdown:', cleanedResponse);

    // Check if response looks like plain text (no JSON structure)
    if (!cleanedResponse.includes('{') && !cleanedResponse.includes('}')) {
      // If it's plain text, wrap it in JSON format
      if (cleanedResponse.length > 0) {
        console.log('🔄 Converting plain text response to JSON format');
        // Escape any special characters that would break JSON
        const escapedText = cleanedResponse
          .replace(/\\/g, '\\\\')
          .replace(/"/g, '\\"')
          .replace(/\n/g, '\\n')
          .replace(/\r/g, '\\r')
          .replace(/\t/g, '\\t');
        
        cleanedResponse = `{"message": "${escapedText}", "message_type": "follow_up"}`;
        console.log('🔍 Converted to JSON:', cleanedResponse);
      }
    } else {
      // Extract just the JSON part
      const jsonStartIndex = cleanedResponse.indexOf('{');
      const jsonEndIndex = cleanedResponse.lastIndexOf('}') + 1;
      
      if (jsonStartIndex >= 0 && jsonEndIndex > jsonStartIndex) {
        cleanedResponse = cleanedResponse.substring(jsonStartIndex, jsonEndIndex);
        console.log('🔍 Extracted JSON part:', cleanedResponse);
      }
      
      // Check if JSON is complete (has matching braces)
      const openBraces = (cleanedResponse.match(/\{/g) || []).length;
      const closeBraces = (cleanedResponse.match(/\}/g) || []).length;
      
      if (openBraces > closeBraces) {
        // Try to complete the JSON if it's missing closing braces
        cleanedResponse += '}'.repeat(openBraces - closeBraces);
        console.log('🔍 Added missing closing braces:', cleanedResponse);
      }
      
      // Fix common JSON formatting issues
      const fixedResponse = cleanedResponse
        .replace(/,\s*}/g, '}') // Remove trailing commas
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Quote unquoted keys
        .replace(/:\s*'([^']*)'/g, ': "$1"') // Replace single quotes with double quotes
        .replace(/\\'/g, "'") // Fix escaped single quotes
        .trim();
      
      if (fixedResponse !== cleanedResponse) {
        console.log('🔍 Fixed JSON formatting issues:', fixedResponse);
        cleanedResponse = fixedResponse;
      }
    }

    // Try to parse the JSON
    let parsed: any;
    try {
      parsed = JSON.parse(cleanedResponse);
      console.log('✅ Successfully parsed JSON response');
    } catch (parseError) {
      console.error('❌ Failed to parse JSON, trying more aggressive fixes:', parseError);
      
      // More aggressive JSON fixes
      try {
        // Try to fix unescaped quotes within strings
        let fixAttempt = cleanedResponse;
        
        // Find message content and ensure quotes are properly escaped
        const messageMatch = fixAttempt.match(/"message"\s*:\s*"(.*?)",\s*"message_type"/s);
        if (messageMatch && messageMatch[1]) {
          const messageContent = messageMatch[1];
          const fixedMessageContent = messageContent
            .replace(/(?<!\\)"/g, '\\"') // Escape unescaped quotes
            .replace(/\n/g, '\\n') // Escape newlines
            .replace(/\r/g, '\\r'); // Escape carriage returns
          
          fixAttempt = fixAttempt.replace(messageContent, fixedMessageContent);
          console.log('🔍 Attempted to fix unescaped quotes:', fixAttempt);
          
          parsed = JSON.parse(fixAttempt);
          console.log('✅ Successfully parsed JSON after fixing quotes');
        } else {
          throw new Error('Could not identify message content for fixing');
        }
      } catch (secondError) {
        console.error('❌ Second parse attempt failed:', secondError);
        console.error('Raw response:', response);
        console.error('Cleaned response:', cleanedResponse);
        
        // Extract text directly as a last resort
        console.log('🔄 Attempting direct text extraction as fallback');
        
        // Try to extract message from non-JSON response
        let fallbackMessage = response
          .replace(/```json\s*/gi, '')
          .replace(/```\s*/g, '')
          .replace(/^json\s*/gi, '')
          .trim();

        // Try various extraction methods
        // 1. Look for message field in malformed JSON
        const messageMatch = fallbackMessage.match(/"message"\s*:\s*"([^"]+)"/); 
        if (messageMatch && messageMatch[1]) {
          fallbackMessage = messageMatch[1];
          console.log('🔄 Extracted message from field:', fallbackMessage.substring(0, 50));
        } 
        // 2. Try to extract from incomplete JSON
        else {
          const incompleteMatch = fallbackMessage.match(/"message"\s*:\s*"([^"]*)/); 
          if (incompleteMatch && incompleteMatch[1] && incompleteMatch[1].length > 10) {
            fallbackMessage = incompleteMatch[1];
            console.log('🔄 Extracted from incomplete JSON:', fallbackMessage.substring(0, 50));
          } 
          // 3. Try to extract any paragraph that looks like a response
          else {
            // Look for sentences that start with capital letters and end with punctuation
            const paragraphMatch = fallbackMessage.match(/[A-Z][^.!?]*[.!?][^.!?]*[.!?]/);
            if (paragraphMatch && paragraphMatch[0]) {
              fallbackMessage = paragraphMatch[0];
              console.log('🔄 Extracted paragraph:', fallbackMessage.substring(0, 50));
            }
            // 4. Just take any sentence
            else {
              const sentenceMatch = fallbackMessage.match(/[A-Z][^.!?]*[.!?]/);
              if (sentenceMatch && sentenceMatch[0]) {
                fallbackMessage = sentenceMatch[0];
                console.log('🔄 Extracted sentence:', fallbackMessage);
              }
            }
          }
        }

        if (fallbackMessage && fallbackMessage.length > 5) {
          console.log('🔄 Using fallback message extraction');
          return {
            message: fallbackMessage,
            message_type: 'follow_up' as const,
          };
        }
        
        // If all else fails, return a generic response
        console.log('⚠️ All extraction attempts failed, using generic response');
        return {
          message: "I'm here to chat about your journal entry. What would you like to discuss?",
          message_type: 'follow_up' as const,
        };
      }
    }

    if (!parsed.message || typeof parsed.message !== 'string') {
      console.error('❌ Missing or invalid message field in parsed response:', parsed);
      throw new LocalLLMError('invalid_response', 'Missing or invalid message field');
    }

    const messageType = parsed.message_type === 'reflection_question' ? 'reflection_question' : 'follow_up';

    console.log('✅ Successfully parsed conversation response:', {
      message: parsed.message.substring(0, 100) + (parsed.message.length > 100 ? '...' : ''),
      message_type: messageType
    });

    return {
      message: parsed.message.trim(),
      message_type: messageType,
    };
  } catch (error) {
    console.error('❌ Local LLM conversation generation failed:', error);

    // If it's already a LocalLLMError, re-throw it
    if (error instanceof LocalLLMError) {
      throw error;
    }

    // Wrap other errors
    throw new LocalLLMError('service_error', `Conversation generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
