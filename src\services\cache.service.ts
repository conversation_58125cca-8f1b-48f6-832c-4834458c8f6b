/**
 * Cache Service
 * Intelligent caching layer for API responses, user data, and application state
 */

import { PerformanceCacheConfig, CacheEntry, CacheStats } from '@/types/performance';
import { getEnvironmentConfig } from '@/config/environment.config';

/**
 * In-memory cache implementation with TTL and size limits
 */
class MemoryCache<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private config: PerformanceCacheConfig;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalItems: 0,
    totalSize: 0,
    averageAccessTime: 0,
    topKeys: [],
  };

  constructor(config: PerformanceCacheConfig) {
    this.config = config;

    // Set up cleanup interval
    setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  /**
   * Get item from cache
   */
  get(key: string): T | null {
    const startTime = performance.now();
    const fullKey = `${this.config.keyPrefix}:${key}`;
    const entry = this.cache.get(fullKey);

    if (!entry) {
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(fullKey);
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    this.stats.hits++;
    this.updateStats();

    // Track access time
    const accessTime = performance.now() - startTime;
    this.updateAverageAccessTime(accessTime);

    return entry.data;
  }

  /**
   * Set item in cache
   */
  set(key: string, data: T, customTtl?: number): void {
    const fullKey = `${this.config.keyPrefix}:${key}`;
    const ttl = customTtl || this.config.ttl;
    const size = this.estimateSize(data);

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      accessCount: 0,
      lastAccessed: Date.now(),
      key: fullKey,
      size,
    };

    // Check size limits
    if (this.config.maxSize && this.cache.size >= this.config.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(fullKey, entry);
    this.updateStats();

    // Persist to localStorage if configured
    if (this.config.persist) {
      this.persistToStorage(fullKey, entry);
    }
  }

  /**
   * Delete item from cache
   */
  delete(key: string): boolean {
    const fullKey = `${this.config.keyPrefix}:${key}`;
    const deleted = this.cache.delete(fullKey);

    if (deleted) {
      this.updateStats();

      // Remove from localStorage if persisted
      if (this.config.persist) {
        localStorage.removeItem(fullKey);
      }
    }

    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    if (this.config.persist) {
      // Remove persisted entries
      this.cache.forEach((_, key) => {
        localStorage.removeItem(key);
      });
    }

    this.cache.clear();
    this.resetStats();
  }

  /**
   * Check if key exists in cache
   */
  has(key: string): boolean {
    const fullKey = `${this.config.keyPrefix}:${key}`;
    const entry = this.cache.get(fullKey);

    if (!entry) return false;

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(fullKey);
      return false;
    }

    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      if (this.config.persist) {
        localStorage.removeItem(key);
      }
    });

    if (expiredKeys.length > 0) {
      this.updateStats();
    }
  }

  /**
   * Evict least recently used entry
   */
  private evictLeastRecentlyUsed(): void {
    let lruKey: string | null = null;
    let lruTime = Date.now();

    this.cache.forEach((entry, key) => {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    });

    if (lruKey) {
      this.cache.delete(lruKey);
      if (this.config.persist) {
        localStorage.removeItem(lruKey);
      }
    }
  }

  /**
   * Estimate data size in bytes
   */
  private estimateSize(data: T): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      return 0;
    }
  }

  /**
   * Persist entry to localStorage
   */
  private persistToStorage(key: string, entry: CacheEntry<T>): void {
    try {
      localStorage.setItem(key, JSON.stringify(entry));
    } catch (error) {
      console.warn('Failed to persist cache entry to localStorage:', error);
    }
  }

  /**
   * Load persisted entries from localStorage
   */
  private loadFromStorage(): void {
    if (!this.config.persist) return;

    const prefix = this.config.keyPrefix;

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(prefix)) {
        try {
          const item = localStorage.getItem(key);
          if (item) {
            const entry: CacheEntry<T> = JSON.parse(item);

            // Check if still valid
            if (Date.now() - entry.timestamp <= entry.ttl) {
              this.cache.set(key, entry);
            } else {
              localStorage.removeItem(key);
            }
          }
        } catch (error) {
          console.warn('Failed to load cache entry from localStorage:', error);
          localStorage.removeItem(key);
        }
      }
    }
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    this.stats.totalItems = this.cache.size;
    this.stats.hitRate = this.stats.hits / (this.stats.hits + this.stats.misses) || 0;

    // Calculate total size
    this.stats.totalSize = Array.from(this.cache.values()).reduce(
      (total, entry) => total + (entry.size || 0),
      0
    );

    // Update top keys
    this.stats.topKeys = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => b.accessCount - a.accessCount)
      .slice(0, 10)
      .map(([key, entry]) => ({ key, accessCount: entry.accessCount }));
  }

  /**
   * Update average access time
   */
  private updateAverageAccessTime(accessTime: number): void {
    const totalAccesses = this.stats.hits + this.stats.misses;
    this.stats.averageAccessTime =
      (this.stats.averageAccessTime * (totalAccesses - 1) + accessTime) / totalAccesses;
  }

  /**
   * Reset statistics
   */
  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalItems: 0,
      totalSize: 0,
      averageAccessTime: 0,
      topKeys: [],
    };
  }
}

/**
 * Cache service factory
 */
export class CacheService {
  private static instances = new Map<string, MemoryCache>();

  /**
   * Get or create cache instance
   */
  static getCache<T = any>(name: string, config?: Partial<PerformanceCacheConfig>): MemoryCache<T> {
    if (!this.instances.has(name)) {
      const env = getEnvironmentConfig();

      const defaultConfig: PerformanceCacheConfig = {
        keyPrefix: `amberglow_${env.environment}_${name}`,
        ttl: 5 * 60 * 1000, // 5 minutes
        maxSize: 100,
        persist: false,
        invalidationStrategy: 'time',
      };

      const finalConfig = { ...defaultConfig, ...config };
      this.instances.set(name, new MemoryCache<T>(finalConfig));
    }

    return this.instances.get(name) as MemoryCache<T>;
  }

  /**
   * Clear all caches
   */
  static clearAll(): void {
    this.instances.forEach(cache => cache.clear());
    this.instances.clear();
  }

  /**
   * Get statistics for all caches
   */
  static getAllStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {};

    this.instances.forEach((cache, name) => {
      stats[name] = cache.getStats();
    });

    return stats;
  }
}

/**
 * Predefined cache instances for common use cases
 */
export const caches = {
  // API response cache
  api: CacheService.getCache('api', {
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 200,
    persist: false,
  }),

  // User data cache
  user: CacheService.getCache('user', {
    ttl: 15 * 60 * 1000, // 15 minutes
    maxSize: 50,
    persist: true,
  }),

  // AI reflection cache
  ai: CacheService.getCache('ai', {
    ttl: 30 * 60 * 1000, // 30 minutes
    maxSize: 100,
    persist: true,
  }),

  // Application state cache
  state: CacheService.getCache('state', {
    ttl: 60 * 60 * 1000, // 1 hour
    maxSize: 50,
    persist: true,
  }),
};
