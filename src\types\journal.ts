/**
 * Journal Entry Types
 * Comprehensive TypeScript interfaces for journal entries and related data structures
 */

/**
 * Emotion types that users can select when creating journal entries
 */
export type EmotionType =
  | 'joyful'
  | 'calm'
  | 'neutral'
  | 'sad'
  | 'anxious'
  | 'excited'
  | 'grateful'
  | 'frustrated'
  | 'hopeful'
  | 'overwhelmed';

/**
 * Mood score range from 1-10
 */
export type MoodScore = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;

/**
 * AI-generated reflection data structure
 */
export interface AIReflection {
  /** Brief, empathetic summary of the main themes */
  summary: string;
  /** Primary emotion detected by AI */
  emotion: string;
  /** Warm, supportive message that validates feelings */
  encouragement: string;
  /** Gentle, open-ended question to help further reflection */
  reflection_question: string;
  /** Legacy field for backward compatibility */
  reflection?: string;
}

/**
 * Core journal entry interface matching database schema
 */
export interface JournalEntry {
  /** Unique identifier for the journal entry */
  id: string;
  /** User ID who created the entry */
  user_id: string;
  /** Entry title */
  title: string;
  /** Main content of the journal entry */
  content: string;
  /** User-selected emotion */
  emotion: EmotionType;
  /** User-rated mood score (1-10) */
  mood_score: MoodScore;
  /** AI-generated summary */
  ai_summary?: string;
  /** AI-detected emotion */
  ai_emotion?: string;
  /** AI-generated encouragement */
  ai_encouragement?: string;
  /** AI-generated reflection question */
  ai_reflection_question?: string;
  /** Legacy AI reflection field */
  ai_reflection?: string;
  /** Entry creation timestamp */
  created_at: string;
  /** Entry last update timestamp */
  updated_at: string;
}

/**
 * Journal entry creation payload (without auto-generated fields)
 */
export interface CreateJournalEntryPayload {
  title: string;
  content: string;
  emotion: EmotionType;
  mood_score: MoodScore;
  ai_summary?: string;
  ai_emotion?: string;
  ai_encouragement?: string;
  ai_reflection_question?: string;
  ai_reflection?: string;
}

/**
 * Journal entry update payload (partial fields)
 */
export interface UpdateJournalEntryPayload {
  title?: string;
  content?: string;
  emotion?: EmotionType;
  mood_score?: MoodScore;
  ai_summary?: string;
  ai_emotion?: string;
  ai_encouragement?: string;
  ai_reflection_question?: string;
  ai_reflection?: string;
}

/**
 * Formatted journal entry for display purposes
 * Note: emotion field is more flexible here to handle AI-generated emotions
 * and legacy data that might not match the strict EmotionType union
 */
export interface FormattedJournalEntry {
  id: string;
  date: string; // ISO date string
  title: string;
  content: string;
  emotion: string; // More flexible to handle AI emotions and legacy data
  mood_score: MoodScore;
  ai_reflection?: string;
  ai_summary?: string;
  ai_emotion?: string;
  ai_encouragement?: string;
  ai_reflection_question?: string;
  created_at: string;
}

/**
 * Journal entry statistics and analytics
 */
export interface JournalStats {
  total_entries: number;
  average_mood: number;
  most_common_emotion: EmotionType;
  entries_this_week: number;
  entries_this_month: number;
  mood_trend: 'improving' | 'declining' | 'stable';
}

/**
 * Journal entry filters for querying
 */
export interface JournalEntryFilters {
  emotion?: EmotionType;
  mood_range?: {
    min: MoodScore;
    max: MoodScore;
  };
  date_range?: {
    start: string;
    end: string;
  };
  search_term?: string;
  limit?: number;
  offset?: number;
}

/**
 * Pagination state for journal entries
 */
export interface JournalPaginationState {
  /** Array of all loaded journal entries */
  loadedEntries: FormattedJournalEntry[];
  /** Current offset for pagination (number of entries already loaded) */
  currentOffset: number;
  /** Whether more entries are available to load */
  hasMoreEntries: boolean;
  /** Whether a load more operation is currently in progress */
  isLoadingMore: boolean;
  /** Whether the initial load is in progress */
  isInitialLoading: boolean;
  /** Number of entries to load per page */
  pageSize: number;
  /** Total number of entries available (if known) */
  totalEntries?: number;
}

/**
 * Pagination options for journal entry queries
 */
export interface JournalPaginationOptions {
  /** Number of entries to load per page (default: 10) */
  pageSize?: number;
  /** Initial filters to apply */
  filters?: Omit<JournalEntryFilters, 'limit' | 'offset'>;
  /** Whether to enable automatic prefetching of next page */
  enablePrefetch?: boolean;
  /** Cache time for paginated results in milliseconds */
  cacheTime?: number;
  /** Stale time for paginated results in milliseconds */
  staleTime?: number;
}

/**
 * Result of a paginated journal entries query
 */
export interface JournalPaginationResult {
  /** The loaded entries for this page */
  entries: FormattedJournalEntry[];
  /** Whether there are more entries available */
  hasMore: boolean;
  /** Total number of entries (if available) */
  total?: number;
  /** The offset used for this query */
  offset: number;
  /** The limit used for this query */
  limit: number;
}

/**
 * Actions available for journal pagination
 */
export interface JournalPaginationActions {
  /** Load the next page of entries */
  loadMore: () => Promise<void>;
  /** Refresh all loaded entries */
  refresh: () => Promise<void>;
  /** Reset pagination to initial state */
  reset: () => void;
  /** Add a new entry to the beginning of the list */
  addEntry: (entry: FormattedJournalEntry) => void;
  /** Update an existing entry in the list */
  updateEntry: (id: string, updates: Partial<FormattedJournalEntry>) => void;
  /** Remove an entry from the list */
  removeEntry: (id: string) => void;
}
