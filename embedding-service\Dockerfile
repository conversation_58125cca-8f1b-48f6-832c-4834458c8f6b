# AmberGlow Embedding Service Docker Configuration
# This is optional - for advanced users who prefer Docker deployment

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app.py .
COPY start.py .

# Create non-root user for security
RUN useradd -m -u 1000 embeduser && chown -R embeduser:embeduser /app
USER embeduser

# Expose port
EXPOSE 5005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5005/health')"

# Start the service
CMD ["python", "app.py"]
