/**
 * Loading State Management System
 * Centralized loading state management with consistent patterns
 */

import { useState, useCallback, useRef } from 'react';

/**
 * Loading state interface
 */
export interface LoadingState {
  /** Whether operation is currently loading */
  isLoading: boolean;
  /** Optional loading message */
  message?: string;
  /** Loading progress (0-100) */
  progress?: number;
  /** Operation identifier for tracking multiple operations */
  operationId?: string;
}

/**
 * Loading manager configuration
 */
interface LoadingManagerConfig {
  /** Default loading message */
  defaultMessage?: string;
  /** Minimum loading duration to prevent flashing */
  minDuration?: number;
  /** Maximum loading duration before timeout */
  maxDuration?: number;
  /** Whether to show progress */
  showProgress?: boolean;
}

/**
 * Loading operation metadata
 */
interface LoadingOperation {
  /** Operation identifier */
  id: string;
  /** Start timestamp */
  startTime: number;
  /** Loading message */
  message?: string;
  /** Progress value */
  progress?: number;
  /** Timeout handle */
  timeoutHandle?: NodeJS.Timeout;
}

/**
 * Hook for managing loading states
 */
export const useLoadingState = (config: LoadingManagerConfig = {}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
  });

  const operationsRef = useRef<Map<string, LoadingOperation>>(new Map());
  const { minDuration = 300, maxDuration = 30000 } = config;

  /**
   * Starts a loading operation
   */
  const startLoading = useCallback(
    (operationId: string = 'default', message?: string, progress?: number) => {
      const operation: LoadingOperation = {
        id: operationId,
        startTime: Date.now(),
        message: message || config.defaultMessage,
        progress,
      };

      // Set timeout for maximum duration
      if (maxDuration > 0) {
        operation.timeoutHandle = setTimeout(() => {
          console.warn(
            `Loading operation ${operationId} exceeded maximum duration of ${maxDuration}ms`
          );
          stopLoading(operationId);
        }, maxDuration);
      }

      operationsRef.current.set(operationId, operation);
      updateLoadingState();
    },
    [config.defaultMessage, maxDuration]
  );

  /**
   * Updates progress for a loading operation
   */
  const updateProgress = useCallback(
    (operationId: string = 'default', progress: number, message?: string) => {
      const operation = operationsRef.current.get(operationId);
      if (operation) {
        operation.progress = Math.max(0, Math.min(100, progress));
        if (message) {
          operation.message = message;
        }
        updateLoadingState();
      }
    },
    []
  );

  /**
   * Stops a loading operation
   */
  const stopLoading = useCallback(
    (operationId: string = 'default') => {
      const operation = operationsRef.current.get(operationId);
      if (!operation) return;

      const elapsed = Date.now() - operation.startTime;

      // Clear timeout if exists
      if (operation.timeoutHandle) {
        clearTimeout(operation.timeoutHandle);
      }

      // Ensure minimum duration to prevent flashing
      if (elapsed < minDuration) {
        setTimeout(() => {
          operationsRef.current.delete(operationId);
          updateLoadingState();
        }, minDuration - elapsed);
      } else {
        operationsRef.current.delete(operationId);
        updateLoadingState();
      }
    },
    [minDuration]
  );

  /**
   * Updates the loading state based on active operations
   */
  const updateLoadingState = useCallback(() => {
    const operations = Array.from(operationsRef.current.values());

    if (operations.length === 0) {
      setLoadingState({ isLoading: false });
      return;
    }

    // Find the most recent operation or one with highest progress
    const primaryOperation = operations.reduce((prev, current) => {
      if (current.progress !== undefined && prev.progress !== undefined) {
        return current.progress > prev.progress ? current : prev;
      }
      return current.startTime > prev.startTime ? current : prev;
    });

    setLoadingState({
      isLoading: true,
      message: primaryOperation.message,
      progress: primaryOperation.progress,
      operationId: primaryOperation.id,
    });
  }, []);

  /**
   * Stops all loading operations
   */
  const stopAllLoading = useCallback(() => {
    // Clear all timeouts
    operationsRef.current.forEach(operation => {
      if (operation.timeoutHandle) {
        clearTimeout(operation.timeoutHandle);
      }
    });

    operationsRef.current.clear();
    setLoadingState({ isLoading: false });
  }, []);

  /**
   * Checks if a specific operation is loading
   */
  const isOperationLoading = useCallback((operationId: string) => {
    return operationsRef.current.has(operationId);
  }, []);

  /**
   * Gets all active operations
   */
  const getActiveOperations = useCallback(() => {
    return Array.from(operationsRef.current.keys());
  }, []);

  return {
    loadingState,
    startLoading,
    stopLoading,
    updateProgress,
    stopAllLoading,
    isOperationLoading,
    getActiveOperations,
  };
};

/**
 * Higher-order function to wrap async operations with loading state
 */
export const withLoading = <T extends any[], R>(
  asyncFn: (...args: T) => Promise<R>,
  loadingManager: ReturnType<typeof useLoadingState>,
  operationId?: string,
  message?: string
) => {
  return async (...args: T): Promise<R> => {
    const id = operationId || `operation_${Date.now()}`;

    try {
      loadingManager.startLoading(id, message);
      const result = await asyncFn(...args);
      return result;
    } finally {
      loadingManager.stopLoading(id);
    }
  };
};

/**
 * Creates a loading wrapper for service functions
 */
export const createLoadingWrapper = (loadingManager: ReturnType<typeof useLoadingState>) => {
  return <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    operationId?: string,
    message?: string
  ) => withLoading(fn, loadingManager, operationId, message);
};

/**
 * Utility for creating sequential loading operations with progress
 */
export const createProgressiveLoader = (
  loadingManager: ReturnType<typeof useLoadingState>,
  operationId: string = 'progressive'
) => {
  let currentStep = 0;
  let totalSteps = 0;

  return {
    /**
     * Initialize progressive loading
     */
    start: (steps: number, initialMessage?: string) => {
      totalSteps = steps;
      currentStep = 0;
      loadingManager.startLoading(operationId, initialMessage, 0);
    },

    /**
     * Advance to next step
     */
    nextStep: (message?: string) => {
      currentStep++;
      const progress = totalSteps > 0 ? (currentStep / totalSteps) * 100 : undefined;
      loadingManager.updateProgress(operationId, progress || 0, message);
    },

    /**
     * Set specific step
     */
    setStep: (step: number, message?: string) => {
      currentStep = Math.max(0, Math.min(totalSteps, step));
      const progress = totalSteps > 0 ? (currentStep / totalSteps) * 100 : undefined;
      loadingManager.updateProgress(operationId, progress || 0, message);
    },

    /**
     * Complete progressive loading
     */
    complete: () => {
      loadingManager.stopLoading(operationId);
    },

    /**
     * Get current progress
     */
    getProgress: () => ({
      currentStep,
      totalSteps,
      percentage: totalSteps > 0 ? (currentStep / totalSteps) * 100 : 0,
    }),
  };
};

/**
 * Debounced loading state to prevent rapid state changes
 */
export const useDebouncedLoading = (delay: number = 200) => {
  const [debouncedState, setDebouncedState] = useState<LoadingState>({
    isLoading: false,
  });

  const timeoutRef = useRef<NodeJS.Timeout>();

  const setLoadingState = useCallback(
    (state: LoadingState) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // If starting to load, show immediately
      if (state.isLoading && !debouncedState.isLoading) {
        setDebouncedState(state);
        return;
      }

      // If stopping loading, debounce to prevent flashing
      timeoutRef.current = setTimeout(() => {
        setDebouncedState(state);
      }, delay);
    },
    [debouncedState.isLoading, delay]
  );

  return {
    loadingState: debouncedState,
    setLoadingState,
  };
};

/**
 * Global loading state manager (singleton)
 */
class GlobalLoadingManager {
  private operations = new Map<string, LoadingOperation>();
  private listeners = new Set<(state: LoadingState) => void>();

  /**
   * Subscribe to loading state changes
   */
  subscribe(listener: (state: LoadingState) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Start global loading operation
   */
  startLoading(operationId: string, message?: string, progress?: number) {
    const operation: LoadingOperation = {
      id: operationId,
      startTime: Date.now(),
      message,
      progress,
    };

    this.operations.set(operationId, operation);
    this.notifyListeners();
  }

  /**
   * Stop global loading operation
   */
  stopLoading(operationId: string) {
    this.operations.delete(operationId);
    this.notifyListeners();
  }

  /**
   * Update progress for global operation
   */
  updateProgress(operationId: string, progress: number, message?: string) {
    const operation = this.operations.get(operationId);
    if (operation) {
      operation.progress = progress;
      if (message) operation.message = message;
      this.notifyListeners();
    }
  }

  /**
   * Get current global loading state
   */
  getState(): LoadingState {
    const operations = Array.from(this.operations.values());

    if (operations.length === 0) {
      return { isLoading: false };
    }

    const primaryOperation = operations[0]; // Use first operation for simplicity

    return {
      isLoading: true,
      message: primaryOperation.message,
      progress: primaryOperation.progress,
      operationId: primaryOperation.id,
    };
  }

  private notifyListeners() {
    const state = this.getState();
    this.listeners.forEach(listener => listener(state));
  }
}

export const globalLoadingManager = new GlobalLoadingManager();
