import { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AuthForm, useAuthForm } from '@/components/forms/AuthForm';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Auth = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    mode,
    data,
    setData,
    errors,
    validate,
    toggleMode,
    reset,
  } = useAuthForm('signin');

  useEffect(() => {
    if (user) {
      navigate('/journal'); // Redirect to journal after successful sign-in
    }
  }, [user, navigate]);

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/journal`, // Redirect to journal after Google sign-in
        },
      });
      if (error) throw error;
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in with Google');
    }
  };

  const handleEmailAuth = async () => {
    if (!validate()) {
      return;
    }

    try {
      if (mode === 'signup') {
        const { error } = await supabase.auth.signUp({
          email: data.email,
          password: data.password,
          options: {
            emailRedirectTo: `${window.location.origin}/journal`,
            data: {
              full_name: data.fullName || '',
            },
          },
        });
        if (error) throw error;
        toast.success('Check your email to confirm your account!');
      } else {
        const { error } = await supabase.auth.signInWithPassword({
          email: data.email,
          password: data.password,
        });
        if (error) throw error;
        // Navigation will be handled by useEffect when user state changes
      }
    } catch (error: any) {
      toast.error(error.message || 'Authentication failed');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-amber-50 via-orange-50 to-warm-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gradient">Amberglow</h1>
          <p className="mt-2 text-muted-foreground">
            {mode === 'signup'
              ? 'Create your mindful journaling space'
              : 'Welcome back to your mindful space'}
          </p>
        </div>

        <Card className="glass-effect">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">
              {mode === 'signup' ? 'Create account' : 'Sign in'}
            </CardTitle>
            <CardDescription className="text-center">
              {mode === 'signup'
                ? 'Enter your details to create your account'
                : 'Enter your email and password to sign in'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AuthForm
              mode={mode}
              data={data}
              onChange={setData}
              onSubmit={handleEmailAuth}
              onGoogleSignIn={handleGoogleSignIn}
              onModeToggle={toggleMode}
              errors={errors}
              testId="auth-form"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Auth;
