<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Semantic Memory System Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #FFF8E1 0%, #FFE0B2 100%);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 167, 38, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.9);
            margin: 20px 0;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 167, 38, 0.3);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #FFA726 0%, #FF7043 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .test-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .console-output {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Semantic Memory System Test</h1>
        <p>Test suite for semantic deduplication and memory recall functionality</p>
    </div>

    <div class="test-section">
        <h2>🔧 Embedding Service Tests</h2>
        <p>Test the core embedding service functionality and semantic similarity calculations.</p>
        <button class="test-button" onclick="testEmbeddingService()">Test Embedding Service</button>
        <button class="test-button" onclick="testSemanticSimilarity()">Test Semantic Similarity</button>
        <div id="embedding-status" class="status info" style="display: none;"></div>
        <div id="embedding-output" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🔄 Deduplication Tests</h2>
        <p>Test semantic deduplication threshold and duplicate detection.</p>
        <button class="test-button" onclick="testDeduplication()">Test Deduplication</button>
        <div id="dedup-status" class="status info" style="display: none;"></div>
        <div id="dedup-output" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🎯 Memory Recall Tests</h2>
        <p>Test semantic memory recall and relevance ranking.</p>
        <button class="test-button" onclick="testMemoryRecall()">Test Memory Recall</button>
        <div id="recall-status" class="status info" style="display: none;"></div>
        <div id="recall-output" class="console-output" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🚀 Full Test Suite</h2>
        <p>Run all tests in sequence to verify complete functionality.</p>
        <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        <button class="test-button" onclick="clearOutput()">Clear Output</button>
        <div id="full-status" class="status info" style="display: none;"></div>
        <div id="full-output" class="console-output" style="display: none;"></div>
    </div>

    <script>
        // Test configuration
        const TEST_CONFIG = {
            embeddingServiceUrl: '/api/embedding',
            testMemories: [
                { key: 'pet_cat', value: 'I have a cat named Whiskers who loves to play with yarn', category: 'fact' },
                { key: 'pet_feline', value: 'My feline companion Whiskers enjoys playing with string', category: 'fact' },
                { key: 'work_stress', value: 'Feeling overwhelmed with quarterly reports and deadlines', category: 'emotion' },
                { key: 'hobby_reading', value: 'I love reading science fiction novels in my spare time', category: 'preference' },
                { key: 'goal_fitness', value: 'Want to run a marathon next year and improve my endurance', category: 'goal' },
            ],
            testPrompts: [
                'Tell me about my pets',
                'How am I feeling about work lately?',
                'What do I like to do for fun?',
                'What are my fitness goals?',
            ]
        };

        // Utility functions
        function log(message, outputId = 'full-output') {
            const output = document.getElementById(outputId);
            if (output) {
                output.style.display = 'block';
                output.textContent += message + '\n';
                output.scrollTop = output.scrollHeight;
            }
            console.log(message);
        }

        function setStatus(message, type = 'info', statusId = 'full-status') {
            const status = document.getElementById(statusId);
            if (status) {
                status.style.display = 'block';
                status.className = `status ${type}`;
                status.textContent = message;
            }
        }

        function clearOutput() {
            const outputs = document.querySelectorAll('.console-output');
            outputs.forEach(output => {
                output.textContent = '';
                output.style.display = 'none';
            });
            const statuses = document.querySelectorAll('.status');
            statuses.forEach(status => {
                status.style.display = 'none';
            });
        }

        function cosineSimilarity(a, b) {
            let dotProduct = 0;
            let normA = 0;
            let normB = 0;
            
            for (let i = 0; i < a.length; i++) {
                dotProduct += a[i] * b[i];
                normA += a[i] * a[i];
                normB += b[i] * b[i];
            }
            
            return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
        }

        // Test functions
        async function testEmbeddingService() {
            const outputId = 'embedding-output';
            const statusId = 'embedding-status';
            
            log('🧪 Testing embedding service...', outputId);
            setStatus('Testing embedding service...', 'info', statusId);
            
            try {
                // Test health check
                const healthResponse = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/health`);
                if (!healthResponse.ok) {
                    throw new Error(`Health check failed: ${healthResponse.status}`);
                }
                
                const healthData = await healthResponse.json();
                log(`✅ Embedding service health: ${JSON.stringify(healthData)}`, outputId);
                
                // Test embedding generation
                const testText = 'This is a test sentence for embedding generation';
                const embeddingResponse = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: testText })
                });
                
                if (!embeddingResponse.ok) {
                    throw new Error(`Embedding generation failed: ${embeddingResponse.status}`);
                }
                
                const embeddingData = await embeddingResponse.json();
                log(`✅ Embedding generation successful: ${embeddingData.embedding?.length} dimensions`, outputId);
                log(`   Sample values: [${embeddingData.embedding?.slice(0, 5).map(v => v.toFixed(3)).join(', ')}...]`, outputId);
                
                setStatus('Embedding service test passed!', 'success', statusId);
                return true;
            } catch (error) {
                log(`❌ Embedding service test failed: ${error.message}`, outputId);
                setStatus(`Embedding service test failed: ${error.message}`, 'error', statusId);
                return false;
            }
        }

        async function testSemanticSimilarity() {
            const outputId = 'embedding-output';
            const statusId = 'embedding-status';
            
            log('\n🧪 Testing semantic similarity...', outputId);
            
            try {
                const text1 = 'I have a cat named Whiskers';
                const text2 = 'My feline companion is called Whiskers';
                const text3 = 'I work as a software engineer';
                
                // Generate embeddings
                const responses = await Promise.all([
                    fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text1 })
                    }),
                    fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text2 })
                    }),
                    fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text3 })
                    })
                ]);
                
                const embeddings = await Promise.all(responses.map(r => r.json()));
                
                const similarity12 = cosineSimilarity(embeddings[0].embedding, embeddings[1].embedding);
                const similarity13 = cosineSimilarity(embeddings[0].embedding, embeddings[2].embedding);
                
                log('✅ Semantic similarity results:', outputId);
                log(`   Similar texts: ${similarity12.toFixed(3)} (should be high)`, outputId);
                log(`   Different texts: ${similarity13.toFixed(3)} (should be low)`, outputId);
                
                if (similarity12 > 0.7 && similarity13 < 0.5) {
                    log('✅ Semantic similarity working correctly!', outputId);
                    setStatus('Semantic similarity test passed!', 'success', statusId);
                    return true;
                } else {
                    log('⚠️  Semantic similarity results unexpected', outputId);
                    setStatus('Semantic similarity results unexpected', 'warning', statusId);
                    return false;
                }
                
            } catch (error) {
                log(`❌ Semantic similarity test failed: ${error.message}`, outputId);
                setStatus(`Semantic similarity test failed: ${error.message}`, 'error', statusId);
                return false;
            }
        }

        async function testDeduplication() {
            const outputId = 'dedup-output';
            const statusId = 'dedup-status';
            
            log('🧪 Testing deduplication threshold...', outputId);
            setStatus('Testing deduplication...', 'info', statusId);
            
            try {
                const memory1 = 'I have a cat named Whiskers who loves to play with yarn';
                const memory2 = 'My feline companion Whiskers enjoys playing with string';
                
                const responses = await Promise.all([
                    fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: memory1 })
                    }),
                    fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: memory2 })
                    })
                ]);
                
                const embeddings = await Promise.all(responses.map(r => r.json()));
                const similarity = cosineSimilarity(embeddings[0].embedding, embeddings[1].embedding);
                const threshold = 0.92;
                
                log(`✅ Deduplication test:`, outputId);
                log(`   Memory 1: "${memory1}"`, outputId);
                log(`   Memory 2: "${memory2}"`, outputId);
                log(`   Similarity: ${similarity.toFixed(3)}`, outputId);
                log(`   Threshold: ${threshold}`, outputId);
                log(`   Would be deduplicated: ${similarity >= threshold ? 'YES' : 'NO'}`, outputId);
                
                setStatus('Deduplication test completed!', 'success', statusId);
                return true;
                
            } catch (error) {
                log(`❌ Deduplication test failed: ${error.message}`, outputId);
                setStatus(`Deduplication test failed: ${error.message}`, 'error', statusId);
                return false;
            }
        }

        async function testMemoryRecall() {
            const outputId = 'recall-output';
            const statusId = 'recall-status';
            
            log('🧪 Testing memory recall relevance...', outputId);
            setStatus('Testing memory recall...', 'info', statusId);
            
            try {
                const memories = TEST_CONFIG.testMemories;
                const prompts = TEST_CONFIG.testPrompts;
                
                // Generate embeddings for all memories
                const memoryEmbeddings = await Promise.all(
                    memories.map(async (memory) => {
                        const response = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ text: `${memory.key}: ${memory.value}` })
                        });
                        const data = await response.json();
                        return { ...memory, embedding: data.embedding };
                    })
                );
                
                // Test each prompt
                for (const prompt of prompts) {
                    log(`\n🔍 Testing prompt: "${prompt}"`, outputId);
                    
                    // Generate prompt embedding
                    const promptResponse = await fetch(`${TEST_CONFIG.embeddingServiceUrl}/embed`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: prompt })
                    });
                    const promptData = await promptResponse.json();
                    
                    // Calculate similarities
                    const similarities = memoryEmbeddings.map(memory => {
                        const similarity = cosineSimilarity(promptData.embedding, memory.embedding);
                        return { ...memory, similarity };
                    });
                    
                    // Sort by similarity and show top results
                    const sortedMemories = similarities
                        .sort((a, b) => b.similarity - a.similarity)
                        .slice(0, 3);
                    
                    log('   Top relevant memories:', outputId);
                    sortedMemories.forEach((memory, index) => {
                        const relevanceIcon = memory.similarity >= 0.6 ? '🔥' : 
                                             memory.similarity >= 0.4 ? '⭐' : 
                                             memory.similarity >= 0.2 ? '💡' : '📝';
                        log(`   ${index + 1}. ${relevanceIcon} ${memory.key}: ${memory.similarity.toFixed(3)}`, outputId);
                    });
                }
                
                log('\n✅ Memory recall test completed!', outputId);
                setStatus('Memory recall test passed!', 'success', statusId);
                return true;
                
            } catch (error) {
                log(`❌ Memory recall test failed: ${error.message}`, outputId);
                setStatus(`Memory recall test failed: ${error.message}`, 'error', statusId);
                return false;
            }
        }

        async function runAllTests() {
            const outputId = 'full-output';
            const statusId = 'full-status';
            
            clearOutput();
            log('🚀 Starting semantic memory system tests...', outputId);
            setStatus('Running all tests...', 'info', statusId);
            
            const results = {
                embeddingService: await testEmbeddingService(),
                semanticSimilarity: await testSemanticSimilarity(),
                deduplication: await testDeduplication(),
                memoryRecall: await testMemoryRecall(),
            };
            
            log('\n📊 Test Results Summary:', outputId);
            log('========================', outputId);
            Object.entries(results).forEach(([test, passed]) => {
                log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`, outputId);
            });
            
            const allPassed = Object.values(results).every(result => result);
            log(`\n${allPassed ? '🎉' : '⚠️'} Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`, outputId);
            
            if (allPassed) {
                log('\n🎯 Semantic memory system is ready for use!', outputId);
                log('   • Semantic deduplication will prevent duplicate memories', outputId);
                log('   • Memory recall will provide contextually relevant memories to Amber', outputId);
                log('   • Embedding service is functioning correctly', outputId);
                setStatus('All tests passed! Semantic memory system is ready.', 'success', statusId);
            } else {
                log('\n🔧 Please check the failed tests and ensure:', outputId);
                log('   • Embedding service is running on localhost:5005', outputId);
                log('   • Vite proxy is configured correctly', outputId);
                log('   • Database migration has been applied', outputId);
                setStatus('Some tests failed. Check the output for details.', 'error', statusId);
            }
        }

        // Auto-run basic test on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧠 Semantic Memory Test Page Loaded');
        });
    </script>
</body>
</html>
