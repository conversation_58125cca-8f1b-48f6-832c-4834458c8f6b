/**
 * Mood Analytics Dashboard Page
 * Comprehensive mood and emotion analytics with interactive visualizations
 */

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import PageLayout from '@/components/layout/PageLayout';
import { MoodTrendChart } from '@/components/features/analytics/MoodTrendChart';
import { EmotionDistributionChart } from '@/components/features/analytics/EmotionDistributionChart';
import { MoodCalendarHeatmap } from '@/components/features/analytics/MoodCalendarHeatmap';
import { AnalyticsStatsCards } from '@/components/features/analytics/AnalyticsStatsCards';
import { useMoodAnalytics, useMoodInsights } from '@/hooks/useMoodAnalytics';
import { useAuth } from '@/contexts/AuthContext';
import { AnalyticsTimeRange } from '@/types';
import {
  BarChart3,
  TrendingUp,
  Calendar,
  PieChart,
  Download,
  RefreshCw,
  Lightbulb
} from 'lucide-react';
import { toast } from '@/components/ui/sonner';

/**
 * Time range options for the selector
 */
const TIME_RANGE_OPTIONS = [
  { value: '7d' as AnalyticsTimeRange, label: 'Last 7 days' },
  { value: '30d' as AnalyticsTimeRange, label: 'Last 30 days' },
  { value: '90d' as AnalyticsTimeRange, label: 'Last 3 months' },
  { value: '1y' as AnalyticsTimeRange, label: 'Last year' },
];

const MoodAnalytics = () => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState<AnalyticsTimeRange>('30d');
  const [activeTab, setActiveTab] = useState('overview');

  console.log('📊 MoodAnalytics page render:', {
    user: user?.id,
    authLoading,
  });

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      console.log('🚫 MoodAnalytics: No user, redirecting to auth');
      navigate('/auth');
    }
  }, [user, authLoading, navigate]);

  // Fetch analytics data (hooks must be called before any early returns)
  const {
    data: analytics,
    isLoading,
    error,
    refetch,
    isFetching
  } = useMoodAnalytics(timeRange);

  // Fetch insights
  const { insights } = useMoodInsights(timeRange);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Return null if no user (will redirect via useEffect)
  if (!user) {
    return null;
  }

  /**
   * Handle time range change
   */
  const handleTimeRangeChange = (newTimeRange: AnalyticsTimeRange) => {
    setTimeRange(newTimeRange);
    toast.success(`Updated to show ${TIME_RANGE_OPTIONS.find(opt => opt.value === newTimeRange)?.label.toLowerCase()}`);
  };

  /**
   * Handle data refresh
   */
  const handleRefresh = () => {
    refetch();
    toast.success('Analytics data refreshed');
  };

  /**
   * Handle data export (placeholder)
   */
  const handleExport = () => {
    toast.info('Export feature coming soon!');
  };

  /**
   * Error state
   */
  if (error) {
    return (
      <PageLayout>
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              We couldn't load your mood analytics. Please try again.
            </p>
            <Button onClick={handleRefresh} className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gradient-warm mb-2">Mood Analytics</h1>
              <p className="text-gray-600">
                Insights into your emotional journey and mood patterns
              </p>
            </div>
            
            {/* Controls */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Select value={timeRange} onValueChange={handleTimeRangeChange}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIME_RANGE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex gap-2 w-full sm:w-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isFetching}
                  className="flex-1 sm:flex-none"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExport}
                  className="flex-1 sm:flex-none"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Insights Banner */}
        {insights && insights.length > 0 && (
          <Card className="mb-6 bg-gradient-to-r from-amber-100 to-orange-100 border-amber-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-amber-800">
                <Lightbulb className="w-5 h-5" />
                Insights & Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {insights.slice(0, 3).map((insight, index) => (
                  <p key={index} className="text-amber-700 text-sm">
                    {insight}
                  </p>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Analytics Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-1 sm:gap-2">
              <BarChart3 className="w-4 h-4" />
              <span className="hidden sm:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger value="trends" className="flex items-center gap-1 sm:gap-2">
              <TrendingUp className="w-4 h-4" />
              <span className="hidden sm:inline">Trends</span>
            </TabsTrigger>
            <TabsTrigger value="emotions" className="flex items-center gap-1 sm:gap-2">
              <PieChart className="w-4 h-4" />
              <span className="hidden sm:inline">Emotions</span>
            </TabsTrigger>
            <TabsTrigger value="calendar" className="flex items-center gap-1 sm:gap-2">
              <Calendar className="w-4 h-4" />
              <span className="hidden sm:inline">Calendar</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <AnalyticsStatsCards analytics={analytics} isLoading={isLoading} />
            
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <MoodTrendChart
                data={analytics?.moodTrend}
                timeRange={timeRange}
                isLoading={isLoading}
              />
              <EmotionDistributionChart
                data={analytics?.emotionDistribution}
                isLoading={isLoading}
              />
            </div>
          </TabsContent>

          {/* Trends Tab */}
          <TabsContent value="trends" className="space-y-6">
            <MoodTrendChart 
              data={analytics?.moodTrend} 
              timeRange={timeRange} 
              detailed 
              isLoading={isLoading}
            />
            
            {analytics?.patterns && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Weekly Pattern</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analytics.patterns.weeklyPattern.map((day) => (
                        <div key={day.dayOfWeek} className="flex justify-between items-center">
                          <span className="font-medium">{day.dayName}</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">
                              {day.averageMood.toFixed(1)}/10
                            </span>
                            <div 
                              className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden"
                            >
                              <div 
                                className="h-full bg-amber-500 rounded-full"
                                style={{ width: `${(day.averageMood / 10) * 100}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Best & Worst Days</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-green-600">🌟</span>
                          <span className="font-medium text-green-800">Best Day</span>
                        </div>
                        <p className="text-sm text-green-700">
                          {analytics.patterns.bestDay.dayName}s - Average mood: {analytics.patterns.bestDay.averageMood.toFixed(1)}/10
                        </p>
                      </div>
                      
                      <div className="p-3 bg-orange-50 rounded-lg">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-orange-600">💙</span>
                          <span className="font-medium text-orange-800">Challenging Day</span>
                        </div>
                        <p className="text-sm text-orange-700">
                          {analytics.patterns.worstDay.dayName}s - Average mood: {analytics.patterns.worstDay.averageMood.toFixed(1)}/10
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          {/* Emotions Tab */}
          <TabsContent value="emotions" className="space-y-6">
            <EmotionDistributionChart 
              data={analytics?.emotionDistribution} 
              detailed 
              isLoading={isLoading}
            />
          </TabsContent>

          {/* Calendar Tab */}
          <TabsContent value="calendar" className="space-y-6">
            <MoodCalendarHeatmap 
              data={analytics?.dailyMoods} 
              isLoading={isLoading}
            />
          </TabsContent>
        </Tabs>
    </PageLayout>
  );
};

export default MoodAnalytics;
