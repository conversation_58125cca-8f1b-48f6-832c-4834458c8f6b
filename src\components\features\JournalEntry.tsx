import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { JournalEntryForm, useJournalEntryForm } from '@/components/forms/JournalEntryForm';
import { AmberButton } from '@/components/ui/amber-button';
import { AIReflection } from './AIReflection';
import { Sparkles } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { generateAIReflection } from '@/services/aiReflectionService';
import { extractMemoriesFromJournalEntry } from '@/services/memoryExtractionService';
import { utcTimestampToLocalDateString, getCurrentLocalDateForDisplay } from '@/utils/dateUtils';
import {
  FormattedJournalEntry,
  AIReflection as AIReflectionType,
} from '@/types';

interface JournalEntryProps {
  onSave: (entry: FormattedJournalEntry) => void;
  onCancel: () => void;
}

export const JournalEntry = ({ onSave, onCancel }: JournalEntryProps) => {
  const { user } = useAuth();
  const {
    data: formData,
    setData: setFormData,
    errors,
    validate,
    reset,
  } = useJournalEntryForm();

  const [isLoading, setIsLoading] = useState(false);
  const [aiReflection, setAiReflection] = useState<AIReflectionType>({
    summary: '',
    emotion: '',
    encouragement: '',
    reflection_question: '',
    reflection: '',
  });
  const [isGeneratingReflection, setIsGeneratingReflection] = useState(false);
  const [showReflection, setShowReflection] = useState(false);

  const handleGenerateReflection = async () => {
    if (!formData.title.trim() || !formData.content.trim() || !formData.emotion) {
      toast.error('Please fill in all fields before generating a reflection');
      return;
    }

    setIsGeneratingReflection(true);
    setShowReflection(true);

    try {
      const reflectionResult = await generateAIReflection({
        title: formData.title.trim(),
        content: formData.content.trim(),
        emotion: formData.emotion,
        moodScore: formData.moodScore,
      });

      if (reflectionResult.success && reflectionResult.data) {
        setAiReflection({
          summary: reflectionResult.data.summary,
          emotion: reflectionResult.data.emotion,
          encouragement: reflectionResult.data.encouragement,
          reflection_question: reflectionResult.data.reflection_question,
          reflection: reflectionResult.data.reflection,
        });
      } else {
        toast.error('Failed to generate reflection. Please try again.');
        setShowReflection(false);
      }
    } catch (error) {
      console.error('Error generating reflection:', error);
      toast.error('Failed to generate reflection. Please try again.');
      setShowReflection(false);
    } finally {
      setIsGeneratingReflection(false);
    }
  };

  const handleSave = async () => {
    if (!validate()) {
      return;
    }

    if (!user) {
      toast.error('You must be logged in to save entries');
      return;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase
        .from('journal_entries')
        .insert({
          user_id: user.id,
          title: formData.title.trim(),
          content: formData.content.trim(),
          emotion: formData.emotion,
          mood_score: formData.moodScore,
          ai_reflection: aiReflection.reflection || null,
          ai_summary: aiReflection.summary || null,
          ai_emotion: aiReflection.emotion || null,
          ai_encouragement: aiReflection.encouragement || null,
          ai_reflection_question: aiReflection.reflection_question || null,
        })
        .select()
        .single();

      if (error) throw error;

      const entry = {
        id: data.id,
        date: utcTimestampToLocalDateString(data.created_at),
        title: data.title,
        content: data.content,
        emotion: data.emotion,
        mood_score: data.mood_score,
        ai_reflection: data.ai_reflection,
        ai_summary: data.ai_summary,
        ai_emotion: data.ai_emotion,
        ai_encouragement: data.ai_encouragement,
        ai_reflection_question: data.ai_reflection_question,
        created_at: data.created_at,
      };

      onSave(entry);
      toast.success('Journal entry saved!');

      // Automatically extract memories in the background (non-blocking)
      setTimeout(async () => {
        try {
          console.log('🧠 [DEBUG] Starting automatic memory extraction from new journal entry...');
          console.log('🧠 [DEBUG] Journal entry data:', {
            title: formData.title,
            content: formData.content,
            emotion: formData.emotion,
            moodScore: formData.moodScore,
            contentLength: formData.content.length,
          });

          const memoryResult = await extractMemoriesFromJournalEntry({
            title: formData.title,
            content: formData.content,
            emotion: formData.emotion,
            moodScore: formData.moodScore,
          });

          console.log('🧠 [DEBUG] Memory extraction result:', memoryResult);

          if (memoryResult.success && memoryResult.memories.length > 0) {
            console.log(`🧠 [DEBUG] Successfully extracted ${memoryResult.memories.length} memories from journal entry:`, memoryResult.memories);
            toast.success(`💾 Discovered ${memoryResult.memories.length} new memories from your entry`, {
              duration: 3000,
            });
          } else if (memoryResult.success && memoryResult.memories.length === 0) {
            console.log('🧠 [DEBUG] No memories extracted from journal entry (this is normal for some entries)');
          } else {
            console.warn('🧠 [DEBUG] Memory extraction failed:', memoryResult.error);
          }
        } catch (error) {
          // Memory extraction failure should not affect the user experience
          console.error('🧠 [DEBUG] Automatic memory extraction failed (non-critical):', error);
        }
      }, 100); // Small delay to ensure UI updates first
    } catch (error: any) {
      console.error('Error saving entry:', error);
      console.error('Entry data being saved:', {
        user_id: user?.id,
        title: formData.title.trim(),
        content: formData.content.trim(),
        emotion: formData.emotion,
        mood_score: formData.moodScore,
        ai_reflection: aiReflection.reflection || null,
        ai_summary: aiReflection.summary || null,
        ai_emotion: aiReflection.emotion || null,
        ai_encouragement: aiReflection.encouragement || null,
        ai_reflection_question: aiReflection.reflection_question || null,
      });

      // Provide more specific error messages
      if (error.message?.includes('permission')) {
        toast.error('Permission denied. Please check your account access.');
      } else if (error.message?.includes('network')) {
        toast.error('Network error. Please check your connection and try again.');
      } else if (error.message?.includes('validation')) {
        toast.error('Invalid data format. Please check your entry and try again.');
      } else {
        toast.error(`Failed to save entry: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // AI Reflection section to add to the form
  const aiReflectionSection = (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">Get AI Reflection</Label>
        <AmberButton
          variant="primary"
          onClick={handleGenerateReflection}
          disabled={
            isGeneratingReflection || !formData.title.trim() || !formData.content.trim() || !formData.emotion
          }
          icon={<Sparkles className="w-4 h-4" />}
          isLoading={isGeneratingReflection}
          loadingText="Generating..."
        >
          Generate Reflection
        </AmberButton>
      </div>

      {showReflection && (
        <AIReflection
          summary={aiReflection.summary}
          emotion={aiReflection.emotion}
          encouragement={aiReflection.encouragement}
          reflection_question={aiReflection.reflection_question}
          reflection={aiReflection.reflection}
          isGenerating={isGeneratingReflection}
        />
      )}
    </div>
  );

  return (
    <div className="min-h-screen section-padding fade-in">
      <div className="container mx-auto max-w-5xl">
        <Card className="card-elevated hover-glow">
          <CardHeader className="section-padding pb-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-3xl text-gradient-warm heading-modern">New Journal Entry</CardTitle>
            </div>
            <p className="text-muted-foreground text-modern mt-2">
              {getCurrentLocalDateForDisplay()}
            </p>
          </CardHeader>

          <CardContent className="section-padding pt-0">
            <JournalEntryForm
              data={formData}
              onChange={setFormData}
              onSubmit={handleSave}
              onCancel={onCancel}
              isLoading={isLoading}
              errors={errors}
              submitText="Save Entry"
              cancelText="Cancel"
              additionalContent={aiReflectionSection}
              testId="journal-entry-form"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
