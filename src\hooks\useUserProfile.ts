/**
 * User Profile Hook
 * Custom hook for managing user profile data from the profiles table
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserProfile } from '@/services/user.service';
import { UserProfile } from '@/types';

/**
 * Hook return type
 */
interface UseUserProfileReturn {
  /** User profile data from profiles table */
  profile: UserProfile | null;
  /** Loading state */
  loading: boolean;
  /** Error state */
  error: string | null;
  /** Refresh profile data */
  refreshProfile: () => Promise<void>;
}

/**
 * Custom hook for user profile management
 * Fetches and manages user profile data from the profiles table
 */
export const useUserProfile = (): UseUserProfileReturn => {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Load user profile data
   */
  const loadProfile = useCallback(async () => {
    if (!user?.id) {
      setProfile(null);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await getUserProfile(user.id);

      if (response.success && response.data) {
        setProfile(response.data);
      } else {
        setError(response.error?.message || 'Failed to load user profile.');
        setProfile(null);
      }
    } catch (err) {
      setError('An unexpected error occurred while loading profile.');
      setProfile(null);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  /**
   * Refresh profile data (public method)
   */
  const refreshProfile = useCallback(async () => {
    await loadProfile();
  }, [loadProfile]);

  /**
   * Load profile when user changes
   */
  useEffect(() => {
    loadProfile();
  }, [loadProfile]);

  return {
    profile,
    loading,
    error,
    refreshProfile,
  };
};
