/**
 * Navigation Hook
 * Custom hook for managing application navigation using React Router
 */

import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { NavigationView } from '@/types';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook return type
 */
interface UseNavigationReturn {
  /** Current active view based on URL */
  currentView: NavigationView;
  /** Navigate to a specific view */
  navigateToView: (view: NavigationView) => void;
  /** Navigate to write view (with auth check) */
  navigateToWrite: () => void;
  /** Navigate to entries view (with auth check) */
  navigateToEntries: () => void;
  /** Navigate to home view */
  navigateToHome: () => void;
  /** Handle get started action */
  handleGetStarted: () => void;
}

/**
 * Get current view from URL pathname
 */
const getViewFromPath = (pathname: string): NavigationView => {
  switch (pathname) {
    case '/journal':
      return 'entries';
    case '/write':
      return 'write';
    case '/settings':
      return 'settings';
    case '/analytics':
      return 'analytics';
    case '/auth':
      return 'home'; // Auth page doesn't have a nav view
    default:
      return 'home';
  }
};

/**
 * Custom hook for navigation management using React Router
 */
export const useNavigation = (): UseNavigationReturn => {
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get current view from URL
  const currentView = getViewFromPath(location.pathname);

  /**
   * Navigate to a specific view with authentication checks
   */
  const navigateToView = (view: NavigationView): void => {
    console.log('🧭 Navigation: navigateToView called with:', view, 'user:', user?.id);

    // Check if user is trying to access protected views without authentication
    if (
      !user &&
      (view === 'write' || view === 'entries' || view === 'profile' || view === 'settings' || view === 'analytics')
    ) {
      console.log('🚫 Navigation: No user, redirecting to auth');
      navigate('/auth');
      return;
    }

    // Map view to route
    const routeMap: Record<NavigationView, string> = {
      home: '/',
      write: '/write',
      entries: '/journal',
      profile: '/profile', // Future route
      settings: '/settings',
      analytics: '/analytics',
    };

    const route = routeMap[view];
    console.log('✅ Navigation: Navigating to route:', route);
    navigate(route);
  };

  /**
   * Navigate to write view with auth check
   */
  const navigateToWrite = (): void => {
    navigateToView('write');
  };

  /**
   * Navigate to entries view with auth check
   */
  const navigateToEntries = (): void => {
    navigateToView('entries');
  };

  /**
   * Navigate to home view
   */
  const navigateToHome = (): void => {
    navigate('/');
  };

  /**
   * Handle get started action from hero section
   */
  const handleGetStarted = (): void => {
    if (user) {
      navigate('/write');
    } else {
      navigate('/auth');
    }
  };

  /**
   * Redirect to auth if trying to access protected views without authentication
   */
  useEffect(() => {
    if (!authLoading && !user && (currentView === 'write' || currentView === 'entries' || currentView === 'settings' || currentView === 'analytics')) {
      console.log('🚫 Navigation: No user, redirecting to auth from protected view:', currentView);
      navigate('/auth');
    }
  }, [user, authLoading, currentView, navigate]);

  return {
    currentView,
    navigateToView,
    navigateToWrite,
    navigateToEntries,
    navigateToHome,
    handleGetStarted,
  };
};
