/**
 * Memory Test Page
 * Page for testing and demonstrating memory extraction functionality
 */

import React, { useState } from 'react';
import PageLayout from '@/components/layout/PageLayout';
import { MemoryExtraction } from '@/components/features/MemoryExtraction';
import { MemoryExtractionButton } from '@/components/features/MemoryExtractionButton';
import { MemoryAwareConversation } from '@/components/features/MemoryAwareConversation';
import { MemoryExtractionTest } from '@/components/debug/MemoryExtractionTest';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Brain, MessageCircle, TestTube, Lightbulb, Bug } from 'lucide-react';

/**
 * Memory test page component
 */
export const MemoryTest: React.FC = () => {
  // Sample journal entry for testing
  const [sampleJournalEntry] = useState({
    title: "A Great Day with <PERSON>",
    content: "Had an amazing day today! Took <PERSON> to the dog park and he made so many new friends. I've been feeling much better about my anxiety lately, especially since starting therapy with <PERSON><PERSON>. My goal to run a 5K is getting closer - I managed to run for 20 minutes straight today! Also excited about my sister's wedding next month in Hawaii. Work has been stressful with the quarterly presentation coming up, but I'm trying to stay positive.",
    emotion: "happy",
    moodScore: 8,
  });

  return (
    <PageLayout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Memory Extraction System
          </h1>
          <p className="text-gray-600">
            Test the AI memory extraction system that identifies important long-term facts about users
            from journal entries and conversations.
          </p>
        </div>

        <Tabs defaultValue="debug" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="debug" className="flex items-center gap-2">
              <Bug className="h-4 w-4" />
              Debug Test
            </TabsTrigger>
            <TabsTrigger value="extraction" className="flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              Extraction Test
            </TabsTrigger>
            <TabsTrigger value="integration" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Integration Demo
            </TabsTrigger>
            <TabsTrigger value="conversation" className="flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              Memory-Aware Chat
            </TabsTrigger>
            <TabsTrigger value="examples" className="flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Examples
            </TabsTrigger>
          </TabsList>

          <TabsContent value="debug">
            <MemoryExtractionTest />
          </TabsContent>

          <TabsContent value="extraction">
            <MemoryExtraction />
          </TabsContent>

          <TabsContent value="integration" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Memory Extraction Button Integration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-600">
                  This shows how the memory extraction button can be integrated into journal entries:
                </p>

                {/* Sample Journal Entry */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-lg mb-2">{sampleJournalEntry.title}</h3>
                  <p className="text-gray-700 mb-3">{sampleJournalEntry.content}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>Emotion: {sampleJournalEntry.emotion}</span>
                    <span>Mood: {sampleJournalEntry.moodScore}/10</span>
                  </div>
                </div>

                {/* Memory Extraction Button */}
                <MemoryExtractionButton
                  journalEntry={sampleJournalEntry}
                  showExtractedMemories={true}
                  onMemoriesExtracted={(memories) => {
                    console.log('Extracted memories:', memories);
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="conversation">
            <MemoryAwareConversation
              journalEntry={sampleJournalEntry}
              showMemoryContext={true}
            />
          </TabsContent>

          <TabsContent value="examples" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Journal Entry Example</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="bg-blue-50 p-3 rounded">
                      <p className="text-sm font-medium">Input:</p>
                      <p className="text-sm">"I've been feeling overwhelmed at work lately. Enzo hasn't been eating much either — I hope he's okay. I really want to get back into Mandarin practice this week."</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded">
                      <p className="text-sm font-medium">Extracted Memories:</p>
                      <ul className="text-sm space-y-1">
                        <li>• <strong>work_stress:</strong> feeling overwhelmed at work lately (emotion)</li>
                        <li>• <strong>pet:</strong> Enzo the cat (fact)</li>
                        <li>• <strong>language_goal:</strong> wants to resume Mandarin practice (goal)</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Conversation Example</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="bg-blue-50 p-3 rounded">
                      <p className="text-sm font-medium">Input:</p>
                      <div className="text-sm space-y-1">
                        <p>User: "I've been super stressed lately."</p>
                        <p>Amber: "I'm here for you. Do you know why you've been feeling that way?"</p>
                        <p>User: "Probably because of the upcoming presentation and the pressure to impress the execs."</p>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded">
                      <p className="text-sm font-medium">Extracted Memories:</p>
                      <ul className="text-sm space-y-1">
                        <li>• <strong>work_stress:</strong> stressed about upcoming executive presentation (emotion)</li>
                        <li>• <strong>work_event:</strong> important presentation to executives coming up (event)</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Memory Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div className="space-y-2">
                    <h4 className="font-medium text-blue-700">Facts</h4>
                    <p className="text-sm text-gray-600">Objective information about the user</p>
                    <ul className="text-xs space-y-1">
                      <li>• Pets, family, job</li>
                      <li>• Location, possessions</li>
                      <li>• Personal details</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-red-700">Emotions</h4>
                    <p className="text-sm text-gray-600">Current emotional states or patterns</p>
                    <ul className="text-xs space-y-1">
                      <li>• Stress, anxiety</li>
                      <li>• Excitement, depression</li>
                      <li>• Emotional patterns</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-green-700">Events</h4>
                    <p className="text-sm text-gray-600">Significant upcoming or recent events</p>
                    <ul className="text-xs space-y-1">
                      <li>• Presentations, trips</li>
                      <li>• Milestones, changes</li>
                      <li>• Important dates</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-purple-700">Goals</h4>
                    <p className="text-sm text-gray-600">Things the user wants to achieve</p>
                    <ul className="text-xs space-y-1">
                      <li>• Learning languages</li>
                      <li>• Fitness, career</li>
                      <li>• Personal growth</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-yellow-700">Preferences</h4>
                    <p className="text-sm text-gray-600">Likes, dislikes, habits</p>
                    <ul className="text-xs space-y-1">
                      <li>• Hobbies, food</li>
                      <li>• Activities, choices</li>
                      <li>• Personal style</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium text-indigo-700">Identity</h4>
                    <p className="text-sm text-gray-600">Core aspects of who they are</p>
                    <ul className="text-xs space-y-1">
                      <li>• Profession, relationships</li>
                      <li>• Values, beliefs</li>
                      <li>• Personal identity</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
};
