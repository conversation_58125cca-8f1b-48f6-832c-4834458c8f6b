/**
 * Analytics Stats Cards Component
 * Summary statistics cards for mood analytics dashboard
 */

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  BookOpen, 
  Heart, 
  Calendar,
  Target,
  Zap
} from 'lucide-react';
import { MoodAnalyticsData } from '@/types';

interface AnalyticsStatsCardsProps {
  analytics?: MoodAnalyticsData;
  isLoading?: boolean;
}

/**
 * Emotion emoji mapping
 */
const EMOTION_EMOJIS: Record<string, string> = {
  joyful: '😊',
  calm: '😌',
  neutral: '😐',
  sad: '😢',
  anxious: '😰',
  excited: '🤩',
  grateful: '🙏',
  frustrated: '😤',
  hopeful: '🌟',
  overwhelmed: '😵',
};

/**
 * Get mood color based on score
 */
const getMoodColor = (mood: number): string => {
  if (mood >= 8) return 'text-green-600';
  if (mood >= 6) return 'text-amber-600';
  if (mood >= 4) return 'text-orange-600';
  return 'text-red-600';
};

/**
 * Get trend badge variant
 */
const getTrendVariant = (trend: string): 'default' | 'secondary' | 'destructive' => {
  switch (trend) {
    case 'improving': return 'default';
    case 'declining': return 'destructive';
    default: return 'secondary';
  }
};

/**
 * Loading skeleton for stats cards
 */
const LoadingCard = ({ title, icon: Icon }: { title: string; icon: any }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
      <Icon className="h-4 w-4 text-gray-400" />
    </CardHeader>
    <CardContent>
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
      </div>
    </CardContent>
  </Card>
);

/**
 * Empty state for stats cards
 */
const EmptyCard = ({ title, icon: Icon, description }: { title: string; icon: any; description: string }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
      <Icon className="h-4 w-4 text-gray-400" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold text-gray-400 mb-1">--</div>
      <p className="text-xs text-gray-500">{description}</p>
    </CardContent>
  </Card>
);

export const AnalyticsStatsCards = ({ analytics, isLoading = false }: AnalyticsStatsCardsProps) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <LoadingCard title="Total Entries" icon={BookOpen} />
        <LoadingCard title="Average Mood" icon={Heart} />
        <LoadingCard title="Mood Trend" icon={TrendingUp} />
        <LoadingCard title="Current Streak" icon={Zap} />
      </div>
    );
  }

  if (!analytics || !analytics.stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <EmptyCard 
          title="Total Entries" 
          icon={BookOpen} 
          description="No journal entries yet" 
        />
        <EmptyCard 
          title="Average Mood" 
          icon={Heart} 
          description="Start journaling to track mood" 
        />
        <EmptyCard 
          title="Mood Trend" 
          icon={TrendingUp} 
          description="Need more entries for trends" 
        />
        <EmptyCard 
          title="Current Streak" 
          icon={Zap} 
          description="Begin your journaling streak" 
        />
      </div>
    );
  }

  const { stats } = analytics;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total Entries */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Total Entries</CardTitle>
          <BookOpen className="h-4 w-4 text-amber-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900 mb-1">
            {stats.totalEntries.toLocaleString()}
          </div>
          <p className="text-xs text-gray-600">
            {stats.entriesThisMonth} this month
          </p>
        </CardContent>
      </Card>

      {/* Average Mood */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Average Mood</CardTitle>
          <Heart className="h-4 w-4 text-amber-600" />
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold mb-1 ${getMoodColor(stats.averageMood)}`}>
            {stats.averageMood.toFixed(1)}/10
          </div>
          <p className="text-xs text-gray-600">
            Range: {stats.moodRange.min} - {stats.moodRange.max}
          </p>
          {stats.moodChangePercentage !== 0 && (
            <div className="flex items-center gap-1 mt-1">
              {stats.moodChangePercentage > 0 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span className={`text-xs ${stats.moodChangePercentage > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(stats.moodChangePercentage).toFixed(1)}%
              </span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Mood Trend */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Mood Trend</CardTitle>
          {stats.moodTrend === 'improving' && <TrendingUp className="h-4 w-4 text-green-600" />}
          {stats.moodTrend === 'declining' && <TrendingDown className="h-4 w-4 text-red-600" />}
          {stats.moodTrend === 'stable' && <Minus className="h-4 w-4 text-gray-600" />}
        </CardHeader>
        <CardContent>
          <div className="mb-2">
            <Badge variant={getTrendVariant(stats.moodTrend)} className="capitalize">
              {stats.moodTrend}
            </Badge>
          </div>
          <p className="text-xs text-gray-600">
            Most common: {EMOTION_EMOJIS[stats.mostCommonEmotion]} {stats.mostCommonEmotion}
          </p>
        </CardContent>
      </Card>

      {/* Current Streak */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Current Streak</CardTitle>
          <Zap className="h-4 w-4 text-amber-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900 mb-1">
            {stats.streakDays}
          </div>
          <p className="text-xs text-gray-600">
            {stats.streakDays === 1 ? 'day' : 'days'} in a row
          </p>
          {stats.streakDays >= 7 && (
            <div className="flex items-center gap-1 mt-1">
              <Target className="h-3 w-3 text-green-600" />
              <span className="text-xs text-green-600">Great streak! 🔥</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Stats Row */}
      <Card className="md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Recent Activity</CardTitle>
          <Calendar className="h-4 w-4 text-amber-600" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-lg font-bold text-gray-900">
                {stats.entriesThisWeek}
              </div>
              <p className="text-xs text-gray-600">Entries this week</p>
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900">
                {stats.entriesThisMonth}
              </div>
              <p className="text-xs text-gray-600">Entries this month</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insights Card */}
      <Card className="md:col-span-2">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Quick Insights</CardTitle>
          <Heart className="h-4 w-4 text-amber-600" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            {stats.averageMood >= 7 && (
              <div className="flex items-center gap-2 text-green-700">
                <span>🌟</span>
                <span>You're maintaining a positive mood!</span>
              </div>
            )}
            {stats.streakDays >= 7 && (
              <div className="flex items-center gap-2 text-amber-700">
                <span>🔥</span>
                <span>Amazing consistency with journaling!</span>
              </div>
            )}
            {stats.moodTrend === 'improving' && (
              <div className="flex items-center gap-2 text-green-700">
                <span>📈</span>
                <span>Your mood is trending upward!</span>
              </div>
            )}
            {stats.totalEntries >= 30 && (
              <div className="flex items-center gap-2 text-blue-700">
                <span>📚</span>
                <span>You're building a rich journal history!</span>
              </div>
            )}
            {stats.totalEntries < 5 && (
              <div className="flex items-center gap-2 text-gray-600">
                <span>🌱</span>
                <span>Keep journaling to unlock more insights!</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
