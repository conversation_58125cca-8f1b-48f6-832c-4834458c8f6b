/**
 * Authentication Utilities
 * Centralized utilities for authentication flows, route protection, and user state management
 * Consolidates authentication logic used across components
 */

import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Authentication result interface
 */
export interface AuthResult {
  success: boolean;
  user?: User | null;
  error?: string;
}

/**
 * Sign-in credentials interface
 */
export interface SignInCredentials {
  email: string;
  password: string;
}

/**
 * Sign-up credentials interface
 */
export interface SignUpCredentials {
  email: string;
  password: string;
  fullName?: string;
}

/**
 * Authentication redirect paths
 */
export const AUTH_REDIRECT_PATHS = {
  AFTER_SIGN_IN: '/journal',
  AFTER_SIGN_OUT: '/',
  AUTH_PAGE: '/auth',
  DEFAULT_PROTECTED: '/journal',
} as const;

/**
 * Sign in with email and password
 */
export const signInWithEmail = async (credentials: SignInCredentials): Promise<AuthResult> => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: credentials.email,
      password: credentials.password,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      user: data.user,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'An unexpected error occurred',
    };
  }
};

/**
 * Sign up with email and password
 */
export const signUpWithEmail = async (credentials: SignUpCredentials): Promise<AuthResult> => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email: credentials.email,
      password: credentials.password,
      options: {
        emailRedirectTo: `${window.location.origin}${AUTH_REDIRECT_PATHS.AFTER_SIGN_IN}`,
        data: {
          full_name: credentials.fullName || '',
        },
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      user: data.user,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'An unexpected error occurred',
    };
  }
};

/**
 * Sign in with Google OAuth
 */
export const signInWithGoogle = async (): Promise<AuthResult> => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}${AUTH_REDIRECT_PATHS.AFTER_SIGN_IN}`,
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'An unexpected error occurred',
    };
  }
};

/**
 * Sign out user
 */
export const signOut = async (): Promise<AuthResult> => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'An unexpected error occurred',
    };
  }
};

/**
 * Get current user session
 */
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (user: User | null): boolean => {
  return !!user;
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (password.length > 128) {
    errors.push('Password must be less than 128 characters');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate sign-in credentials
 */
export const validateSignInCredentials = (credentials: SignInCredentials): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  if (!credentials.email.trim()) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(credentials.email)) {
    errors.email = 'Please enter a valid email address';
  }

  if (!credentials.password) {
    errors.password = 'Password is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validate sign-up credentials
 */
export const validateSignUpCredentials = (credentials: SignUpCredentials): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};

  if (!credentials.email.trim()) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(credentials.email)) {
    errors.email = 'Please enter a valid email address';
  }

  const passwordValidation = validatePassword(credentials.password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.errors[0];
  }

  if (credentials.fullName !== undefined && !credentials.fullName.trim()) {
    errors.fullName = 'Full name is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Handle authentication errors with user-friendly messages
 */
export const handleAuthError = (error: string): string => {
  const errorMap: Record<string, string> = {
    'Invalid login credentials': 'Invalid email or password. Please check your credentials and try again.',
    'Email not confirmed': 'Please check your email and click the confirmation link before signing in.',
    'User already registered': 'An account with this email already exists. Please sign in instead.',
    'Password should be at least 6 characters': 'Password must be at least 6 characters long.',
    'Unable to validate email address: invalid format': 'Please enter a valid email address.',
    'signup_disabled': 'Account creation is currently disabled. Please contact support.',
  };

  return errorMap[error] || error;
};

/**
 * Show authentication success message
 */
export const showAuthSuccessMessage = (type: 'signin' | 'signup' | 'signout'): void => {
  const messages = {
    signin: 'Welcome back! You have been signed in successfully.',
    signup: 'Account created successfully! Please check your email to confirm your account.',
    signout: 'You have been signed out successfully.',
  };

  toast.success(messages[type]);
};

/**
 * Show authentication error message
 */
export const showAuthErrorMessage = (error: string): void => {
  const friendlyError = handleAuthError(error);
  toast.error(friendlyError);
};

/**
 * Get redirect path after authentication
 */
export const getRedirectPath = (
  type: 'signin' | 'signout',
  from?: string
): string => {
  if (type === 'signout') {
    return AUTH_REDIRECT_PATHS.AFTER_SIGN_OUT;
  }

  // For sign-in, redirect to the page they came from or default protected page
  if (from && from !== AUTH_REDIRECT_PATHS.AUTH_PAGE) {
    return from;
  }

  return AUTH_REDIRECT_PATHS.AFTER_SIGN_IN;
};

/**
 * Check if a path requires authentication
 */
export const isProtectedRoute = (path: string): boolean => {
  const protectedPaths = ['/journal', '/write', '/settings', '/analytics'];
  return protectedPaths.some(protectedPath => path.startsWith(protectedPath));
};

/**
 * Get authentication redirect URL for the current environment
 */
export const getAuthRedirectUrl = (path: string): string => {
  return `${window.location.origin}${path}`;
};
