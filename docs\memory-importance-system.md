# Memory Importance System

## Overview

The Memory Importance System automatically evaluates and prioritizes memories based on their emotional and contextual significance, reducing memory clutter and ensuring that only meaningful information is retained and surfaced to the AI.

## Key Features

### 1. Automatic Importance Evaluation
- **AI-Powered Scoring**: Uses Gemini AI or Local LLM to evaluate memory importance (1-10 scale)
- **Fallback Heuristics**: Smart fallback system when AI services are unavailable
- **Multi-Source Support**: Supports Gemini API, Local LLM, and heuristic-based evaluation

### 2. Intelligent Memory Filtering
- **Default Filtering**: Returns only memories with importance ≥ 4 by default
- **Top-N Selection**: Limits to top 10 most important memories for AI context
- **Configurable Thresholds**: Customizable importance and limit settings
- **Override Options**: Can retrieve all memories when needed

### 3. Automatic Memory Archiving
- **Low Importance Filtering**: Automatically skips saving memories with importance ≤ 3
- **Smart Preservation**: Keeps moderately important memories (4-6) for context
- **High Priority Surfacing**: Prioritizes highly important memories (7-10) for AI interactions

### 4. Backward Compatibility
- **Default Values**: Existing memories automatically assigned importance = 5
- **Seamless Migration**: No breaking changes to existing functionality
- **Gradual Enhancement**: New memories get AI-evaluated importance scores

## Database Schema

### Memory Table Updates
```sql
ALTER TABLE memory ADD COLUMN importance INTEGER DEFAULT 5 CHECK (importance >= 1 AND importance <= 10);
```

### Indexes for Performance
- `idx_memory_importance`: For importance-based queries
- `idx_memory_importance_updated_at`: For combined importance + recency sorting
- `idx_memory_user_importance`: For user-specific importance queries

## API Changes

### Memory Retrieval Options
```typescript
interface MemoryRetrievalOptions {
  limit?: number;                    // Default: 10 (or unlimited if includeAllMemories)
  minImportance?: number;            // Default: 4
  includeAllMemories?: boolean;      // Default: false
  orderByImportance?: boolean;       // Default: true
}
```

### Updated Functions
- `getUserMemories(options?)`: Now supports importance filtering
- `getUserMemoriesByCategory(category, options?)`: Category-specific with importance
- `addUserMemory()`: Automatically evaluates importance before saving

### New Functions
- `evaluateMemoryImportance(memory)`: AI-powered importance evaluation
- `getAllMemories()`: Backward compatibility function for all memories
- `getImportantMemories(options?)`: Explicit important memory retrieval

## Importance Scoring Guidelines

### AI Evaluation Criteria
The AI evaluates memories based on:
- **Emotional Significance**: Personal relationships, major life events, trauma, achievements
- **Long-term Relevance**: Ongoing situations, persistent preferences, core identity aspects
- **Contextual Importance**: Information that helps understand the user's current state
- **Uniqueness**: Rare or special experiences vs. routine daily activities

### Scoring Scale
- **1-3**: Routine, temporary, or trivial information (daily activities, minor preferences)
- **4-6**: Moderately important information (work situations, casual relationships, general interests)
- **7-8**: Significant information (close relationships, important goals, meaningful experiences)
- **9-10**: Highly significant information (life-changing events, core identity, deep emotional connections)

### Fallback Heuristics
When AI evaluation fails, the system uses:
- **Category-based scoring**: Identity (8), Emotion (7), Goal (7), Event (6), Fact (5), Preference (4)
- **Content length adjustment**: Longer content = potentially more important
- **Emotional keyword detection**: Presence of emotional terms increases importance

## Usage Examples

### Default Behavior (Importance Filtering)
```typescript
// Returns top 10 memories with importance >= 4, ordered by importance
const memories = await getUserMemories();
```

### All Memories (Backward Compatibility)
```typescript
// Returns all memories regardless of importance
const allMemories = await getUserMemories({ includeAllMemories: true });
```

### Custom Filtering
```typescript
// Returns top 5 highly important memories (importance >= 7)
const importantMemories = await getUserMemories({ 
  limit: 5, 
  minImportance: 7 
});
```

### Manual Importance Evaluation
```typescript
const memory = { key: 'life_event', value: 'Got married', category: 'event' };
const evaluation = await evaluateMemoryImportance(memory);
console.log(`Importance: ${evaluation.data.importance}/10`);
```

## Performance Optimizations

### Database Indexes
- Composite indexes for efficient importance + recency queries
- User-specific indexes for multi-tenant performance
- Category-specific indexes for filtered queries

### Query Optimization
- Default LIMIT 10 to prevent large result sets
- Importance-first ordering for relevant results
- Efficient filtering at database level

### Caching Strategy
- React Query integration for client-side caching
- Importance-aware cache invalidation
- Optimized memory context loading

## Migration Strategy

### Phase 1: Database Schema (✅ Complete)
- Added importance column with default value 5
- Created performance indexes
- Added database triggers for automatic defaults

### Phase 2: Service Layer (✅ Complete)
- Implemented importance evaluation service
- Updated memory persistence with filtering
- Added backward compatibility functions

### Phase 3: Context Integration (✅ Complete)
- Updated memory context hooks
- Implemented importance-aware loading
- Maintained existing API compatibility

### Phase 4: UI Integration (Future)
- Memory management interface updates
- Importance visualization
- User control over importance thresholds

## Benefits

### For Users
- **Cleaner Memory Collection**: Only meaningful memories are retained
- **Better AI Interactions**: AI gets more relevant context
- **Automatic Organization**: No manual memory management required
- **Preserved History**: Important memories are never lost

### For System Performance
- **Reduced Memory Overhead**: Fewer memories loaded by default
- **Faster AI Context**: Smaller, more relevant memory sets
- **Efficient Queries**: Database-level filtering and indexing
- **Scalable Architecture**: Handles growing memory collections

### For AI Quality
- **Relevant Context**: Only important memories in AI prompts
- **Focused Responses**: AI can provide more targeted advice
- **Emotional Awareness**: High-importance emotional memories prioritized
- **Contextual Understanding**: Better grasp of user's significant experiences

## Configuration

### Environment Variables
- `VITE_GEMINI_API_KEY`: For Gemini-based importance evaluation
- Local LLM configuration for fallback evaluation

### Tunable Parameters
- Default importance threshold (currently 4)
- Default memory limit (currently 10)
- Importance evaluation timeouts
- Fallback heuristic weights

## Monitoring and Analytics

### Importance Distribution
- Track distribution of importance scores
- Monitor AI evaluation success rates
- Analyze fallback usage patterns

### Performance Metrics
- Memory retrieval performance
- AI evaluation response times
- Cache hit rates for importance queries

### User Experience
- Memory relevance feedback
- AI response quality correlation
- User satisfaction with memory surfacing
