/**
 * Conversation Service
 * Data access layer for reflection conversations and messages
 */

import { supabase } from '@/integrations/supabase/client';
import {
  ReflectionConversation,
  ConversationMessage,
  CreateConversationInput,
  CreateMessageInput,
  ApiResponse,
  DatabaseQueryResult,
  supabaseToReflectionConversation,
  supabaseToConversationMessage,
  reflectionConversationToSupabaseInsert,
  conversationMessageToSupabaseInsert,
} from '@/types';

/**
 * Create a new reflection conversation
 */
export const createReflectionConversation = async (
  input: CreateConversationInput
): Promise<ApiResponse<ReflectionConversation>> => {
  try {
    const insertData = reflectionConversationToSupabaseInsert({
      journal_entry_id: input.journal_entry_id,
      user_id: input.user_id,
    });

    const { data, error } = await supabase
      .from('reflection_conversations')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create conversation: ${error.message}`);
    }

    const conversation = supabaseToReflectionConversation(data);

    // If there's an initial message, create it
    if (input.initial_message) {
      await createConversationMessage({
        conversation_id: conversation.id,
        ...input.initial_message,
      });
    }

    return {
      success: true,
      data: conversation,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('Error creating reflection conversation:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to create conversation',
        code: 'CONVERSATION_CREATE_FAILED',
      },
    };
  }
};

/**
 * Get reflection conversation by journal entry ID
 */
export const getConversationByJournalEntry = async (
  journalEntryId: string,
  userId: string
): Promise<ApiResponse<ReflectionConversation | null>> => {
  try {
    const { data, error } = await supabase
      .from('reflection_conversations')
      .select('*')
      .eq('journal_entry_id', journalEntryId)
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(`Failed to fetch conversation: ${error.message}`);
    }

    const conversation = data ? supabaseToReflectionConversation(data) : null;

    return {
      success: true,
      data: conversation,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('Error fetching conversation by journal entry:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to fetch conversation',
        code: 'CONVERSATION_FETCH_FAILED',
      },
    };
  }
};

/**
 * Get conversation messages with pagination
 */
export const getConversationMessages = async (
  conversationId: string,
  options: {
    limit?: number;
    offset?: number;
    orderBy?: 'created_at' | 'id';
    ascending?: boolean;
  } = {}
): Promise<ApiResponse<ConversationMessage[]>> => {
  try {
    const {
      limit = 50,
      offset = 0,
      orderBy = 'created_at',
      ascending = true,
    } = options;

    let query = supabase
      .from('reflection_conversation_messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order(orderBy, { ascending })
      .range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch messages: ${error.message}`);
    }

    const messages = data.map(supabaseToConversationMessage);

    return {
      success: true,
      data: messages,
      meta: {
        timestamp: new Date().toISOString(),
        pagination: {
          limit,
          offset,
          count: data.length,
        },
      },
    };
  } catch (error) {
    console.error('Error fetching conversation messages:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to fetch messages',
        code: 'MESSAGES_FETCH_FAILED',
      },
    };
  }
};

/**
 * Create a new conversation message
 */
export const createConversationMessage = async (
  input: CreateMessageInput
): Promise<ApiResponse<ConversationMessage>> => {
  try {
    const insertData = conversationMessageToSupabaseInsert({
      conversation_id: input.conversation_id,
      sender_type: input.sender_type,
      message_content: input.message_content,
      message_type: input.message_type || 'text',
      ai_metadata: input.ai_metadata || null,
    });

    const { data, error } = await supabase
      .from('reflection_conversation_messages')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create message: ${error.message}`);
    }

    const message = supabaseToConversationMessage(data);

    // Update conversation's updated_at timestamp
    await supabase
      .from('reflection_conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', input.conversation_id);

    return {
      success: true,
      data: message,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('Error creating conversation message:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to create message',
        code: 'MESSAGE_CREATE_FAILED',
      },
    };
  }
};

/**
 * Get conversation with messages
 */
export const getConversationWithMessages = async (
  conversationId: string,
  messageOptions?: {
    limit?: number;
    offset?: number;
  }
): Promise<ApiResponse<ReflectionConversation & { messages: ConversationMessage[] }>> => {
  try {
    // Get conversation
    const { data: conversationData, error: conversationError } = await supabase
      .from('reflection_conversations')
      .select('*')
      .eq('id', conversationId)
      .single();

    if (conversationError) {
      throw new Error(`Failed to fetch conversation: ${conversationError.message}`);
    }

    // Get messages
    const messagesResult = await getConversationMessages(conversationId, messageOptions);
    
    if (!messagesResult.success) {
      throw new Error(messagesResult.error?.message || 'Failed to fetch messages');
    }

    const conversation = supabaseToReflectionConversation(conversationData);
    const messages = messagesResult.data || [];

    return {
      success: true,
      data: {
        ...conversation,
        messages,
      },
      meta: {
        timestamp: new Date().toISOString(),
        messageCount: messages.length,
      },
    };
  } catch (error) {
    console.error('Error fetching conversation with messages:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to fetch conversation',
        code: 'CONVERSATION_WITH_MESSAGES_FETCH_FAILED',
      },
    };
  }
};

/**
 * Delete a conversation and all its messages
 */
export const deleteConversation = async (
  conversationId: string,
  userId: string
): Promise<ApiResponse<void>> => {
  try {
    // Verify ownership
    const { data: conversation, error: fetchError } = await supabase
      .from('reflection_conversations')
      .select('user_id')
      .eq('id', conversationId)
      .single();

    if (fetchError) {
      throw new Error(`Failed to verify conversation ownership: ${fetchError.message}`);
    }

    if (conversation.user_id !== userId) {
      throw new Error('Unauthorized: Cannot delete conversation owned by another user');
    }

    // Delete conversation (messages will be deleted via CASCADE)
    const { error: deleteError } = await supabase
      .from('reflection_conversations')
      .delete()
      .eq('id', conversationId);

    if (deleteError) {
      throw new Error(`Failed to delete conversation: ${deleteError.message}`);
    }

    return {
      success: true,
      data: undefined,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('Error deleting conversation:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to delete conversation',
        code: 'CONVERSATION_DELETE_FAILED',
      },
    };
  }
};

/**
 * Get user's conversation count
 */
export const getUserConversationCount = async (
  userId: string
): Promise<ApiResponse<number>> => {
  try {
    const { count, error } = await supabase
      .from('reflection_conversations')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Failed to count conversations: ${error.message}`);
    }

    return {
      success: true,
      data: count || 0,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('Error counting user conversations:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to count conversations',
        code: 'CONVERSATION_COUNT_FAILED',
      },
    };
  }
};

/**
 * Get conversations older than specified days for cleanup
 */
export const getOldConversations = async (
  userId: string,
  daysOld: number = 90
): Promise<ApiResponse<ReflectionConversation[]>> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const { data, error } = await supabase
      .from('reflection_conversations')
      .select('*')
      .eq('user_id', userId)
      .lt('updated_at', cutoffDate.toISOString())
      .order('updated_at', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch old conversations: ${error.message}`);
    }

    const conversations = data.map(supabaseToReflectionConversation);

    return {
      success: true,
      data: conversations,
      meta: {
        timestamp: new Date().toISOString(),
        cutoffDate: cutoffDate.toISOString(),
        count: conversations.length,
      },
    };
  } catch (error) {
    console.error('Error fetching old conversations:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to fetch old conversations',
        code: 'OLD_CONVERSATIONS_FETCH_FAILED',
      },
    };
  }
};

/**
 * Bulk delete conversations by IDs
 */
export const bulkDeleteConversations = async (
  conversationIds: string[],
  userId: string
): Promise<ApiResponse<{ deletedCount: number }>> => {
  try {
    if (conversationIds.length === 0) {
      return {
        success: true,
        data: { deletedCount: 0 },
        meta: {
          timestamp: new Date().toISOString(),
        },
      };
    }

    // Verify all conversations belong to the user
    const { data: conversations, error: fetchError } = await supabase
      .from('reflection_conversations')
      .select('id, user_id')
      .in('id', conversationIds);

    if (fetchError) {
      throw new Error(`Failed to verify conversation ownership: ${fetchError.message}`);
    }

    const unauthorizedConversations = conversations?.filter(c => c.user_id !== userId) || [];
    if (unauthorizedConversations.length > 0) {
      throw new Error('Unauthorized: Cannot delete conversations owned by another user');
    }

    // Delete conversations (messages will be deleted via CASCADE)
    const { error: deleteError, count } = await supabase
      .from('reflection_conversations')
      .delete({ count: 'exact' })
      .in('id', conversationIds);

    if (deleteError) {
      throw new Error(`Failed to delete conversations: ${deleteError.message}`);
    }

    return {
      success: true,
      data: { deletedCount: count || 0 },
      meta: {
        timestamp: new Date().toISOString(),
        requestedCount: conversationIds.length,
      },
    };
  } catch (error) {
    console.error('Error bulk deleting conversations:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to delete conversations',
        code: 'BULK_DELETE_FAILED',
      },
    };
  }
};

/**
 * Get conversation analytics for a user
 */
export const getConversationAnalytics = async (
  userId: string,
  timeRange: {
    startDate?: string;
    endDate?: string;
  } = {}
): Promise<ApiResponse<{
  totalConversations: number;
  totalMessages: number;
  averageMessagesPerConversation: number;
  mostActiveDay: string | null;
  conversationsByMonth: Array<{ month: string; count: number }>;
}>> => {
  try {
    const { startDate, endDate } = timeRange;

    // Build base query
    let conversationQuery = supabase
      .from('reflection_conversations')
      .select('id, created_at, updated_at')
      .eq('user_id', userId);

    if (startDate) {
      conversationQuery = conversationQuery.gte('created_at', startDate);
    }
    if (endDate) {
      conversationQuery = conversationQuery.lte('created_at', endDate);
    }

    const { data: conversations, error: conversationError } = await conversationQuery;

    if (conversationError) {
      throw new Error(`Failed to fetch conversations: ${conversationError.message}`);
    }

    // Get message count
    let messageQuery = supabase
      .from('reflection_conversation_messages')
      .select('id, created_at, conversation_id')
      .in('conversation_id', conversations?.map(c => c.id) || []);

    const { data: messages, error: messageError } = await messageQuery;

    if (messageError) {
      throw new Error(`Failed to fetch messages: ${messageError.message}`);
    }

    const totalConversations = conversations?.length || 0;
    const totalMessages = messages?.length || 0;
    const averageMessagesPerConversation = totalConversations > 0
      ? Math.round((totalMessages / totalConversations) * 100) / 100
      : 0;

    // Calculate most active day (simplified)
    const messageDates = messages?.map(m => m.created_at.split('T')[0]) || [];
    const dateCounts = messageDates.reduce((acc, date) => {
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostActiveDay = Object.keys(dateCounts).length > 0
      ? Object.entries(dateCounts).sort(([,a], [,b]) => b - a)[0][0]
      : null;

    // Calculate conversations by month
    const conversationsByMonth = conversations?.reduce((acc, conv) => {
      const month = conv.created_at.substring(0, 7); // YYYY-MM
      const existing = acc.find(item => item.month === month);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ month, count: 1 });
      }
      return acc;
    }, [] as Array<{ month: string; count: number }>) || [];

    return {
      success: true,
      data: {
        totalConversations,
        totalMessages,
        averageMessagesPerConversation,
        mostActiveDay,
        conversationsByMonth: conversationsByMonth.sort((a, b) => a.month.localeCompare(b.month)),
      },
      meta: {
        timestamp: new Date().toISOString(),
        timeRange,
      },
    };
  } catch (error) {
    console.error('Error fetching conversation analytics:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to fetch analytics',
        code: 'ANALYTICS_FETCH_FAILED',
      },
    };
  }
};
