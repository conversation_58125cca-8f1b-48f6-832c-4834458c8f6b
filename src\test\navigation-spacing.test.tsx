/**
 * Navigation Spacing Tests
 * Tests to verify the navigation bar has proper spacing and responsive behavior
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Navigation } from '@/components/layout/Navigation';

// Mock the auth context
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: { username: 'testuser' },
};

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: mockUser,
    loading: false,
    signOut: vi.fn(),
  }),
}));

vi.mock('@/hooks/useUserProfile', () => ({
  useUserProfile: () => ({
    profile: {
      full_name: 'Test User',
      avatar_url: null,
    },
  }),
}));

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Navigation Spacing', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render navigation with proper structure', () => {
    renderWithProviders(<Navigation />);
    
    // Check that the logo is present
    expect(screen.getByText('Amberglow')).toBeInTheDocument();
    
    // Check that navigation buttons are present
    expect(screen.getByText('Journal')).toBeInTheDocument();
    expect(screen.getByText('Write')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
  });

  it('should have proper CSS classes for spacing', () => {
    const { container } = renderWithProviders(<Navigation />);
    
    // Check that the main container has proper spacing
    const navContainer = container.querySelector('nav');
    expect(navContainer).toHaveClass('sticky', 'top-0', 'z-50');
    
    // Check that the flex container has gap
    const flexContainer = container.querySelector('.flex.items-center.justify-between');
    expect(flexContainer).toHaveClass('gap-4');
    
    // Check that navigation buttons container has proper spacing
    const buttonContainer = container.querySelector('.flex.items-center.gap-1');
    expect(buttonContainer).toBeInTheDocument();
  });

  it('should have flex-shrink-0 on navigation elements', () => {
    const { container } = renderWithProviders(<Navigation />);
    
    // Check that buttons have flex-shrink-0 class
    const journalButton = screen.getByText('Journal').closest('button');
    expect(journalButton).toHaveClass('flex-shrink-0');
    
    const writeButton = screen.getByText('Write').closest('button');
    expect(writeButton).toHaveClass('flex-shrink-0');
    
    const analyticsButton = screen.getByText('Analytics').closest('button');
    expect(analyticsButton).toHaveClass('flex-shrink-0');
  });

  it('should have responsive text hiding on small screens', () => {
    renderWithProviders(<Navigation />);
    
    // Check that text spans have hidden sm:inline classes
    const journalSpan = screen.getByText('Journal');
    expect(journalSpan).toHaveClass('hidden', 'sm:inline');
    
    const writeSpan = screen.getByText('Write');
    expect(writeSpan).toHaveClass('hidden', 'sm:inline');
    
    const analyticsSpan = screen.getByText('Analytics');
    expect(analyticsSpan).toHaveClass('hidden', 'sm:inline');
  });

  it('should have proper button sizes', () => {
    renderWithProviders(<Navigation />);
    
    // Check that buttons have small size
    const journalButton = screen.getByText('Journal').closest('button');
    expect(journalButton).toHaveClass('h-9'); // sm size in shadcn/ui
    
    const writeButton = screen.getByText('Write').closest('button');
    expect(writeButton).toHaveClass('h-9');
    
    const analyticsButton = screen.getByText('Analytics').closest('button');
    expect(analyticsButton).toHaveClass('h-9');
  });
});
