/**
 * Memory Recall Service
 * Service for retrieving relevant memories for AI chat context using semantic similarity
 */

import { UserMemory, ApiResponse } from '@/types';
import { generateEmbedding, calculateCosineSimilarity } from './embeddingService';
import { getUserMemories } from './memoryPersistenceService';

/**
 * Configuration for memory recall
 */
export interface MemoryRecallConfig {
  /** Maximum number of memories to recall */
  maxMemories: number;
  /** Minimum semantic similarity threshold (0-1) */
  minSimilarity: number;
  /** Minimum importance threshold (1-10) */
  minImportance: number;
  /** Weight for similarity vs importance in ranking (0-1, higher = more similarity weight) */
  similarityWeight: number;
  /** Whether to include low-importance but highly relevant memories */
  includeHighRelevance: boolean;
  /** High relevance threshold for including low-importance memories */
  highRelevanceThreshold: number;
}

/**
 * Memory with recall score
 */
export interface RecalledMemory extends UserMemory {
  /** Semantic similarity score (0-1) */
  similarity: number;
  /** Combined recall score (similarity + importance) */
  recallScore: number;
  /** Reason why this memory was recalled */
  recallReason: string;
}

/**
 * Memory recall result
 */
export interface MemoryRecallResult {
  /** Successfully recalled memories */
  memories: RecalledMemory[];
  /** Total number of memories considered */
  totalConsidered: number;
  /** Number of memories with embeddings */
  memoriesWithEmbeddings: number;
  /** Processing time in milliseconds */
  processingTime: number;
  /** Whether semantic recall was used (vs fallback) */
  usedSemanticRecall: boolean;
}

/**
 * Default memory recall configuration
 */
const DEFAULT_RECALL_CONFIG: MemoryRecallConfig = {
  maxMemories: 8,
  minSimilarity: 0.3,
  minImportance: 4,
  similarityWeight: 0.7,
  includeHighRelevance: true,
  highRelevanceThreshold: 0.8,
};

/**
 * Recall relevant memories for a given prompt using semantic similarity
 * @param prompt The user's message or context to find relevant memories for
 * @param config Optional configuration for memory recall
 * @returns Promise with recalled memories and metadata
 */
export const recallRelevantMemories = async (
  prompt: string,
  config: Partial<MemoryRecallConfig> = {}
): Promise<ApiResponse<MemoryRecallResult>> => {
  const startTime = Date.now();
  const recallConfig = { ...DEFAULT_RECALL_CONFIG, ...config };

  try {
    console.log(`🧠 [RECALL] Starting memory recall for prompt: "${prompt.substring(0, 100)}..."`);

    // Generate embedding for the prompt
    const promptEmbeddingResult = await generateEmbedding(prompt);
    
    if (!promptEmbeddingResult.success) {
      console.warn('🧠 [RECALL] Failed to generate prompt embedding, using fallback recall');
      return await fallbackMemoryRecall(prompt, recallConfig, startTime);
    }

    // Get all user memories with embeddings
    const memoriesResult = await getUserMemories({
      includeAllMemories: true,
      minImportance: 1, // Get all memories for semantic comparison
    });

    if (!memoriesResult.success || !memoriesResult.data) {
      return {
        success: false,
        error: { message: 'Failed to retrieve user memories' },
      };
    }

    const allMemories = memoriesResult.data;
    console.log(`🧠 [RECALL] Considering ${allMemories.length} total memories`);

    // Calculate semantic similarity for memories with embeddings
    const recalledMemories: RecalledMemory[] = [];
    let memoriesWithEmbeddings = 0;

    for (const memory of allMemories) {
      if (!memory.embedding) {
        continue;
      }

      memoriesWithEmbeddings++;

      try {
        // Parse memory embedding
        let memoryEmbedding: number[];
        if (typeof memory.embedding === 'string') {
          memoryEmbedding = JSON.parse(memory.embedding);
        } else if (Array.isArray(memory.embedding)) {
          memoryEmbedding = memory.embedding;
        } else {
          continue;
        }

        // Calculate semantic similarity
        const similarity = calculateCosineSimilarity(promptEmbeddingResult.data, memoryEmbedding);
        
        // Apply similarity threshold
        if (similarity < recallConfig.minSimilarity) {
          continue;
        }

        // Check importance threshold with high relevance exception
        const importance = memory.importance || 5;
        const meetsImportanceThreshold = importance >= recallConfig.minImportance;
        const isHighlyRelevant = recallConfig.includeHighRelevance && similarity >= recallConfig.highRelevanceThreshold;

        if (!meetsImportanceThreshold && !isHighlyRelevant) {
          continue;
        }

        // Calculate combined recall score
        const normalizedImportance = importance / 10; // Normalize to 0-1
        const recallScore = (similarity * recallConfig.similarityWeight) + 
                           (normalizedImportance * (1 - recallConfig.similarityWeight));

        // Determine recall reason
        let recallReason = 'semantic_similarity';
        if (isHighlyRelevant && !meetsImportanceThreshold) {
          recallReason = 'high_relevance_override';
        } else if (similarity >= 0.8) {
          recallReason = 'high_semantic_match';
        } else if (importance >= 8) {
          recallReason = 'high_importance';
        }

        recalledMemories.push({
          ...memory,
          similarity,
          recallScore,
          recallReason,
        });

        console.log(`🧠 [RECALL] Memory "${memory.key}": similarity=${similarity.toFixed(3)}, importance=${importance}, score=${recallScore.toFixed(3)}, reason=${recallReason}`);

      } catch (error) {
        console.warn(`🧠 [RECALL] Error processing memory "${memory.key}":`, error);
        continue;
      }
    }

    // Sort by recall score and take top memories
    const sortedMemories = recalledMemories
      .sort((a, b) => b.recallScore - a.recallScore)
      .slice(0, recallConfig.maxMemories);

    const processingTime = Date.now() - startTime;
    
    console.log(`🧠 [RECALL] Recalled ${sortedMemories.length} memories from ${memoriesWithEmbeddings} with embeddings (${processingTime}ms)`);
    
    if (sortedMemories.length > 0) {
      console.log('🧠 [RECALL] Top recalled memories:', sortedMemories.slice(0, 3).map(m => 
        `${m.key} (similarity: ${m.similarity.toFixed(3)}, score: ${m.recallScore.toFixed(3)})`
      ));
    }

    return {
      success: true,
      data: {
        memories: sortedMemories,
        totalConsidered: allMemories.length,
        memoriesWithEmbeddings,
        processingTime,
        usedSemanticRecall: true,
      },
    };

  } catch (error) {
    console.error('🧠 [RECALL] Error in semantic memory recall:', error);
    
    // Fallback to simple recall
    return await fallbackMemoryRecall(prompt, recallConfig, startTime);
  }
};

/**
 * Fallback memory recall using word matching when semantic recall fails
 */
const fallbackMemoryRecall = async (
  prompt: string,
  config: MemoryRecallConfig,
  startTime: number
): Promise<ApiResponse<MemoryRecallResult>> => {
  try {
    console.log('🧠 [RECALL] Using fallback word-matching recall');

    // Get high-importance memories as fallback
    const memoriesResult = await getUserMemories({
      minImportance: config.minImportance,
      limit: config.maxMemories,
      orderByImportance: true,
    });

    if (!memoriesResult.success || !memoriesResult.data) {
      return {
        success: false,
        error: { message: 'Failed to retrieve memories for fallback recall' },
      };
    }

    // Simple word matching for relevance
    const promptWords = prompt.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    
    const recalledMemories: RecalledMemory[] = memoriesResult.data.map(memory => {
      const memoryText = `${memory.key} ${memory.value}`.toLowerCase();
      const matchCount = promptWords.filter(word => memoryText.includes(word)).length;
      const similarity = promptWords.length > 0 ? matchCount / promptWords.length : 0;
      
      return {
        ...memory,
        similarity,
        recallScore: similarity * 0.5 + (memory.importance || 5) / 10 * 0.5,
        recallReason: 'word_matching_fallback',
      };
    }).filter(m => m.similarity > 0.1); // Basic relevance filter

    const processingTime = Date.now() - startTime;

    return {
      success: true,
      data: {
        memories: recalledMemories.slice(0, config.maxMemories),
        totalConsidered: memoriesResult.data.length,
        memoriesWithEmbeddings: 0,
        processingTime,
        usedSemanticRecall: false,
      },
    };

  } catch (error) {
    console.error('🧠 [RECALL] Fallback memory recall failed:', error);
    
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Memory recall failed',
      },
    };
  }
};

/**
 * Format recalled memories for AI context injection
 * @param memories Array of recalled memories
 * @returns Formatted string for AI prompt context
 */
export const formatMemoriesForAIContext = (memories: RecalledMemory[]): string => {
  if (memories.length === 0) {
    return '';
  }

  const memoryContext = memories.map((memory, index) => {
    const relevanceIndicator = memory.similarity >= 0.8 ? '🔥' : 
                              memory.similarity >= 0.6 ? '⭐' : 
                              memory.similarity >= 0.4 ? '💡' : '📝';
    
    return `${index + 1}. ${relevanceIndicator} ${memory.key}: ${memory.value}`;
  }).join('\n');

  return `\n\n**Relevant memories about the user:**\n${memoryContext}\n\nUse these memories to provide more personalized and contextually aware responses.`;
};

/**
 * Get memory recall statistics for debugging
 */
export const getMemoryRecallStats = async (): Promise<{
  totalMemories: number;
  memoriesWithEmbeddings: number;
  embeddingCoverage: number;
}> => {
  try {
    const memoriesResult = await getUserMemories({
      includeAllMemories: true,
      minImportance: 1,
    });

    if (!memoriesResult.success || !memoriesResult.data) {
      return { totalMemories: 0, memoriesWithEmbeddings: 0, embeddingCoverage: 0 };
    }

    const totalMemories = memoriesResult.data.length;
    const memoriesWithEmbeddings = memoriesResult.data.filter(m => m.embedding).length;
    const embeddingCoverage = totalMemories > 0 ? memoriesWithEmbeddings / totalMemories : 0;

    return {
      totalMemories,
      memoriesWithEmbeddings,
      embeddingCoverage,
    };
  } catch (error) {
    console.error('🧠 [RECALL] Error getting memory recall stats:', error);
    return { totalMemories: 0, memoriesWithEmbeddings: 0, embeddingCoverage: 0 };
  }
};
