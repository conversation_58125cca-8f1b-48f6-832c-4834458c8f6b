/**
 * Performance Monitoring Service
 * Comprehensive performance tracking and monitoring for the application
 */

import {
  PerformanceMetrics,
  PerformanceMonitoringConfig,
  PerformanceAlert,
  PerformanceBudget,
} from '@/types/performance';
import { getEnvironmentConfig } from '@/config/environment.config';
import { handleError } from '@/utils/errorHandler';

/**
 * Performance monitoring service
 */
class PerformanceMonitoringService {
  private config: PerformanceMonitoringConfig;
  private metrics: PerformanceMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private budget: PerformanceBudget;
  private observer?: PerformanceObserver;

  constructor() {
    const env = getEnvironmentConfig();

    this.config = {
      enabled: env.isDevelopment || env.features.debugMode,
      samplingRate: env.isDevelopment ? 1.0 : 0.1, // 100% in dev, 10% in prod
      metricsToTrack: [
        'renderTime',
        'apiResponseTime',
        'cacheHitRate',
        'memoryUsage',
        'firstContentfulPaint',
        'largestContentfulPaint',
        'cumulativeLayoutShift',
      ],
      reportingInterval: 30000, // 30 seconds
      maxMetricsHistory: 1000,
    };

    this.budget = {
      maxBundleSize: 1024, // 1MB
      maxRenderTime: 16, // 60fps
      maxApiResponseTime: 2000, // 2 seconds
      minCacheHitRate: 0.8, // 80%
      maxMemoryUsage: 100, // 100MB
    };

    if (this.config.enabled) {
      this.initialize();
    }
  }

  /**
   * Initialize performance monitoring
   */
  private initialize(): void {
    // Set up performance observer for web vitals
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.setupPerformanceObserver();
    }

    // Set up periodic reporting
    setInterval(() => {
      this.reportMetrics();
    }, this.config.reportingInterval);

    // Monitor memory usage
    if (typeof window !== 'undefined') {
      this.monitorMemoryUsage();
    }
  }

  /**
   * Set up performance observer for web vitals
   */
  private setupPerformanceObserver(): void {
    try {
      this.observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      // Observe different types of performance entries
      this.observer.observe({ entryTypes: ['measure', 'navigation', 'paint', 'layout-shift'] });
    } catch (error) {
      console.warn('Failed to set up performance observer:', error);
    }
  }

  /**
   * Process performance entry
   */
  private processPerformanceEntry(entry: PerformanceEntry): void {
    const metrics: Partial<PerformanceMetrics> = {
      renderTime: 0,
      apiResponseTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
    };

    switch (entry.entryType) {
      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          metrics.firstContentfulPaint = entry.startTime;
        }
        break;

      case 'largest-contentful-paint':
        metrics.largestContentfulPaint = entry.startTime;
        break;

      case 'layout-shift':
        if ('value' in entry) {
          metrics.cumulativeLayoutShift = (entry as any).value;
        }
        break;

      case 'measure':
        if (entry.name.includes('render')) {
          metrics.renderTime = entry.duration;
        } else if (entry.name.includes('api')) {
          metrics.apiResponseTime = entry.duration;
        }
        break;
    }

    this.recordMetrics(metrics as PerformanceMetrics);
  }

  /**
   * Monitor memory usage
   */
  private monitorMemoryUsage(): void {
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB

        this.recordMetrics({
          renderTime: 0,
          apiResponseTime: 0,
          cacheHitRate: 0,
          memoryUsage,
        });
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Record performance metrics
   */
  recordMetrics(metrics: PerformanceMetrics): void {
    if (!this.config.enabled) return;

    // Apply sampling rate
    if (Math.random() > this.config.samplingRate) return;

    // Add timestamp
    const timestampedMetrics = {
      ...metrics,
      timestamp: Date.now(),
    };

    this.metrics.push(timestampedMetrics);

    // Limit metrics history
    if (this.metrics.length > this.config.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.config.maxMetricsHistory);
    }

    // Check for performance budget violations
    this.checkPerformanceBudget(metrics);
  }

  /**
   * Check performance budget violations
   */
  private checkPerformanceBudget(metrics: PerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];

    if (metrics.renderTime > this.budget.maxRenderTime) {
      alerts.push({
        type: 'warning',
        metric: 'renderTime',
        threshold: this.budget.maxRenderTime,
        message: `Render time (${metrics.renderTime}ms) exceeds budget (${this.budget.maxRenderTime}ms)`,
        timestamp: Date.now(),
        suggestions: [
          'Consider using React.memo for expensive components',
          'Implement virtualization for large lists',
          'Use useMemo and useCallback for expensive computations',
        ],
      });
    }

    if (metrics.apiResponseTime > this.budget.maxApiResponseTime) {
      alerts.push({
        type: 'error',
        metric: 'apiResponseTime',
        threshold: this.budget.maxApiResponseTime,
        message: `API response time (${metrics.apiResponseTime}ms) exceeds budget (${this.budget.maxApiResponseTime}ms)`,
        timestamp: Date.now(),
        suggestions: [
          'Implement request caching',
          'Optimize database queries',
          'Consider using a CDN',
        ],
      });
    }

    if (metrics.cacheHitRate < this.budget.minCacheHitRate) {
      alerts.push({
        type: 'warning',
        metric: 'cacheHitRate',
        threshold: this.budget.minCacheHitRate,
        message: `Cache hit rate (${metrics.cacheHitRate}) is below budget (${this.budget.minCacheHitRate})`,
        timestamp: Date.now(),
        suggestions: [
          'Review cache configuration',
          'Increase cache TTL for stable data',
          'Implement prefetching strategies',
        ],
      });
    }

    if (metrics.memoryUsage > this.budget.maxMemoryUsage) {
      alerts.push({
        type: 'error',
        metric: 'memoryUsage',
        threshold: this.budget.maxMemoryUsage,
        message: `Memory usage (${metrics.memoryUsage}MB) exceeds budget (${this.budget.maxMemoryUsage}MB)`,
        timestamp: Date.now(),
        suggestions: [
          'Check for memory leaks',
          'Implement component cleanup',
          'Optimize large data structures',
        ],
      });
    }

    // Store and report alerts
    this.alerts.push(...alerts);
    alerts.forEach(alert => this.reportAlert(alert));
  }

  /**
   * Report performance alert
   */
  private reportAlert(alert: PerformanceAlert): void {
    const env = getEnvironmentConfig();

    if (env.features.debugMode) {
      console.warn(`[Performance Alert] ${alert.message}`, {
        metric: alert.metric,
        threshold: alert.threshold,
        suggestions: alert.suggestions,
      });
    }

    // Report to error handling system
    handleError(
      new Error(alert.message),
      {
        component: 'PerformanceMonitoring',
        action: 'performance_alert',
        metadata: {
          metric: alert.metric,
          threshold: alert.threshold,
          type: alert.type,
        },
      },
      { showToast: false, logToConsole: false }
    );
  }

  /**
   * Report metrics summary
   */
  private reportMetrics(): void {
    if (this.metrics.length === 0) return;

    const recentMetrics = this.metrics.slice(-100); // Last 100 metrics
    const summary = this.calculateMetricsSummary(recentMetrics);

    const env = getEnvironmentConfig();
    if (env.features.debugMode) {
      console.log('[Performance Summary]', summary);
    }
  }

  /**
   * Calculate metrics summary
   */
  private calculateMetricsSummary(metrics: PerformanceMetrics[]) {
    const summary = {
      averageRenderTime: 0,
      averageApiResponseTime: 0,
      averageCacheHitRate: 0,
      averageMemoryUsage: 0,
      p95RenderTime: 0,
      p95ApiResponseTime: 0,
      totalMetrics: metrics.length,
    };

    if (metrics.length === 0) return summary;

    // Calculate averages
    summary.averageRenderTime = metrics.reduce((sum, m) => sum + m.renderTime, 0) / metrics.length;
    summary.averageApiResponseTime =
      metrics.reduce((sum, m) => sum + m.apiResponseTime, 0) / metrics.length;
    summary.averageCacheHitRate =
      metrics.reduce((sum, m) => sum + m.cacheHitRate, 0) / metrics.length;
    summary.averageMemoryUsage =
      metrics.reduce((sum, m) => sum + m.memoryUsage, 0) / metrics.length;

    // Calculate 95th percentiles
    const sortedRenderTimes = metrics.map(m => m.renderTime).sort((a, b) => a - b);
    const sortedApiTimes = metrics.map(m => m.apiResponseTime).sort((a, b) => a - b);

    const p95Index = Math.floor(metrics.length * 0.95);
    summary.p95RenderTime = sortedRenderTimes[p95Index] || 0;
    summary.p95ApiResponseTime = sortedApiTimes[p95Index] || 0;

    return summary;
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  /**
   * Get current alerts
   */
  getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  /**
   * Clear metrics and alerts
   */
  clear(): void {
    this.metrics = [];
    this.alerts = [];
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<PerformanceMonitoringConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Update performance budget
   */
  updateBudget(budget: Partial<PerformanceBudget>): void {
    this.budget = { ...this.budget, ...budget };
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// Export singleton instance
export const performanceService = new PerformanceMonitoringService();

// Export utility functions
export const measurePerformance = (name: string, fn: () => void | Promise<void>) => {
  const start = performance.now();

  const result = fn();

  if (result instanceof Promise) {
    return result.finally(() => {
      const end = performance.now();
      performance.measure(name, { start, end });
    });
  } else {
    const end = performance.now();
    performance.measure(name, { start, end });
    return result;
  }
};

export const markPerformance = (name: string) => {
  performance.mark(name);
};

export const measureBetweenMarks = (name: string, startMark: string, endMark: string) => {
  performance.measure(name, startMark, endMark);
};
