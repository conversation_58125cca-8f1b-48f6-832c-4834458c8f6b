/**
 * AI Reflection Service with Local LLM Integration
 * Enhanced service with strict typing, error handling, and response validation
 * Migrated from Google Gemini to Ollama local LLM
 */

import {
  AIReflectionInput,
  GeminiAIResponse,
  LocalLLMResponse,
  AIResponse,
  AIReflectionResponse,
  AIServiceResponse,
  AIError,
  AIServiceConfig,
  ApiResponse,
} from '@/types';
import { handleApiError } from '@/utils/errorHandler';
import { processAIResponse, scoreResponseQuality } from '@/utils/aiValidation';
import { getAIConfig, renderPromptTemplate } from '@/config/ai.config';
import {
  generateLocalLLMReflection,
  LocalLLMError,
  LocalLLMErrorType
} from './localLLMService';
import { extractMemoriesFromJournalEntry } from './memoryExtractionService';
import { getRelevantMemories } from './memoryRelevanceService';

/**
 * Maps AI service errors to our standardized error types
 */
const mapAIError = (error: Error | any): AIError => {
  const message = error.message?.toLowerCase() || '';

  if (message.includes('api_key_invalid') || message.includes('invalid api key')) {
    return {
      type: 'api_key_invalid',
      message: 'Invalid API key. Please check your Gemini API configuration.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('quota') || message.includes('rate limit')) {
    return {
      type: 'quota_exceeded',
      message: 'API quota exceeded. Please try again later.',
      details: error.message,
      retryable: true,
    };
  }

  if (message.includes('404') || message.includes('model not found')) {
    return {
      type: 'model_not_found',
      message: 'AI model not available. Using fallback responses.',
      details: error.message,
      retryable: false,
    };
  }

  if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
    return {
      type: 'network_error',
      message: 'Network error. Please check your connection and try again.',
      details: error.message,
      retryable: true,
    };
  }

  if (message.includes('parse') || message.includes('json')) {
    return {
      type: 'parsing_error',
      message: 'Failed to parse AI response. Using fallback response.',
      details: error.message,
      retryable: true,
    };
  }

  return {
    type: 'unknown_error',
    message: 'An unexpected error occurred while generating AI reflection.',
    details: error.message,
    retryable: true,
  };
};

/**
 * Check if local LLM service is available
 */
const isLocalLLMAvailable = async (config: AIServiceConfig): Promise<boolean> => {
  try {
    const endpoint = config.localLLMEndpoint || 'http://localhost:11434/v1/chat/completions';
    // Use the correct Ollama API endpoint for checking service availability
    const checkEndpoint = endpoint.replace('/v1/chat/completions', '/api/tags');
    const response = await fetch(checkEndpoint, {
      method: 'GET',
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });
    return response.ok;
  } catch (error) {
    console.warn('Local LLM service not available:', error);
    return false;
  }
};

/**
 * Convert LocalLLMError to AIError for compatibility
 */
const convertLocalLLMError = (error: LocalLLMError): AIError => {
  const errorTypeMap: Record<LocalLLMErrorType, AIErrorType> = {
    'service_unavailable': 'network_error',
    'invalid_response': 'invalid_response',
    'timeout': 'timeout_error',
    'rate_limit_exceeded': 'quota_exceeded',
    'model_not_found': 'model_not_found',
    'unknown_error': 'unknown_error',
  };

  return {
    type: errorTypeMap[error.type] || 'unknown_error',
    message: error.message,
    details: error.originalError?.message,
    retryable: ['service_unavailable', 'timeout', 'rate_limit_exceeded'].includes(error.type),
  };
};

/**
 * Enhanced retry logic with exponential backoff and timeout
 */
const retryWithBackoff = async <T>(fn: () => Promise<T>, config?: AIServiceConfig): Promise<T> => {
  const aiConfig = config || getAIConfig();
  for (let attempt = 1; attempt <= aiConfig.maxRetries; attempt++) {
    try {
      // Add timeout to the function call
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), aiConfig.timeout);
      });

      const result = await Promise.race([fn(), timeoutPromise]);
      return result;
    } catch (error) {
      if (attempt === aiConfig.maxRetries) {
        throw error;
      }

      const delay = aiConfig.baseDelay * Math.pow(2, attempt - 1);
      console.warn(`AI request attempt ${attempt} failed, retrying in ${delay}ms:`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw new Error('Max retries exceeded');
};

/**
 * Generate AI reflection using Local LLM with enhanced error handling
 */
const generateLocalLLMReflectionService = async (
  input: AIReflectionInput,
  config?: AIServiceConfig
): Promise<AIServiceResponse<LocalLLMResponse>> => {
  const aiConfig = config || getAIConfig();
  const startTime = Date.now();

  try {
    // Check if local LLM is available
    const isAvailable = await isLocalLLMAvailable(aiConfig);
    if (!isAvailable) {
      throw new LocalLLMError('service_unavailable', 'Local LLM service is not available');
    }

    const result = await generateLocalLLMReflection(input, aiConfig);

    // Process and validate the response using comprehensive validation
    const processedResponse = processAIResponse(result, input);

    if (!processedResponse.isValid) {
      console.warn('AI response validation failed, using fallback:', processedResponse.errors);
    }

    if (processedResponse.warnings.length > 0) {
      console.warn('AI response validation warnings:', processedResponse.warnings);
    }

    // Score the response quality for monitoring
    const qualityScore = scoreResponseQuality(processedResponse.data);
    console.log(`Local LLM response quality score: ${qualityScore}/100`);

    const processingTime = Date.now() - startTime;

    return {
      success: true,
      data: processedResponse.data,
      source: 'ai',
      processingTime,
    };
  } catch (error) {
    const processingTime = Date.now() - startTime;

    let aiError: AIError;
    if (error instanceof LocalLLMError) {
      aiError = convertLocalLLMError(error);
    } else {
      aiError = mapAIError(error);
    }

    return {
      success: false,
      error: aiError,
      source: 'ai',
      processingTime,
    };
  }
};

/**
 * Enhanced fallback mock responses with proper typing
 */
const mockReflectionTemplates: Record<string, LocalLLMResponse> = {
  joyful: {
    summary:
      "It sounds like you're having a really wonderful time and feeling grateful for the good things in your life.",
    emotion: 'joy',
    encouragement:
      "I love seeing you so happy! This positive energy you're radiating is contagious. It's beautiful how you're able to appreciate the good moments - that's such a valuable skill.",
    reflection_question:
      'What do you think helped create this joyful feeling, and how might you nurture more moments like this?',
  },
  calm: {
    summary:
      'You seem to be in a peaceful, centered place right now, which is really lovely to see.',
    emotion: 'serenity',
    encouragement:
      "There's something so grounding about the way you're describing your current state. This kind of inner calm is precious - you've clearly found a good balance.",
    reflection_question:
      'What practices or thoughts help you maintain this sense of peace when life gets busier?',
  },
  neutral: {
    summary:
      "You're taking an honest look at where you are right now, which shows real self-awareness.",
    emotion: 'contemplation',
    encouragement:
      "I appreciate how thoughtful you're being about your feelings. Sometimes the most growth happens in these quieter, more reflective moments.",
    reflection_question:
      "What's one small thing you're curious about or looking forward to in the coming days?",
  },
  sad: {
    summary:
      "It sounds like you're going through a tough time, and I can hear the weight of what you're carrying.",
    emotion: 'sadness',
    encouragement:
      "I'm really glad you're taking the time to process these feelings instead of pushing them away. That takes courage, and I want you to know that what you're feeling is completely valid.",
    reflection_question: "What's one gentle way you could show yourself some kindness today?",
  },
  anxious: {
    summary:
      "I can sense there's a lot on your mind right now, and that anxious energy sounds really overwhelming.",
    emotion: 'anxiety',
    encouragement:
      "Anxiety can feel so intense, but I'm proud of you for acknowledging it and writing about it. Sometimes just naming our worries can help take away some of their power.",
    reflection_question:
      "What's one thing that's within your control right now that might help you feel a little more grounded?",
  },
};

/**
 * Generate mock reflection with proper service response structure
 */
const generateMockReflection = (input: AIReflectionInput): AIServiceResponse<LocalLLMResponse> => {
  const template =
    mockReflectionTemplates[input.emotion as keyof typeof mockReflectionTemplates] ||
    mockReflectionTemplates.neutral;

  return {
    success: true,
    data: template,
    source: 'fallback',
    processingTime: 50, // Simulate fast fallback response
  };
};

/**
 * Enhanced AI reflection generation with proper error handling and service integration
 */
export const generateAIReflection = async (
  input: AIReflectionInput,
  config?: AIServiceConfig
): Promise<ApiResponse<AIReflectionResponse>> => {
  const aiConfig = config || getAIConfig();

  try {
    let serviceResponse: AIServiceResponse<LocalLLMResponse>;

    // Try Local LLM first
    console.log('Attempting to generate reflection using Local LLM...');
    serviceResponse = await generateLocalLLMReflectionService(input, aiConfig);

    // If Local LLM fails, fall back to mock responses
    if (!serviceResponse.success) {
      console.warn('Local LLM failed, falling back to mock responses:', serviceResponse.error);

      // Log specific error information for debugging
      if (serviceResponse.error) {
        await handleApiError(
          {
            code: serviceResponse.error.type.toUpperCase(),
            message: serviceResponse.error.message,
            details: serviceResponse.error.details,
            retryable: serviceResponse.error.retryable,
          },
          'aiReflectionService',
          'generate_reflection',
          { showToast: false } // Don't show toast for AI errors, handle gracefully
        );
      }

      // Generate fallback response
      serviceResponse = generateMockReflection(input);
      console.log('Using mock response as fallback');
    } else {
      console.log('Successfully generated reflection using Local LLM');
    }

    // Extract the AI response data
    const aiData = serviceResponse.data!;

    // Automatically extract memories from the journal entry (fire and forget)
    try {
      console.log('🧠 Automatically extracting memories from journal entry...');
      extractMemoriesFromJournalEntry({
        title: input.title,
        content: input.content,
        emotion: input.emotion,
        moodScore: input.moodScore,
      }).then((memoryResult) => {
        if (memoryResult.success && memoryResult.memories.length > 0) {
          console.log(`🧠 Automatically extracted ${memoryResult.memories.length} memories from journal entry`);
          
          // Find relevant memories based on journal content
          getRelevantMemories(input.content, {
            minImportance: 4,
            minRelevance: 0.2,
            relevanceWeight: 0.6,
            importanceWeight: 0.4,
            limit: 10,
          }).then(relevantResult => {
            if (relevantResult.success && relevantResult.data && relevantResult.data.length > 0) {
              console.log(`🧠 Found ${relevantResult.data.length} relevant memories for this journal entry`);
              console.log('🧠 Top relevant memories:', relevantResult.data.slice(0, 3).map(m => 
                `${m.key} (relevance: ${(m.relevance * 100).toFixed(0)}%, importance: ${m.importance}/10)`
              ));
            }
          }).catch(relevanceError => {
            console.warn('🧠 Relevance-based memory retrieval failed (non-critical):', relevanceError);
          });
        }
      }).catch((memoryError) => {
        console.warn('🧠 Automatic memory extraction failed (non-critical):', memoryError);
      });
    } catch (error) {
      // Memory extraction failure should not affect AI reflection
      console.warn('🧠 Automatic memory extraction error (non-critical):', error);
    }

    const reflectionResponse: AIReflectionResponse = {
      summary: aiData.summary,
      emotion: aiData.emotion,
      encouragement: aiData.encouragement,
      reflection_question: aiData.reflection_question,
      reflection: aiData.encouragement, // Legacy field for backward compatibility
      success: true,
    };

    return {
      success: true,
      data: reflectionResponse,
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    console.error('Unexpected error generating AI reflection:', error);

    // Handle unexpected errors
    await handleApiError(
      {
        code: 'UNEXPECTED_ERROR',
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
        retryable: false,
      },
      'aiReflectionService',
      'generate_reflection',
      { showToast: false }
    );

    // Return fallback response even for unexpected errors
    const fallbackResponse = generateMockReflection(input);
    const aiData = fallbackResponse.data!;

    return {
      success: true, // Still return success with fallback data
      data: {
        summary: aiData.summary,
        emotion: aiData.emotion,
        encouragement: aiData.encouragement,
        reflection_question: aiData.reflection_question,
        reflection: aiData.encouragement,
        success: true,
      },
      meta: {
        timestamp: new Date().toISOString(),
      },
    };
  }
};
