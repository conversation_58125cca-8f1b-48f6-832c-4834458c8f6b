# AmberGlow Frontend Integration Guide

This guide explains how to integrate the semantic embedding service with the AmberGlow React frontend to replace the current word-matching relevance system.

## Overview

The embedding service will replace the current `calculateRelevanceScore` function in `src/services/memoryRelevanceService.ts` with semantic similarity calculations using vector embeddings.

## Integration Steps

### 1. Create Embedding Service Client

Create a new service file for embedding API calls:

**File: `src/services/embeddingService.ts`**

```typescript
/**
 * Embedding Service Client
 * Handles communication with the local semantic embedding service
 */

import { ApiResponse } from '@/types';

export interface EmbeddingResponse {
  embedding: number[];
}

export interface EmbeddingError {
  error: string;
}

const EMBEDDING_SERVICE_URL = 'http://localhost:5005';

/**
 * Generate semantic embedding for text
 */
export const generateEmbedding = async (text: string): Promise<ApiResponse<number[]>> => {
  try {
    if (!text || text.trim().length === 0) {
      return {
        success: false,
        error: { message: 'Text cannot be empty' }
      };
    }

    const response = await fetch(`${EMBEDDING_SERVICE_URL}/embed`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text: text.trim() }),
    });

    if (!response.ok) {
      const errorData: EmbeddingError = await response.json();
      return {
        success: false,
        error: { 
          message: errorData.error || `HTTP ${response.status}: ${response.statusText}` 
        }
      };
    }

    const data: EmbeddingResponse = await response.json();
    
    if (!data.embedding || !Array.isArray(data.embedding)) {
      return {
        success: false,
        error: { message: 'Invalid embedding response format' }
      };
    }

    return {
      success: true,
      data: data.embedding
    };

  } catch (error) {
    console.error('Embedding service error:', error);
    return {
      success: false,
      error: { 
        message: error instanceof Error ? error.message : 'Unknown embedding service error' 
      }
    };
  }
};

/**
 * Check if embedding service is available
 */
export const checkEmbeddingServiceHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${EMBEDDING_SERVICE_URL}/health`, {
      method: 'GET',
      timeout: 5000
    });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Calculate cosine similarity between two embeddings
 */
export const calculateCosineSimilarity = (embedding1: number[], embedding2: number[]): number => {
  if (embedding1.length !== embedding2.length) {
    throw new Error('Embeddings must have the same dimension');
  }

  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    norm1 += embedding1[i] * embedding1[i];
    norm2 += embedding2[i] * embedding2[i];
  }

  const magnitude1 = Math.sqrt(norm1);
  const magnitude2 = Math.sqrt(norm2);

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0;
  }

  return dotProduct / (magnitude1 * magnitude2);
};
```

### 2. Update Database Schema

Add an embedding column to the memory table:

**SQL Migration:**

```sql
-- Add embedding column to memory table
ALTER TABLE memory ADD COLUMN embedding vector(384);

-- Create index for vector similarity search (if using pgvector)
-- CREATE INDEX ON memory USING ivfflat (embedding vector_cosine_ops);
```

**Alternative: Store embeddings as JSON array:**

```sql
-- If pgvector is not available, store as JSONB
ALTER TABLE memory ADD COLUMN embedding JSONB;

-- Create GIN index for JSONB
CREATE INDEX idx_memory_embedding ON memory USING gin (embedding);
```

### 3. Update Memory Persistence Service

Modify `src/services/memoryPersistenceService.ts` to generate and store embeddings:

```typescript
// Add to imports
import { generateEmbedding } from './embeddingService';

// Update addUserMemory function
export const addUserMemory = async (
  memory: UserMemory,
  source: 'manual' | 'journal_entry' | 'conversation' | 'ai_extraction' = 'manual',
  confidenceScore: number = 1.0,
  metadata: Record<string, any> = {}
): Promise<ApiResponse<UserMemory>> => {
  try {
    // ... existing code ...

    // Generate embedding for the memory
    const embeddingText = `${memory.key} ${memory.value}`;
    const embeddingResult = await generateEmbedding(embeddingText);
    
    let embedding: number[] | null = null;
    if (embeddingResult.success) {
      embedding = embeddingResult.data;
    } else {
      console.warn('Failed to generate embedding:', embeddingResult.error);
      // Continue without embedding - fallback to word matching
    }

    const insertData = {
      user_id: user.id,
      key: memory.key,
      value: memory.value,
      category: memory.category,
      importance: memoryImportance,
      embedding: embedding ? JSON.stringify(embedding) : null
    };

    // ... rest of existing code ...
  } catch (error) {
    // ... existing error handling ...
  }
};
```

### 4. Update Memory Relevance Service

Replace the word-matching algorithm with semantic similarity:

```typescript
// Update src/services/memoryRelevanceService.ts

import { generateEmbedding, calculateCosineSimilarity } from './embeddingService';

/**
 * Calculate semantic relevance score using embeddings
 */
export const calculateSemanticRelevanceScore = async (
  query: string,
  memory: UserMemory & { embedding?: string },
  context?: RelevanceContext
): Promise<number> => {
  try {
    // If no embedding stored, fall back to word matching
    if (!memory.embedding) {
      return calculateRelevanceScore(query, memory, context);
    }

    // Generate embedding for query
    const queryEmbeddingResult = await generateEmbedding(query);
    if (!queryEmbeddingResult.success) {
      console.warn('Failed to generate query embedding, falling back to word matching');
      return calculateRelevanceScore(query, memory, context);
    }

    // Parse stored embedding
    let memoryEmbedding: number[];
    try {
      memoryEmbedding = JSON.parse(memory.embedding);
    } catch {
      console.warn('Invalid stored embedding, falling back to word matching');
      return calculateRelevanceScore(query, memory, context);
    }

    // Calculate cosine similarity
    const similarity = calculateCosineSimilarity(queryEmbeddingResult.data, memoryEmbedding);
    
    // Convert similarity (-1 to 1) to relevance score (0 to 1)
    let relevance = (similarity + 1) / 2;

    // Apply category boost
    if (context?.category && memory.category === context.category) {
      relevance *= 1.25; // 25% boost for matching category
    }

    // Apply recency factor
    if (context?.recency !== undefined) {
      relevance = (relevance * 0.7) + (context.recency * 0.3);
    }

    return Math.min(1, relevance);

  } catch (error) {
    console.error('Semantic relevance calculation error:', error);
    // Fall back to word matching
    return calculateRelevanceScore(query, memory, context);
  }
};

/**
 * Updated getRelevantMemories function
 */
export const getRelevantMemories = async (
  query: string,
  options: RelevanceRetrievalOptions = {}
): Promise<ApiResponse<RelevantMemory[]>> => {
  try {
    // ... existing code to fetch memories ...

    // Calculate relevance scores using semantic similarity
    const memoriesWithRelevance = await Promise.all(
      memories.map(async (memory) => {
        const relevance = await calculateSemanticRelevanceScore(query, memory, {
          category: options.category,
          recency: includeRecency ? recency : undefined
        });
        
        return { ...memory, relevance };
      })
    );

    // ... rest of existing code ...
  } catch (error) {
    // ... existing error handling ...
  }
};
```

### 5. Background Embedding Generation

Create a service to generate embeddings for existing memories:

```typescript
// File: src/services/embeddingMigrationService.ts

/**
 * Service to generate embeddings for existing memories
 */
export const generateEmbeddingsForExistingMemories = async (): Promise<void> => {
  try {
    // Get all memories without embeddings
    const { data: memories } = await supabase
      .from('memory')
      .select('*')
      .is('embedding', null);

    if (!memories || memories.length === 0) {
      console.log('No memories need embedding generation');
      return;
    }

    console.log(`Generating embeddings for ${memories.length} memories...`);

    for (const memory of memories) {
      const embeddingText = `${memory.key} ${memory.value}`;
      const embeddingResult = await generateEmbedding(embeddingText);

      if (embeddingResult.success) {
        await supabase
          .from('memory')
          .update({ embedding: JSON.stringify(embeddingResult.data) })
          .eq('id', memory.id);
        
        console.log(`Generated embedding for memory: ${memory.key}`);
      } else {
        console.warn(`Failed to generate embedding for memory: ${memory.key}`);
      }

      // Add delay to avoid overwhelming the service
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('Embedding generation completed');
  } catch (error) {
    console.error('Embedding migration error:', error);
  }
};
```

## Testing Integration

### 1. Start the Embedding Service

```bash
cd embedding-service
python start.py
```

### 2. Test API Connection

```bash
# Test from your main project directory
curl http://localhost:5005/health
```

### 3. Test in Browser Console

```javascript
// Test embedding generation
const testEmbedding = async () => {
  const response = await fetch('http://localhost:5005/embed', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ text: 'I love hiking in the mountains' })
  });
  const data = await response.json();
  console.log('Embedding dimension:', data.embedding.length);
  console.log('Sample values:', data.embedding.slice(0, 5));
};

testEmbedding();
```

## Deployment Considerations

### Development
- Run embedding service locally on port 5005
- Ensure CORS is configured for your React dev server

### Production
- Deploy embedding service on same server or internal network
- Update EMBEDDING_SERVICE_URL to production endpoint
- Consider using environment variables for configuration
- Add proper error handling and fallbacks

### Performance
- Embedding generation: ~10-50ms per text
- Memory usage: ~500MB for the model
- Consider caching frequently requested embeddings
- Batch process existing memories during off-peak hours

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure embedding service allows your frontend origin
2. **Connection Refused**: Check if embedding service is running on port 5005
3. **Slow Performance**: Embedding service needs time to load model on first start
4. **Memory Issues**: Ensure server has at least 2GB RAM available

### Fallback Strategy

The integration includes automatic fallback to word-matching if:
- Embedding service is unavailable
- Embedding generation fails
- Stored embeddings are invalid

This ensures the memory system continues working even if the embedding service has issues.
