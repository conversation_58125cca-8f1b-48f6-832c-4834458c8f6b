/**
 * Settings Types
 * TypeScript interfaces for settings page functionality
 */

import { ApiResponse } from './api';

/**
 * User settings form data
 */
export interface UserSettingsFormData {
  /** User's display name */
  full_name: string;
  /** User's email (read-only) */
  email: string;
}

/**
 * User settings update payload (only editable fields)
 */
export interface UserSettingsUpdatePayload {
  /** User's display name */
  full_name: string;
}

/**
 * Bulk delete confirmation data
 */
export interface BulkDeleteConfirmation {
  /** Confirmation text that user must type */
  confirmationText: string;
  /** Whether user has confirmed the action */
  isConfirmed: boolean;
}

/**
 * Bulk delete operation result
 */
export interface BulkDeleteResult {
  /** Number of entries deleted */
  deletedCount: number;
  /** Whether the operation was successful */
  success: boolean;
  /** Error message if operation failed */
  error?: string;
}

/**
 * Settings page loading states
 */
export interface SettingsLoadingStates {
  /** Whether user profile is loading */
  loadingProfile: boolean;
  /** Whether profile update is in progress */
  updatingProfile: boolean;
  /** Whether bulk delete is in progress */
  deletingEntries: boolean;
}

/**
 * Settings page error states
 */
export interface SettingsErrorStates {
  /** Profile loading error */
  profileError?: string | null;
  /** Profile update error */
  updateError?: string | null;
  /** Bulk delete error */
  deleteError?: string | null;
}

/**
 * Settings form validation errors
 */
export interface SettingsFormErrors {
  /** Full name validation error */
  full_name?: string;
  /** General form error */
  general?: string;
}

/**
 * Settings page state interface
 */
export interface SettingsPageState {
  /** Form data */
  formData: UserSettingsFormData;
  /** Loading states */
  loading: SettingsLoadingStates;
  /** Error states */
  errors: SettingsErrorStates;
  /** Form validation errors */
  formErrors: SettingsFormErrors;
  /** Bulk delete confirmation state */
  bulkDeleteConfirmation: BulkDeleteConfirmation;
}

/**
 * Settings service response types
 */
export type UserSettingsUpdateResponse = ApiResponse<{
  /** Updated user profile */
  profile: UserSettingsFormData;
}>;

export type BulkDeleteResponse = ApiResponse<BulkDeleteResult>;

/**
 * Settings form field names
 */
export type SettingsFormField = keyof UserSettingsFormData;

/**
 * Settings action types for state management
 */
export type SettingsAction =
  | { type: 'SET_LOADING'; field: keyof SettingsLoadingStates; value: boolean }
  | { type: 'SET_ERROR'; field: keyof SettingsErrorStates; value: string | undefined }
  | { type: 'SET_FORM_ERROR'; field: keyof SettingsFormErrors; value: string | undefined }
  | { type: 'SET_FORM_DATA'; data: Partial<UserSettingsFormData> }
  | { type: 'SET_BULK_DELETE_CONFIRMATION'; data: Partial<BulkDeleteConfirmation> }
  | { type: 'RESET_ERRORS' }
  | { type: 'RESET_FORM' };
