/**
 * Centralized Database Service
 * Consolidates common Supabase operations, error handling, and response formatting patterns
 * Provides a consistent interface for all database operations across the application
 */

import { PostgrestError, PostgrestResponse } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

/**
 * Standard API response interface
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  meta?: {
    timestamp: string;
    pagination?: PaginationMeta;
  };
}

/**
 * API error interface
 */
export interface ApiError {
  code: string;
  message: string;
  details?: string;
  field?: string;
}

/**
 * Pagination metadata interface
 */
export interface PaginationMeta {
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Database query options
 */
export interface QueryOptions {
  /** Columns to select */
  select?: string;
  /** Filters to apply */
  filters?: Record<string, any>;
  /** Ordering */
  orderBy?: { column: string; ascending?: boolean }[];
  /** Pagination */
  pagination?: {
    page: number;
    pageSize: number;
  };
  /** Range for Supabase queries */
  range?: [number, number];
}

/**
 * Map Supabase/PostgreSQL errors to standardized API errors
 */
export const mapDatabaseError = (error: PostgrestError | Error): ApiError => {
  console.error('Database error:', error);

  // Handle PostgrestError (Supabase errors)
  if ('code' in error && 'details' in error) {
    const postgrestError = error as PostgrestError;
    
    switch (postgrestError.code) {
      case 'PGRST116':
        return {
          code: 'NOT_FOUND',
          message: 'The requested resource was not found',
          details: postgrestError.details,
        };
      
      case 'PGRST301':
        return {
          code: 'PERMISSION_DENIED',
          message: 'You do not have permission to perform this action',
          details: postgrestError.details,
        };
      
      case '23505':
        return {
          code: 'DUPLICATE_ENTRY',
          message: 'A record with this information already exists',
          details: postgrestError.details,
        };
      
      case '23503':
        return {
          code: 'FOREIGN_KEY_VIOLATION',
          message: 'This operation would violate data integrity constraints',
          details: postgrestError.details,
        };
      
      case '23514':
        return {
          code: 'CHECK_VIOLATION',
          message: 'The provided data does not meet validation requirements',
          details: postgrestError.details,
        };
      
      default:
        return {
          code: postgrestError.code || 'DATABASE_ERROR',
          message: postgrestError.message || 'A database error occurred',
          details: postgrestError.details,
        };
    }
  }

  // Handle generic errors
  if (error.message?.includes('fetch')) {
    return {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed. Please check your internet connection.',
      details: error.message,
    };
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'An unexpected error occurred',
    details: error.stack,
  };
};

/**
 * Create a standardized API response
 */
export const createApiResponse = <T>(
  data?: T,
  error?: ApiError,
  meta?: ApiResponse<T>['meta']
): ApiResponse<T> => {
  return {
    success: !error,
    data,
    error,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta,
    },
  };
};

/**
 * Create pagination metadata
 */
export const createPaginationMeta = (
  page: number,
  pageSize: number,
  totalCount: number
): PaginationMeta => {
  const totalPages = Math.ceil(totalCount / pageSize);
  
  return {
    page,
    pageSize,
    totalCount,
    totalPages,
    hasNextPage: page < totalPages - 1,
    hasPreviousPage: page > 0,
  };
};

/**
 * Execute a Supabase query with standardized error handling
 */
export const executeQuery = async <T>(
  queryPromise: Promise<PostgrestResponse<T>>
): Promise<ApiResponse<T[]>> => {
  try {
    const { data, error, count } = await queryPromise;

    if (error) {
      return createApiResponse<T[]>(undefined, mapDatabaseError(error));
    }

    return createApiResponse<T[]>(data || [], undefined, {
      pagination: count !== null ? {
        page: 0,
        pageSize: data?.length || 0,
        totalCount: count,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      } : undefined,
    });
  } catch (error) {
    return createApiResponse<T[]>(undefined, mapDatabaseError(error as Error));
  }
};

/**
 * Execute a single record query with standardized error handling
 */
export const executeSingleQuery = async <T>(
  queryPromise: Promise<PostgrestResponse<T>>
): Promise<ApiResponse<T>> => {
  try {
    const { data, error } = await queryPromise;

    if (error) {
      return createApiResponse<T>(undefined, mapDatabaseError(error));
    }

    if (!data) {
      return createApiResponse<T>(undefined, {
        code: 'NOT_FOUND',
        message: 'Record not found',
      });
    }

    return createApiResponse<T>(data);
  } catch (error) {
    return createApiResponse<T>(undefined, mapDatabaseError(error as Error));
  }
};

/**
 * Execute a paginated query with standardized error handling
 */
export const executePaginatedQuery = async <T>(
  table: string,
  options: QueryOptions = {}
): Promise<ApiResponse<T[]>> => {
  try {
    let query = supabase.from(table).select(options.select || '*', { count: 'exact' });

    // Apply filters
    if (options.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });
    }

    // Apply ordering
    if (options.orderBy) {
      options.orderBy.forEach(({ column, ascending = false }) => {
        query = query.order(column, { ascending });
      });
    }

    // Apply pagination
    if (options.range) {
      query = query.range(options.range[0], options.range[1]);
    }

    const { data, error, count } = await query;

    if (error) {
      return createApiResponse<T[]>(undefined, mapDatabaseError(error));
    }

    // Create pagination metadata
    let paginationMeta: PaginationMeta | undefined;
    if (options.pagination && count !== null) {
      paginationMeta = createPaginationMeta(
        options.pagination.page,
        options.pagination.pageSize,
        count
      );
    }

    return createApiResponse<T[]>(data || [], undefined, {
      pagination: paginationMeta,
    });
  } catch (error) {
    return createApiResponse<T[]>(undefined, mapDatabaseError(error as Error));
  }
};

/**
 * Database service class with common operations
 */
export class DatabaseService {
  /**
   * Get records from a table with optional filtering and pagination
   */
  static async getRecords<T>(
    table: string,
    options: QueryOptions = {}
  ): Promise<ApiResponse<T[]>> {
    return executePaginatedQuery<T>(table, options);
  }

  /**
   * Get a single record by ID
   */
  static async getRecordById<T>(
    table: string,
    id: string,
    select?: string
  ): Promise<ApiResponse<T>> {
    const query = supabase
      .from(table)
      .select(select || '*')
      .eq('id', id)
      .single();

    return executeSingleQuery<T>(query);
  }

  /**
   * Create a new record
   */
  static async createRecord<T>(
    table: string,
    data: Partial<T>,
    select?: string
  ): Promise<ApiResponse<T>> {
    const query = supabase
      .from(table)
      .insert(data)
      .select(select || '*')
      .single();

    return executeSingleQuery<T>(query);
  }

  /**
   * Update a record by ID
   */
  static async updateRecord<T>(
    table: string,
    id: string,
    data: Partial<T>,
    select?: string
  ): Promise<ApiResponse<T>> {
    const query = supabase
      .from(table)
      .update(data)
      .eq('id', id)
      .select(select || '*')
      .single();

    return executeSingleQuery<T>(query);
  }

  /**
   * Delete a record by ID
   */
  static async deleteRecord(
    table: string,
    id: string
  ): Promise<ApiResponse<null>> {
    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id);

      if (error) {
        return createApiResponse<null>(undefined, mapDatabaseError(error));
      }

      return createApiResponse<null>(null);
    } catch (error) {
      return createApiResponse<null>(undefined, mapDatabaseError(error as Error));
    }
  }

  /**
   * Count records in a table with optional filters
   */
  static async countRecords(
    table: string,
    filters?: Record<string, any>
  ): Promise<ApiResponse<number>> {
    try {
      let query = supabase.from(table).select('*', { count: 'exact', head: true });

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value);
          }
        });
      }

      const { count, error } = await query;

      if (error) {
        return createApiResponse<number>(undefined, mapDatabaseError(error));
      }

      return createApiResponse<number>(count || 0);
    } catch (error) {
      return createApiResponse<number>(undefined, mapDatabaseError(error as Error));
    }
  }
}
