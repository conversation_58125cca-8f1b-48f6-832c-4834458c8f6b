
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AmberGlow - Personal Journaling & Reflection</title>
    <meta name="description" content="A beautiful, secure journaling application with AI-powered reflection features to help you grow and understand yourself better." />
    <meta name="keywords" content="journaling, reflection, personal growth, AI, mindfulness, self-improvement, diary, mental health" />
    <meta name="author" content="AmberGlow Team" />
    <meta name="robots" content="index,follow" />
    <meta name="theme-color" content="#f59e0b" />

    <!-- Accessibility -->
    <meta name="color-scheme" content="light dark" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="AmberGlow" />

    <!-- Google Fonts - Inter for UI, Lora for journal entries -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lora:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="AmberGlow - Personal Journaling & Reflection" />
    <meta property="og:description" content="A beautiful, secure journaling application with AI-powered reflection features to help you grow and understand yourself better." />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="AmberGlow" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:image" content="/placeholder.svg" />
    <meta property="og:image:alt" content="AmberGlow - Personal Journaling Application" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="AmberGlow - Personal Journaling & Reflection" />
    <meta name="twitter:description" content="A beautiful, secure journaling application with AI-powered reflection features." />
    <meta name="twitter:image" content="/placeholder.svg" />
    <meta name="twitter:image:alt" content="AmberGlow - Personal Journaling Application" />
  </head>

  <body>
    <!-- Skip navigation for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:z-50 focus:px-4 focus:py-2 focus:bg-black focus:text-white focus:rounded">
      Skip to main content
    </a>

    <!-- Main application container with semantic structure -->
    <div id="root" role="application" aria-label="AmberGlow Journaling Application"></div>

    <!-- Structured data will be injected by SEO service -->

    <script type="module" src="/src/main.tsx"></script>

    <!-- Accessibility styles -->
    <style>
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }

      /* High contrast mode support */
      @media (prefers-contrast: high) {
        :root {
          --contrast-multiplier: 1.5;
        }
      }

      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* Focus indicators for keyboard navigation */
      .keyboard-navigation *:focus {
        outline: 2px solid #f59e0b;
        outline-offset: 2px;
      }
    </style>
  </body>
</html>
